/**
 * Superadmin Dashboard -Monthly ReportModal
 * 
 * This script handles theMonthly Reportmodal functionality in the superadmin dashboard.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the modal element
    const jasaKaryawanModal = document.getElementById('jasaKaryawanDetailModal');
    
    // If the modal exists, initialize it
    if (jasaKaryawanModal) {
        // Initialize the modal with Bootstrap
        const modal = new bootstrap.Modal(jasaKaryawanModal);
        
        // Add event listener to the modal when it's shown
        jasaKaryawanModal.addEventListener('shown.bs.modal', function() {
            console.log('Jasa Karyawan modal shown');
        });
    }
});
