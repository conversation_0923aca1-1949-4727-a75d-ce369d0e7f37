<?php

namespace App\Http\Controllers;

use App\Models\Site;
use App\Models\SiteInStock;
use App\Models\SiteOutStock;
use App\Models\PartInventory; 
use Carbon\Carbon;
use Illuminate\Http\Request;

class DashboardController extends Controller
{

    public function AdminHO(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'group_by' => 'nullable|in:day,week,month'
        ]);

        $groupBy = $request->input('group_by', 'day');
        $today = Carbon::now();
        $defaultEndDate = $today->toDateString(); // Tanggal hari ini

        // Jika hari ini tanggal 1, maka ambil tanggal 1 bulan sebelumnya
        if ($today->day === 1) {
            $defaultStartDate = $today->subMonth()->startOfMonth()->toDateString();
        } else {
            $defaultStartDate = $today->startOfMonth()->toDateString();
        }


        $startDate = $request->input('start_date', $defaultStartDate);
        $endDate = $request->input('end_date', $defaultEndDate);

        $sites = Site::all();
        $periods = [];
        $currentDate = Carbon::parse($startDate);
        $endDateObj = Carbon::parse($endDate);

        // Generate periods array
        while ($currentDate <= $endDateObj) {
            switch ($groupBy) {
                case 'day':
                    $periodStart = $currentDate->copy()->startOfDay();
                    $periodEnd = $currentDate->copy()->endOfDay();
                    $label = $currentDate->format('d M Y');
                    $currentDate->addDay();
                    break;

                case 'week':
                    $periodStart = $currentDate->copy()->startOfWeek();
                    $periodEnd = $currentDate->copy()->endOfWeek();
                    $periodEnd = $periodEnd->min($endDateObj);
                    $label = $periodStart->format('d M') . ' - ' . $periodEnd->format('d M');
                    $currentDate = $periodEnd->copy()->addDay();
                    break;
                case 'month':
                    $periodStart = $currentDate->copy()->startOfMonth();
                    $periodEnd = $currentDate->copy()->endOfMonth();
                    $periodEnd = $periodEnd->min($endDateObj);
                    $label = $periodStart->format('M Y');
                    $currentDate = $periodEnd->copy()->addDay();
                    break;
            }

            $periods[] = [
                'start' => $periodStart,
                'end' => $periodEnd,
                'label' => $label
            ];
        }

        $inStockChartData = [];
        $outStockChartData = [];

        foreach ($sites as $site) {
            $inStockData = [];
            $outStockData = [];

            foreach ($periods as $period) {
                // In Stock Calculation
                $inStock = SiteInStock::join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->where('part_inventories.site_id', $site->site_id)
                    ->whereBetween('date_in', [$period['start'], $period['end']])
                    ->sum('site_in_stocks.quantity');

                // Out Stock Calculation
                $outStock = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->where('part_inventories.site_id', $site->site_id)
                    ->whereBetween('date_out', [$period['start'], $period['end']])
                    ->sum('site_out_stocks.quantity');

                $inStockData[] = $inStock;
                $outStockData[] = $outStock;
            }

            $inStockChartData[] = [
                'site_name' => $site->site_name,
                'labels' => array_column($periods, 'label'),
                'data' => $inStockData
            ];

            $outStockChartData[] = [
                'site_name' => $site->site_name,
                'labels' => array_column($periods, 'label'),
                'data' => $outStockData
            ];
        }

        // Data untuk Pie Chart dan Bar Chart
        $inventoryData = $this->getInventoryStatusData();

        return view('warehouse.dashboard', compact(
            'inStockChartData',
            'outStockChartData',
            'startDate',
            'endDate',
            'groupBy',
            'inventoryData' // Kirim data inventory ke view
        ));
    }
    private function getInventoryStatusData()
    {
        $sites = Site::all();
        $inventoryData = [];

        foreach ($sites as $site) {
            $inventories = PartInventory::where('site_id', $site->site_id)->get();
            $totalInventories = $inventories->count();
            $notReadyCount = 0;
            $mediumCount = 0; // Tambahkan counter untuk status "Medium"
            $notReadyParts = [];
            $mediumParts = []; // Array untuk menyimpan part dengan status "Medium"

            foreach ($inventories as $inventory) {
                $averageStock = ($inventory->min_stock + $inventory->max_stock) / 2;

                // Logika Status
                if ($inventory->stock_quantity <= $inventory->min_stock) {
                    $status = 'danger';
                    $notReadyCount++;
                    $notReadyParts[] = [
                        'part_name' => $inventory->part->part_name,
                        'min_stock' => $inventory->min_stock,
                        'max_stock' => $inventory->max_stock,
                        'stock_quantity' => $inventory->stock_quantity,
                        'status' => $status, // Tambahkan status ke array
                    ];
                } elseif ($inventory->stock_quantity < $averageStock) {
                    $status = 'warning'; // Status "Medium"
                    $mediumCount++;
                    $mediumParts[] = [
                        'part_name' => $inventory->part->part_name,
                        'min_stock' => $inventory->min_stock,
                        'max_stock' => $inventory->max_stock,
                        'stock_quantity' => $inventory->stock_quantity,
                        'status' => $status, // Tambahkan status ke array
                    ];
                } else {
                    $status = 'ok'; // Status aman
                }
            }

            $readyCount = $totalInventories - $notReadyCount - $mediumCount; //Update Perhitungan Ready Count

            // Persentase untuk Pie Chart
            $totalNotOk = $notReadyCount + $mediumCount; // Hitung total part "tidak ok"
            $readyPercentage = ($totalInventories > 0) ? ($readyCount / $totalInventories) * 100 : 0;
            $notReadyPercentage = ($totalInventories > 0) ? ($totalNotOk / $totalInventories) * 100 : 0; // Gabungkan persentase "Not Ready" dan "Medium"

            $inventoryData[] = [
                'site_name' => $site->site_name,
                'ready_percentage' => round($readyPercentage, 2),
                'not_ready_percentage' => round($notReadyPercentage, 2),
                'not_ready_count' => $notReadyCount,
                'medium_count' => $mediumCount, // Kirim jumlah part "Medium" ke view
                'not_ready_parts' => $notReadyParts,
                'medium_parts' => $mediumParts, // Kirim daftar part "Medium" ke view
            ];
        }

        return $inventoryData;
    }

    public function index(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'group_by' => 'nullable|in:day,week,month'
        ]);

        $groupBy = $request->input('group_by', 'week');
        $defaultStartDate = Carbon::create(2024, 2, 1)->toDateString();
        $defaultEndDate = Carbon::create(2024, 2, 1)->endOfMonth()->toDateString();

        $startDate = $request->input('start_date', $defaultStartDate);
        $endDate = $request->input('end_date', $defaultEndDate);

        $sites = Site::all();
        $periods = [];
        $currentDate = Carbon::parse($startDate);
        $endDateObj = Carbon::parse($endDate);

        // Generate periods array
        while ($currentDate <= $endDateObj) {
            switch ($groupBy) {
                case 'day':
                    $periodStart = $currentDate->copy()->startOfDay();
                    $periodEnd = $currentDate->copy()->endOfDay();
                    $label = $currentDate->format('d M Y');
                    $currentDate->addDay();
                    break;

                case 'week':
                    $periodStart = $currentDate->copy()->startOfWeek();
                    $periodEnd = $currentDate->copy()->endOfWeek();
                    $periodEnd = $periodEnd->min($endDateObj);
                    $label = $periodStart->format('d M') . ' - ' . $periodEnd->format('d M');
                    $currentDate = $periodEnd->copy()->addDay();
                    break;
                case 'month':
                    $periodStart = $currentDate->copy()->startOfMonth();
                    $periodEnd = $currentDate->copy()->endOfMonth();
                    $periodEnd = $periodEnd->min($endDateObj);
                    $label = $periodStart->format('M Y');
                    $currentDate = $periodEnd->copy()->addDay();
                    break;
            }

            $periods[] = [
                'start' => $periodStart,
                'end' => $periodEnd,
                'label' => $label
            ];
        }

        $inStockChartData = [];
        $outStockChartData = [];

        foreach ($sites as $site) {
            $inStockData = [];
            $outStockData = [];

            foreach ($periods as $period) {
                // In Stock Calculation
                $inStock = SiteInStock::join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->where('part_inventories.site_id', $site->site_id)
                    ->whereBetween('date_in', [$period['start'], $period['end']])
                    ->sum('site_in_stocks.quantity');

                // Out Stock Calculation
                $outStock = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->where('part_inventories.site_id', $site->site_id)
                    ->whereBetween('date_out', [$period['start'], $period['end']])
                    ->sum('site_out_stocks.quantity');

                $inStockData[] = $inStock;
                $outStockData[] = $outStock;
            }

            $inStockChartData[] = [
                'site_name' => $site->site_name,
                'labels' => array_column($periods, 'label'),
                'data' => $inStockData
            ];

            $outStockChartData[] = [
                'site_name' => $site->site_name,
                'labels' => array_column($periods, 'label'),
                'data' => $outStockData
            ];
        }

        return view('welcome', compact(
            'inStockChartData',
            'outStockChartData',
            'startDate',
            'endDate',
            'groupBy'
        ));
    }

    public function adminsite(Request $request)
    {
        return view('sites.content');
    }
}
