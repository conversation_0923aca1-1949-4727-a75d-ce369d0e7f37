@extends('sales.contentsales')
@section('resourcesales')
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
<style>
    .w-fit-content {
        width: fit-content;
    }
    .shadow-kit {
        border: 1px ;
        border-radius: 0.5rem;
        background-color: #fff;
    }
</style>
@endsection
@section('contentsales')

<div class="bgwhite mb-2 p-2 pr-2">
    <div class="d-flex justify-content-right">
        <div class="nav-links">
            <a href="{{ route('sales.dashboard') }}" class="btn btn-sm {{ request()->routeIs('sales.dashboard') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-view-dashboard"></i> Dashboard
            </a>
            <a href="{{ route('sales.penawaran') }}" class="btn btn-sm {{ request()->routeIs('sales.penawaran') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-file-document-edit"></i> Buat Penawaran
            </a>
            <a href="{{ route('sales.customer') }}" class="btn btn-sm {{ request()->routeIs('sales.customer') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-account-group"></i> Customer
            </a>
            <a href="{{ route('sales.jasa-karyawan') }}" class="btn btn-sm {{ request()->routeIs('sales.jasa-karyawan') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-account-cash"></i> Monthly Report
            </a>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <!-- Left side: Customer Table -->
        <div class="col-md-7">
            <div class="bgwhite shadow-kit rounded-lg mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 font-bold text-uppercase text-white">Daftar Customer</h5>
                    <button type="button" id="btn-show-form" class="btn btn-sm btn-light">
                        <i class="mdi mdi-plus"></i> Tambah Customer
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered w-100" id="customer-table">
                            <thead class="bg-light">
                                <tr>
                                    <th>No</th>
                                    <th>KODE</th>
                                    <th>NAMA CUSTOMER</th>
                                    <th>ALAMAT</th>
                                    <th>TOTAL PENDING</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($customers as $index => $customer)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>{{ $customer->kode }}</td>
                                    <td>{{ $customer->nama_customer }}</td>
                                    <td>{{ $customer->alamat }}</td>
                                    <td>Rp {{ number_format($customer->total_pending, 0, ',', '.') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info btn-edit" data-id="{{ $customer->id }}">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger btn-delete" data-id="{{ $customer->id }}">
                                                <i class="mdi mdi-delete"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-center mt-3">
                        {{ $customers->links() }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Right side: Form Customer (Hidden initially) -->
        <div class="col-md-5" id="form-container">
            <div class="bgwhite shadow-kit rounded-lg mb-4 sticky-top" style="top: 10px; z-index: 100;">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 font-bold text-uppercase text-white">Form Customer</h5>
                    <div>
                        <button type="button" id="btn-reset-form" class="btn btn-sm btn-light me-1">
                            <i class="mdi mdi-refresh"></i> Reset
                        </button>
                        <button type="button" id="btn-close-form" class="btn btn-sm btn-light">
                            <i class="mdi mdi-close"></i> Tutup
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form id="customer-form">
                        <input type="hidden" id="customer-id">
                        <div class="mb-3">
                            <label for="kode" class="form-label">Kode Customer</label>
                            <input type="text" class="form-control" id="kode" name="kode" required>
                        </div>
                        <div class="mb-3">
                            <label for="nama_customer" class="form-label">Nama Customer</label>
                            <input type="text" class="form-control" id="nama_customer" name="nama_customer" required>
                        </div>
                        <div class="mb-3">
                            <label for="alamat" class="form-label">Alamat</label>
                            <textarea class="form-control" id="alamat" name="alamat" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="total_pending" class="form-label">Total Pending (Rp)</label>
                            <input type="text" class="form-control" id="total_pending" name="total_pending">
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="mdi mdi-content-save"></i> Simpan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
