/**
 * Superadmin Mobile Menu JavaScript
 * This file contains the functionality for the mobile menu in superadmin pages
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mobileMenuClose = document.getElementById('mobileMenuClose');
    const mobileMenu = document.getElementById('mobileMenu');
    const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');

    // Function to open mobile menu
    function openMobileMenu() {
        if (mobileMenu && mobileMenuOverlay) {
            mobileMenu.classList.add('active');
            mobileMenuOverlay.classList.add('active');
            document.body.style.overflow = 'hidden'; // Prevent scrolling when menu is open

            // Add a small delay to ensure the transition works properly
            setTimeout(() => {
                mobileMenu.style.right = '0';
            }, 10);
        }
    }

    // Function to close mobile menu
    function closeMobileMenu() {
        if (mobileMenu && mobileMenuOverlay) {
            mobileMenu.style.right = '-100%';
            mobileMenuOverlay.classList.remove('active');

            // Wait for the transition to complete before removing the active class
            setTimeout(() => {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = ''; // Restore scrolling
            }, 300); // Match this with the CSS transition duration
        }
    }

    // Add event listeners
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            openMobileMenu();
        });
    }

    if (mobileMenuClose) {
        mobileMenuClose.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeMobileMenu();
        });
    }

    if (mobileMenuOverlay) {
        mobileMenuOverlay.addEventListener('click', function(e) {
            e.preventDefault();
            closeMobileMenu();
        });
    }

    // Close menu when clicking on a menu item (for mobile)
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Only close if we're in mobile view
            if (window.innerWidth <= 992) {
                closeMobileMenu();
            }
        });
    });

    // Close menu when window is resized to desktop size
    window.addEventListener('resize', function() {
        if (window.innerWidth > 992) {
            // Reset styles for desktop view
            if (mobileMenu) {
                mobileMenu.style.right = '';
                mobileMenu.classList.remove('active');
                document.body.style.overflow = '';
            }
            if (mobileMenuOverlay) {
                mobileMenuOverlay.classList.remove('active');
            }
        }
    });

    // Handle touch events for better mobile experience
    let touchStartX = 0;
    let touchEndX = 0;

    document.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    }, false);

    document.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    }, false);

    function handleSwipe() {
        // Swipe left to close menu (when menu is open)
        if (mobileMenu && mobileMenu.classList.contains('active') && touchEndX < touchStartX - 50) {
            closeMobileMenu();
        }

        // Swipe right to open menu (when near the edge of the screen)
        if (mobileMenu && !mobileMenu.classList.contains('active') && touchStartX < 30 && touchEndX > touchStartX + 50) {
            openMobileMenu();
        }
    }

    console.log('Mobile menu functionality initialized');
});
