/**
 * Bootstrap Dropdown Fix for Bootstrap 5
 * This script ensures that Bootstrap 5 dropdowns work correctly in all environments
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if Bootstrap is available
    if (typeof bootstrap === 'undefined' && typeof window.bootstrap === 'undefined') {
        console.warn('Bootstrap not found, attempting to load from CDN');
        
        // Create a script element to load Bootstrap
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js';
        script.integrity = 'sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p';
        script.crossOrigin = 'anonymous';
        
        // Add the script to the document
        document.head.appendChild(script);
        
        // Wait for the script to load
        script.onload = function() {
            console.log('Bootstrap loaded from CDN');
            initializeDropdowns();
        };
    } else {
        // Bootstrap is already available
        console.log('Bootstrap already loaded');
        initializeDropdowns();
    }
    
    // Function to initialize dropdowns
    function initializeDropdowns() {
        // Make sure bootstrap is available globally
        window.bootstrap = window.bootstrap || bootstrap;
        
        // Initialize all dropdowns
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        dropdownElementList.map(function(dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
        
        // Initialize dropdown submenus
        document.querySelectorAll('.dropdown-submenu > a.dropdown-toggle').forEach(function(element) {
            element.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Close all other open submenus
                document.querySelectorAll('.dropdown-submenu .dropdown-menu').forEach(function(submenu) {
                    if (submenu !== e.target.nextElementSibling) {
                        submenu.classList.remove('show');
                        submenu.style.display = 'none';
                    }
                });
                
                // Toggle this submenu
                const submenu = e.target.nextElementSibling;
                if (submenu.classList.contains('show') || submenu.style.display === 'block') {
                    submenu.classList.remove('show');
                    submenu.style.display = 'none';
                } else {
                    submenu.classList.add('show');
                    submenu.style.display = 'block';
                }
            });
        });
        
        // Close submenus when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown-submenu')) {
                document.querySelectorAll('.dropdown-submenu .dropdown-menu').forEach(function(submenu) {
                    submenu.classList.remove('show');
                    submenu.style.display = 'none';
                });
            }
        });
    }
});
