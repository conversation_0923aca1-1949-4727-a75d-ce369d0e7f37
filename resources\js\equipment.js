import Swal from "sweetalert2";

window.loadEquipments = function () {
    loadEquipmentData();
    loadEquipmentStockData();
};

// Global variables for pagination
let currentEquipmentPage = 1;
let currentStockPage = 1;
const equipmentPerPage = 5; // 5 items per page for equipment table
const stockPerPage = 10; // 10 items per page for stock table

// Separate function to load equipment data
window.loadEquipmentData = function (page = 1) {
    currentEquipmentPage = page;
    const search = document.getElementById("search").value; // Get search term

    const url = `/equipment/equipments?search=${search}&page=${page}&per_page=${equipmentPerPage}`;

    fetch(url, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
    })
        .then((response) => response.json())
        .then((data) => {
            updateEquipmentTable(data.data);
            renderEquipmentPagination(data);
            // Reload equipment options in stock form
            loadEquipmentOptions();
        })
        .catch((error) => {
            console.error("Error loading equipment data:", error);
            Swal.fire({
                icon: "error",
                title: "Kesalahan!",
                text: "Gagal memuat data peralatan.",
            });
        });
};

// Load equipment options for stock form
window.loadEquipmentOptions = function () {
    // Use a special endpoint for getting all equipment without pagination
    fetch("/equipment/equipments?all=true", {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
    })
        .then((response) => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then((data) => {
            try {
                // Check if we have the new format (with data property) or old format (direct array)
                const equipments = data.data || data;

                const selectElement = document.getElementById("equipment_id_stock");
                if (!selectElement) {
                    console.error("Select element not found");
                    return;
                }

                selectElement.innerHTML = ""; // Clear existing options

                equipments.forEach((equipment) => {
                    const option = document.createElement("option");
                    option.value = equipment.id;
                    option.text = equipment.name;
                    selectElement.appendChild(option);
                });
            } catch (innerError) {
                console.error("Error processing equipment data:", innerError);
                throw innerError; // Re-throw to be caught by the outer catch
            }
        })
        .catch((error) => {
            console.error("Error loading equipment options:", error);
            Swal.fire({
                icon: "error",
                title: "Kesalahan!",
                text: "Gagal memuat pilihan peralatan.",
            });
        });
};

// Separate function to load equipment stock data
window.loadEquipmentStockData = function (page = 1) {
    currentStockPage = page;
    const siteId = document.getElementById("site_id").value;
    const status = document.getElementById("status").value;

    const url = `/equipment/stocks?site_id=${siteId}&status=${status}&page=${page}&per_page=${stockPerPage}`;

    fetch(url, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
    })
        .then((response) => response.json())
        .then((data) => {
            updateEquipmentStockTable(data.data);
            renderStockPagination(data);
        })
        .catch((error) => {
            console.error("Error loading equipment stock data:", error);
            Swal.fire({
                icon: "error",
                title: "Kesalahan!",
                text: "Gagal memuat data stok peralatan.",
            });
        });
};

window.applyFilters = function () {
    loadEquipmentStockData(1); // Reset to first page when filters change
};

// Function to create pagination item
function createPaginationItem(page, text, isActive = false) {
    const li = document.createElement('li');
    li.className = `page-item ${isActive ? 'active' : ''}`;

    const a = document.createElement('a');
    a.className = 'page-link';
    a.href = '#';
    a.textContent = text;
    a.dataset.page = page;

    li.appendChild(a);
    return li;
}

// Render pagination for equipment table
window.renderEquipmentPagination = function(data) {
    try {
        const container = document.getElementById('equipment-pagination');
        if (!container) {
            console.error('Pagination container not found');
            return;
        }

        container.innerHTML = '';

        if (data.last_page > 1) {
            const pagination = document.createElement('ul');
            pagination.className = 'pagination pagination-rounded  justify-content-center';

            // Previous button
            if (data.current_page > 1) {
                pagination.appendChild(createPaginationItem(data.current_page - 1, '«'));
            }

            // Page numbers
            for (let i = 1; i <= data.last_page; i++) {
                pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
            }

            // Next button
            if (data.current_page < data.last_page) {
                pagination.appendChild(createPaginationItem(data.current_page + 1, '»'));
            }

            container.appendChild(pagination);

            // Add event listeners to pagination links
            container.querySelectorAll('.page-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = parseInt(this.dataset.page);
                    loadEquipmentData(page);
                });
            });
        }
    } catch (error) {
        console.error('Error rendering equipment pagination:', error);
    }
};

// Render pagination for stock table
window.renderStockPagination = function(data) {
    try {
        const container = document.getElementById('stock-pagination');
        if (!container) {
            console.error('Stock pagination container not found');
            return;
        }

        container.innerHTML = '';

        if (data.last_page > 1) {
            const pagination = document.createElement('ul');
            pagination.className = 'pagination pagination-rounded  justify-content-center';

            // Previous button
            if (data.current_page > 1) {
                pagination.appendChild(createPaginationItem(data.current_page - 1, '«'));
            }

            // Page numbers
            for (let i = 1; i <= data.last_page; i++) {
                pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
            }

            // Next button
            if (data.current_page < data.last_page) {
                pagination.appendChild(createPaginationItem(data.current_page + 1, '»'));
            }

            container.appendChild(pagination);

            // Add event listeners to pagination links
            container.querySelectorAll('.page-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = parseInt(this.dataset.page);
                    loadEquipmentStockData(page);
                });
            });
        }
    } catch (error) {
        console.error('Error rendering stock pagination:', error);
    }
};

window.updateEquipmentTable = function (equipments) {
    const tableBody = document.getElementById("equipmentTableBody");
    tableBody.innerHTML = ""; // Clear existing rows
    let i = 1;
    equipments.forEach((equipment) => {
        const row = `
            <tr>
                <td>${i++}</td>
                <td>${equipment.name}</td>
                <td>${equipment.description || "-"}</td>
                <td>
                    <button class="btn btn-sm btn-secondary" onclick="openEditForm(${
                        equipment.id
                    })">Edit</button>
                    <button class="btn btn-sm btn-danger" onclick="confirmDeleteEquipment(${
                        equipment.id
                    })">Hapus</button>
                </td>
            </tr>
        `;
        tableBody.innerHTML += row;
    });
};

window.updateEquipmentStockTable = function (equipmentStocks) {
    const tableBody = document.getElementById("equipmentStockTableBody");
    tableBody.innerHTML = "";
    let i=1;
    equipmentStocks.forEach((stock) => {
        const row = `
            <tr>
                <td>${i++}</td>
                <td>${stock.equipment.name}</td>
                <td>${stock.site.site_name}</td>
                <td>${stock.status}</td>
                <td>${stock.quantity}</td>
                <td>${stock.received_at}</td>
                <td>
                    <button class="btn btn-sm btn-secondary" onclick="openEditEquipmentStock(${stock.id})">Edit</button>
                    <button class="btn btn-sm btn-danger" onclick="confirmDeleteEquipmentStock(${stock.id})">Hapus</button>
                </td>
            </tr>
        `;
        tableBody.innerHTML += row;
    });
};

window.openCreateForm = function () {
    document.getElementById("equipment_id").value = "";
    document.getElementById("name").value = "";
    document.getElementById("description").value = "";
    // Show the form (assuming it's hidden initially)
};

window.openEditForm = function (id) {
    try {
        // Scroll to the form to make it visible
        const formElement = document.getElementById('equipmentForm');
        if (formElement) {
            formElement.scrollIntoView({ behavior: 'smooth' });
        }

        fetch(`/equipment/${id}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
        })
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then((data) => {

                // Set form values with error checking
                const idField = document.getElementById("equipment_id");
                const nameField = document.getElementById("name");
                const descriptionField = document.getElementById("description");

                if (idField) idField.value = data.id;
                if (nameField) nameField.value = data.name;
                if (descriptionField) descriptionField.value = data.description || "";

            })
            .catch((error) => {
                console.error("Error fetching equipment data:", error);
                Swal.fire({
                    icon: "error",
                    title: "Kesalahan!",
                    text: "Gagal membuka formulir edit. Silakan coba lagi.",
                });
            });
    } catch (error) {
        console.error("Error in openEditForm function:", error);
        Swal.fire({
            icon: "error",
            title: "Kesalahan!",
            text: "Terjadi kesalahan saat membuka formulir. Silakan refresh halaman dan coba lagi.",
        });
    }
};

// Fungsi untuk membuka form edit stock
window.openEditEquipmentStock = function (id) {
    fetch(`/equipment-stock/${id}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
    })
        .then((response) => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then((data) => {
            // Scroll to the form to make it visible
            const formElement = document.getElementById('equipmentStockForm');
            if (formElement) {
                formElement.scrollIntoView({ behavior: 'smooth' });
            }

            try {
                // Set form values - with error checking for each element
                const equipmentIdElement = document.getElementById("equipment_id_stock");
                const siteIdElement = document.getElementById("site_id_stock");
                const statusElement = document.getElementById("status_stock");
                const quantityElement = document.getElementById("quantity");
                const receivedAtElement = document.getElementById("received_at_stock");
                const formElement = document.getElementById("equipmentStockForm");

                if (equipmentIdElement) equipmentIdElement.value = data.equipment_id;
                if (siteIdElement) siteIdElement.value = data.site_id;
                if (statusElement) statusElement.value = data.status;
                if (quantityElement) quantityElement.value = data.quantity || 1;

                // Format the date properly if it exists
                if (data.received_at && receivedAtElement) {
                    try {
                        // Extract just the date part in YYYY-MM-DD format
                        const dateObj = new Date(data.received_at);
                        const formattedDate = dateObj.toISOString().split('T')[0];
                        receivedAtElement.value = formattedDate;
                    } catch (dateError) {
                        console.error("Error formatting date:", dateError);
                        // Use current date as fallback
                        receivedAtElement.value = new Date().toISOString().split('T')[0];
                    }
                }

                // Set the stock ID for updating
                if (formElement) formElement.dataset.stockId = id;
            } catch (formError) {
                console.error("Error setting form values:", formError);
                throw formError; // Re-throw to be caught by the outer catch
            }
        })
        .catch((error) => {
            console.error("Error:", error);
            Swal.fire({
                icon: "error",
                title: "Kesalahan!",
                text: "Gagal membuka formulir edit stok. Silakan coba lagi.",
            });
        });
};

window.saveEquipment = function () {
    try {
        // Get form values
        const id = document.getElementById("equipment_id").value;
        const name = document.getElementById("name").value;
        const description = document.getElementById("description").value;

        // Validate required fields
        if (!name) {
            Swal.fire({
                icon: "error",
                title: "Form tidak lengkap!",
                text: "Nama peralatan harus diisi.",
            });
            return;
        }

        // Prepare data
        const data = {
            name: name,
            description: description,
        };

        // Determine if this is an update or create operation
        let updatestock = false;
        let method = "POST";
        let url = "/equipment";
        let actionText = "ditambahkan";

        if (id) {
            method = "PUT";
            updatestock = true;
            url = `/equipment/${id}`;
            actionText = "diperbarui";
        }

        // Send request to server
        fetch(url, {
            method: method,
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
            body: JSON.stringify(data),
        })
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then((responseData) => {
                // Reload data tables
                window.loadEquipmentData();
                if (updatestock) {
                    window.loadEquipmentStockData();
                }

                // Reset form
                closeForm();

                // Show success message
                Swal.fire({
                    icon: "success",
                    title: "Berhasil!",
                    text: `Peralatan berhasil ${actionText}.`,
                });
            })
            .catch((error) => {
                console.error("Error saving equipment:", error);
                Swal.fire({
                    icon: "error",
                    title: "Kesalahan!",
                    text: `Gagal ${actionText} peralatan. Silakan coba lagi.`,
                });
            });
    } catch (error) {
        console.error("Error in saveEquipment function:", error);
        Swal.fire({
            icon: "error",
            title: "Kesalahan!",
            text: "Terjadi kesalahan saat menyimpan data. Silakan refresh halaman dan coba lagi.",
        });
    }
};

window.confirmDeleteEquipment = function (id) {
    Swal.fire({
        title: "Apakah Anda yakin?",
        text: "Anda tidak akan dapat mengembalikan ini!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Ya, hapus!",
        cancelButtonText: "Batal",
    }).then((result) => {
        if (result.isConfirmed) {
            deleteEquipment(id);
        }
    });
};

window.deleteEquipment = function (id) {
    fetch(`/equipment/${id}`, {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
    })
        .then((response) => {
            if (response.ok) {
                window.loadEquipmentData(); // Only reload equipment data
                Swal.fire("Terhapus!", "Peralatan telah dihapus.", "success");
            } else {
                console.error("Error deleting equipment");
                Swal.fire({
                    icon: "error",
                    title: "Kesalahan!",
                    text: "Gagal menghapus peralatan.",
                });
            }
        })
        .catch((error) => {
            console.error("Error:", error);
            Swal.fire({
                icon: "error",
                title: "Kesalahan!",
                text: "Gagal menghapus peralatan.",
            });
        });
};

window.confirmDeleteEquipmentStock = function (id) {
    Swal.fire({
        title: "Apakah Anda yakin?",
        text: "Anda tidak akan dapat mengembalikan ini!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Ya, hapus!",
        cancelButtonText: "Batal",
    }).then((result) => {
        if (result.isConfirmed) {
            deleteEquipmentStock(id);
        }
    });
};

window.deleteEquipmentStock = function (id) {
    fetch(`/equipment-stock/${id}`, {
        // Adjust route as needed
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
    })
        .then((response) => {
            if (response.ok) {
                window.loadEquipmentStockData();
                Swal.fire(
                    "Terhapus!",
                    "Data stok peralatan telah dihapus.",
                    "success"
                );
            } else {
                console.error("Error deleting equipment stock");
                Swal.fire({
                    icon: "error",
                    title: "Kesalahan!",
                    text: "Gagal menghapus data stok peralatan.",
                });
            }
        })
        .catch((error) => {
            console.error("Error:", error);
            Swal.fire({
                icon: "error",
                title: "Kesalahan!",
                text: "Gagal menghapus data stok peralatan.",
            });
        });
};

window.cancelsave = function () {
    try {
        // Get form elements with error checking
        const equipmentIdElement = document.getElementById("equipment_id_stock");
        const siteIdElement = document.getElementById("site_id_stock");
        const quantityElement = document.getElementById("quantity");
        const statusElement = document.getElementById("status_stock");
        const receivedAtElement = document.getElementById("received_at_stock");
        const formElement = document.getElementById("equipmentStockForm");

        // Reset form fields with null checks
        if (equipmentIdElement) equipmentIdElement.selectedIndex = 0;
        if (siteIdElement) siteIdElement.selectedIndex = 0;
        if (quantityElement) quantityElement.value = '1';
        if (statusElement) statusElement.selectedIndex = 0;

        // Set today's date
        if (receivedAtElement) {
            receivedAtElement.value = new Date().toISOString().split('T')[0];
        }

        // Clear the stock ID to ensure we're creating a new record, not updating
        if (formElement) formElement.dataset.stockId = "";
    } catch (error) {
        console.error("Error in cancelsave:", error);
        // No need to show an alert here as this is just a reset function
    }
}
window.saveEquipmentStock = function () {
    try {
        // Get form elements with error checking
        const equipmentIdElement = document.getElementById("equipment_id_stock");
        const siteIdElement = document.getElementById("site_id_stock");
        const quantityElement = document.getElementById("quantity");
        const statusElement = document.getElementById("status_stock");
        const receivedAtElement = document.getElementById("received_at_stock");

        // Check if all elements exist
        if (!equipmentIdElement || !siteIdElement || !quantityElement || !statusElement || !receivedAtElement) {
            console.error("One or more form elements not found");
            Swal.fire({
                icon: "error",
                title: "Error!",
                text: "Form elements not found. Please refresh the page and try again.",
            });
            return;
        }

        // Get values
        const equipmentId = equipmentIdElement.value;
        const siteId = siteIdElement.value;
        const quantity = quantityElement.value;
        const status = statusElement.value;
        const receivedAt = receivedAtElement.value;

        // Validate required fields
        if (!equipmentId || !siteId || !quantity || !status || !receivedAt) {
            Swal.fire({
                icon: "error",
                title: "Form tidak lengkap!",
                text: "Harap isi semua field yang diperlukan.",
            });
            return;
        }

        const data = {
            equipment_id: equipmentId,
            site_id: siteId,
            quantity: quantity,
            status: status,
            received_at: receivedAt,
        };

        // Get the form element again to check for stockId
        const formElement = document.getElementById("equipmentStockForm");
        if (!formElement) {
            throw new Error("Form element not found");
        }

        const stockId = formElement.dataset.stockId;
        let method = "POST";
        let url = "/equipment-stock";

        if (stockId) {
            method = "PUT";
            url = `/equipment-stock/${stockId}`;
        }

        fetch(url, {
            method: method,
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
            body: JSON.stringify(data),
        })
            .then((response) => response.json())
            .then((data) => {
                if (data.success) {
                    // Reload data table
                    window.loadEquipmentStockData();

                    // Reset form
                    cancelsave();

                    // Show success message
                    Swal.fire({
                        icon: "success",
                        title: "Berhasil!",
                        text: "Data Stok Peralatan berhasil disimpan.",
                    });
                } else {
                    // Show error with more specific message if available
                    let errorMessage = "Data Stok Peralatan Gagal disimpan.";
                    if (data.errors) {
                        errorMessage = Object.values(data.errors).flat().join('\n');
                    } else if (data.message) {
                        errorMessage = data.message;
                    }

                    Swal.fire({
                        icon: "error",
                        title: "Harap Isi Form dengan benar!",
                        text: errorMessage,
                    });
                }
            })
            .catch((error) => {
                console.error("Error:", error);
                Swal.fire({
                    icon: "error",
                    title: "Kesalahan!",
                    text: "Gagal menyimpan data stok peralatan. Silakan coba lagi.",
                    footer: "Jika masalah berlanjut, hubungi administrator sistem."
                });
            });
    } catch (error) {
        console.error("Error in saveEquipmentStock:", error);
        Swal.fire({
            icon: "error",
            title: "Kesalahan!",
            text: "Terjadi kesalahan saat menyimpan data. Silakan refresh halaman dan coba lagi.",
        });
    }
};

window.closeForm = function () {
    try {
        // Reset all form fields
        const equipmentIdField = document.getElementById("equipment_id");
        const nameField = document.getElementById("name");
        const descriptionField = document.getElementById("description");

        if (equipmentIdField) equipmentIdField.value = "";
        if (nameField) nameField.value = "";
        if (descriptionField) descriptionField.value = "";

    } catch (error) {
        console.error("Error in closeForm function:", error);
    }
};

document.addEventListener("DOMContentLoaded", function () {
    try {
        // Load initial data
        window.loadEquipments(); // Muat data perlengkapan
        window.loadEquipmentOptions(); // Load equipment options on page load

        // Set default date on the form
        const today = new Date().toISOString().split('T')[0];
        const receivedAtElement = document.getElementById("received_at_stock");
        if (receivedAtElement) {
            receivedAtElement.value = today;
        }

        // Add event listeners for form validation
        const quantityElement = document.getElementById("quantity");
        if (quantityElement) {
            quantityElement.addEventListener("input", function(e) {
                // Ensure quantity is always at least 1
                if (parseInt(e.target.value) < 1 || !e.target.value) {
                    e.target.value = 1;
                }
            });
        }

        // Add event listener for search input
        const searchElement = document.getElementById("search");
        if (searchElement) {
            searchElement.addEventListener("keyup", function() {
                // Reset to page 1 when searching
                window.loadEquipmentData(1);
            });
        }
    } catch (error) {
        console.error("Error in DOMContentLoaded:", error);
    }
});
