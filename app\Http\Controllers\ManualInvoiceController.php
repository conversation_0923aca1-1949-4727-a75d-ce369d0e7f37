<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Helpers\LogHelper;
use Carbon\Carbon;

class ManualInvoiceController extends Controller
{
    /**
     * Store a new manual invoice without unit requirement
     */
    public function storeManualInvoice(Request $request)
    {
        try {
            // Validate the request - removed unit requirement
            $request->validate([
                'customer' => 'nullable|string|max:255',
                'site_id' => 'nullable|exists:sites,id',
                'no_invoice' => 'required|string|max:255',
                'po_number' => 'nullable|string|max:255',
                'sn' => 'nullable|string|max:255',
                'trouble' => 'nullable|string',
                'lokasi' => 'nullable|string|max:255',
                'model_unit' => 'nullable|string|max:255',
                'hmkm' => 'nullable|string|max:255',
                'tanggal_invoice' => 'required|date',
                'due_date' => 'nullable|date|after_or_equal:tanggal_invoice',
                'direct_subtotal' => 'required|numeric|min:0',
                'ppn' => 'nullable|numeric|min:0|max:100',
                'transfer_to' => 'nullable|string|max:255',
                'bank_account' => 'nullable|string|max:255',
                'bank_branch' => 'nullable|string|max:255',
                'notes' => 'nullable|string',
                'document' => 'nullable|file|max:10240|mimes:pdf,jpg,jpeg,png,doc,docx',
                'payment_status' => 'nullable|string|in:Lunas,Belum Lunas'
            ], [
                'no_invoice.required' => 'Nomor invoice harus diisi',
                'tanggal_invoice.required' => 'Tanggal invoice harus diisi',
                'direct_subtotal.required' => 'Nilai invoice harus diisi',
                'direct_subtotal.min' => 'Nilai invoice tidak boleh kurang dari 0',
                'document.max' => 'Ukuran file tidak boleh lebih dari 10MB',
                'document.mimes' => 'Format file harus PDF, JPG, JPEG, PNG, DOC, atau DOCX'
            ]);

            // Start a database transaction
            DB::beginTransaction();

            try {
                // Generate invoice number if not provided
                $invoiceNumber = $request->no_invoice;
                if (empty($invoiceNumber)) {
                    $invoiceNumber = $this->generateNextInvoiceNumber();
                }

                // Convert PPN from percentage to decimal (e.g., 11% to 0.11)
                // Make sure ppn is between 0 and 100
                $ppn = $request->ppn ?? 11;
                $ppn = max(0, min(100, $ppn)); // Clamp between 0 and 100
                $ppnDecimal = $ppn / 100;

                // Handle file upload if present
                $documentPath = null;
                if ($request->hasFile('document')) {
                    $file = $request->file('document');
                    $fileName = time() . '_' . $file->getClientOriginalName();
                    $file->move(public_path('assets/invoice_documents'), $fileName);
                    $documentPath = $fileName;
                }

                // Set due date if not provided (60 days from invoice date)
                $dueDate = $request->due_date;
                if (empty($dueDate) && !empty($request->tanggal_invoice)) {
                    $invoiceDate = Carbon::parse($request->tanggal_invoice);
                    $dueDate = $invoiceDate->copy()->addDays(60)->format('Y-m-d');
                }

                // Create new invoice
                $invoice = Invoice::create([
                    'customer' => $request->customer,
                    'site_id' => $request->site_id,
                    'location' => $request->location,
                    'no_invoice' => $invoiceNumber,
                    'po_number' => $request->po_number,
                    'sn' => $request->sn,
                    'trouble' => $request->trouble ?: 'Manual Invoice', // Default to "Manual Invoice" if empty
                    'lokasi' => $request->lokasi,
                    'model_unit' => $request->model_unit,
                    'hmkm' => $request->hmkm,
                    'tanggal_invoice' => $request->tanggal_invoice,
                    'due_date' => $dueDate,
                    'ppn' => $ppnDecimal,
                    'payment_status' => $request->payment_status ?? 'Belum Lunas',
                    'transfer_to' => $request->transfer_to,
                    'bank_account' => $request->bank_account,
                    'bank_branch' => $request->bank_branch,
                    'notes' => $request->notes,
                    'document_path' => $documentPath,
                    'direct_subtotal' => $request->direct_subtotal
                ]);

                // Commit the transaction
                DB::commit();

                $message = 'Invoice manual berhasil dibuat';
                $logAction = 'Create Manual Invoice';
                $logDescription = 'Invoice manual baru dibuat dengan nomor ' . $invoiceNumber;

                // Log the action
                LogHelper::createLog(
                    $logAction,
                    $logDescription,
                    'Invoice',
                    $request
                );

                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'invoice' => $invoice
                ]);
            } catch (\Exception $innerException) {
                // Roll back the transaction
                DB::rollBack();
                throw $innerException;
            }
        } catch (\Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal membuat invoice manual: ' . $exception->getMessage()
            ], 422);
        }
    }

    /**
     * Generate the next invoice number based on the last invoice
     */
    private function generateNextInvoiceNumber()
    {
        // Get the current month and year
        $month = Carbon::now()->format('m');
        $year = Carbon::now()->format('Y');

        // Find the latest invoice
        $latestInvoice = Invoice::orderBy('created_at', 'desc')->first();

        if ($latestInvoice) {
            // Extract the numeric part before the first slash
            $parts = explode('/', $latestInvoice->no_invoice);
            $numericPart = (int) $parts[0];

            // Increment the numeric part
            $nextNumericPart = $numericPart + 1;

            // Pad the numeric part to the same length as the original
            $originalLength = strlen($parts[0]);
            $paddedNumericPart = str_pad($nextNumericPart, $originalLength, '0', STR_PAD_LEFT);

            // Return the new invoice number with current month/year
            return sprintf('%s/INV-PWB/%s/%s', $paddedNumericPart, $month, $year);
        } else {
            // Start with 001 if no invoice exists (default format)
            return sprintf('%03d/INV-PWB/%s/%s', 1, $month, $year);
        }
    }

    /**
     * Get the latest invoice for auto-increment
     */
    public function getLatestInvoice()
    {
        try {
            $latestInvoice = Invoice::orderBy('created_at', 'desc')->first();

            return response()->json([
                'success' => true,
                'latest_invoice' => $latestInvoice
            ]);
        } catch (\Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil invoice terbaru: ' . $exception->getMessage()
            ], 500);
        }
    }
}
