import Chart from 'chart.js/auto';
import ChartDataLabels from 'chartjs-plugin-datalabels';

/**
 * Initialize category tabs for best parts section
 */
function initCategoryTabs() {
    const categoryTabs = document.querySelectorAll('.category-tab');
    if (!categoryTabs.length) return;

    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Get the category from data attribute
            const category = this.dataset.category;

            // Remove active class from all tabs
            categoryTabs.forEach(t => t.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Hide all category panes
            document.querySelectorAll('.category-pane').forEach(pane => {
                pane.style.display = 'none';
            });

            // Show the selected category pane
            const selectedPane = document.getElementById(`category-${category}`);
            if (selectedPane) {
                selectedPane.style.display = 'block';
            }
        });
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // Register Chart.js plugins
    Chart.register(ChartDataLabels);

    // Initialize category tabs
    initCategoryTabs();

    // Initialize the monthly invoice chart
    initMonthlyInvoiceChart();

    // Listen for date range change events
    document.addEventListener('dateRangeChanged', function(event) {
        console.log('Date range changed:', event.detail);

        // Reload the monthly invoice chart when date range changes
        // Note: We don't need to reload the chart here since the page will be refreshed
        // But we can use this event to update other components via AJAX if needed
    });
});

// Listen for sitesDataLoaded event
document.addEventListener('sitesDataLoaded', function(event) {
    // The sitesData is now loaded via AJAX
    console.log('Sites data loaded via AJAX');
});

/**
 * Initialize the monthly invoice chart
 */
function initMonthlyInvoiceChart() {
    // Get monthly invoice data from the page
    const monthlyInvoiceElement = document.getElementById('monthlyInvoiceData');
    if (!monthlyInvoiceElement) return;

    const monthlyInvoiceData = JSON.parse(monthlyInvoiceElement.dataset.monthlyInvoice);
    const currentYear = monthlyInvoiceElement.dataset.currentYear;

    // Get the canvas element
    const monthlyInvoiceCtx = document.getElementById('monthlyInvoiceChart');
    if (!monthlyInvoiceCtx) return;

    // Log the data to debug
    console.log('Monthly Invoice Data:', monthlyInvoiceData);

    // Ensure all months have data points (use 0 for missing data)
    // This ensures continuous lines even when there's no data for certain months
    if (monthlyInvoiceData.datasets) {
        // For each dataset, ensure all months have values
        monthlyInvoiceData.datasets.forEach(dataset => {
            // Convert data object to array with 12 months (1-indexed in PHP, so we need to adjust)
            const completeData = [];
            for (let i = 0; i < 12; i++) {
                // Use the existing value or default to 0
                // The data is already in array format from the controller
                completeData.push(dataset.data[i] !== undefined ? dataset.data[i] : 0);
            }
            // Replace the original data object with our complete array
            dataset.data = completeData;
        });
    } else if (monthlyInvoiceData.values) {
        // If using the values array directly, ensure it has 12 months
        const completeValues = [];
        for (let i = 0; i < 12; i++) {
            completeValues.push(monthlyInvoiceData.values[i] !== undefined ? monthlyInvoiceData.values[i] : 0);
        }
        monthlyInvoiceData.values = completeValues;
    }

    // Create the chart
    new Chart(monthlyInvoiceCtx.getContext('2d'), {
        type: 'line',
        data: {
            labels: monthlyInvoiceData.labels,
            datasets: monthlyInvoiceData.datasets ? monthlyInvoiceData.datasets.map(dataset => ({
                ...dataset,
                fill: false,
                tension: 0.4,
                pointRadius: 4,
                pointHoverRadius: 6,
                borderWidth: 3,
                spanGaps: false // Ensure lines are continuous
            })) : [{
                label: `Grafik Pendapatan ${currentYear}`,
                data: monthlyInvoiceData.values || [],
                backgroundColor: 'rgba(34, 82, 151, 0.1)',
                borderColor: 'rgba(34, 82, 151, 1)',
                borderWidth: 3,
                pointBackgroundColor: 'rgba(34, 82, 151, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6,
                fill: false,
                tension: 0.4,
                spanGaps: false // Ensure lines are continuous
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    top: 30,
                    bottom: 10,
                    left: 10,
                    right: 20
                }
            },
            elements: {
                line: {
                    tension: 0.4,     // Smoother curves
                    borderWidth: 3,   // Thicker lines for better visibility
                    fill: false,      // No fill under the line
                    spanGaps: false   // Connect points with line even if data is missing
                },
                point: {
                    radius: 4,        // Consistent point size
                    hoverRadius: 6,   // Slightly larger on hover
                    backgroundColor: 'rgba(34, 82, 151, 1)',
                    borderColor: '#fff',
                    borderWidth: 2
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false,
                        drawBorder: false
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + value.toLocaleString('id-ID');
                        },
                        font: {
                            size: 12
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                        drawBorder: false
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 12,
                        padding: 15,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    padding: 12,
                    titleFont: { size: 14 },
                    bodyFont: { size: 13 },
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': Rp ' + context.raw.toLocaleString('id-ID');
                        },
                        footer: function(tooltipItems) {
                            let sum = 0;
                            tooltipItems.forEach(function(tooltipItem) {
                                sum += tooltipItem.parsed.y;
                            });
                            return 'Total: Rp ' + sum.toLocaleString('id-ID');
                        }
                    }
                },
                datalabels: {
                    display: false // Disable datalabels for stacked bar chart to avoid clutter
                }
            }
        }
    });
}
