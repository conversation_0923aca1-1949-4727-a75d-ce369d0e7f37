/**
 * Compatibility script to handle CommonJS/AMD modules in browser environment
 * This script provides polyfills for 'require' and 'define' functions
 * to prevent errors when libraries using these patterns are loaded in the browser
 */

// Create a simple implementation of CommonJS require if it doesn't exist
if (typeof window.require === 'undefined') {
    window.require = function(moduleName) {
        console.warn('Polyfill require called for:', moduleName);
        
        // Map common module names to global variables
        const moduleMap = {
            'jquery': window.jQuery || window.$,
            'bootstrap': window.bootstrap,
            'moment': window.moment,
            'datatables.net': window.DataTable || window.$.fn.dataTable,
            'sweetalert2': window.Swal
        };
        
        // Return the mapped module or null
        return moduleMap[moduleName] || null;
    };
}

// Create a simple implementation of AMD define if it doesn't exist
if (typeof window.define === 'undefined') {
    window.define = function(name, deps, callback) {
        console.warn('Polyfill define called');
        
        // If the first argument is an array, it's the dependencies
        if (Array.isArray(name)) {
            callback = deps;
            deps = name;
            name = null;
        }
        
        // If the second argument is a function, there are no dependencies
        if (typeof deps === 'function') {
            callback = deps;
            deps = [];
        }
        
        // Load dependencies
        const resolvedDeps = deps.map(dep => window.require(dep));
        
        // Execute the callback with the dependencies
        const result = callback.apply(null, resolvedDeps);
        
        // If a name was provided, assign the result to window
        if (name && result) {
            const parts = name.split('.');
            let current = window;
            
            for (let i = 0; i < parts.length - 1; i++) {
                if (!current[parts[i]]) {
                    current[parts[i]] = {};
                }
                current = current[parts[i]];
            }
            
            current[parts[parts.length - 1]] = result;
        }
    };
    
    // Add amd property to define
    window.define.amd = {};
}
