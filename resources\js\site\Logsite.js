document.addEventListener('DOMContentLoaded', function() {
    // Set default date values to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('start_date').value = today;
    document.getElementById('end_date').value = today;

    // Global variables for pagination
    let currentPage = 1;
    const itemsPerPage = 10; // 10 items per page

    // Load logs on page load
    loadLogs();

    // Add event listeners for filters
    document.getElementById('start_date').addEventListener('change', function() {
        currentPage = 1; // Reset to first page when changing date filter
        loadLogs();
    });

    document.getElementById('end_date').addEventListener('change', function() {
        currentPage = 1; // Reset to first page when changing date filter
        loadLogs();
    });

    document.getElementById('action_filter').addEventListener('change', function() {
        currentPage = 1; // Reset to first page when changing action filter
        loadLogs();
    });

    document.getElementById('search_input').addEventListener('input', function() {
        currentPage = 1; // Reset to first page when searching
        loadLogs();
    });

    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;

        li.appendChild(a);
        return li;
    }

    // Function to render pagination
    function renderPagination(data) {
        const container = document.getElementById('pagination-container');
        container.innerHTML = '';

        if (data.last_page > 1) {
            const pagination = document.createElement('ul');
            pagination.className = 'pagination pagination-rounded  justify-content-center';

            // Previous button
            if (data.current_page > 1) {
                pagination.appendChild(createPaginationItem(data.current_page - 1, '\u00ab'));
            }

            // Page numbers
            for (let i = 1; i <= data.last_page; i++) {
                pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
            }

            // Next button
            if (data.current_page < data.last_page) {
                pagination.appendChild(createPaginationItem(data.current_page + 1, '\u00bb'));
            }

            container.appendChild(pagination);

            // Add event listeners to pagination links
            container.querySelectorAll('.page-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    currentPage = parseInt(this.dataset.page);
                    loadLogs();
                });
            });
        }
    }

    // Function to load logs with filters and pagination
    function loadLogs() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const action = document.getElementById('action_filter').value;
        const search = document.getElementById('search_input').value;

        let url = `/logaktivitassite/data?page=${currentPage}&per_page=${itemsPerPage}`;

        // Add filters if provided
        if (startDate && endDate) {
            url += `&start_date=${startDate}&end_date=${endDate}`;
        }

        if (action && action !== 'all') {
            url += `&action=${action}`;
        }

        if (search) {
            url += `&search=${search}`;
        }

        fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            updateLogTable(data);
            renderPagination(data);
        })
        .catch(error => {
            console.error('Error loading logs:', error);
            Swal.fire({
                icon: 'error',
                title: 'Kesalahan!',
                text: 'Gagal memuat data log aktivitas.'
            });
        });
    }

    // Function to update the log table with data
    function updateLogTable(data) {
        const tableBody = document.querySelector('#logTable tbody');
        tableBody.innerHTML = '';

        // Check if we have data
        if (!data.data || data.data.length === 0) {
            // Display a message in the table when no data is available
            const emptyRow = `
                <tr>
                    <td colspan="7" class="text-center py-3">Tidak ada data log aktivitas yang tersedia.</td>
                </tr>
            `;
            tableBody.innerHTML = emptyRow;
            return;
        }

        // If we have data, display it
        let i = (data.current_page - 1) * data.per_page + 1;
        data.data.forEach(log => {
            const row = `
                <tr>
                    <td>${i++}</td>
                    <td>${log.name || '-'}</td>
                    <td>${log.action || '-'}</td>
                    <td>${log.description || '-'}</td>
                    <td>${log.table || '-'}</td>
                    <td>${log.formatted_created_at || '-'}</td>
                </tr>
            `;
            tableBody.innerHTML += row;
        });
    }
});