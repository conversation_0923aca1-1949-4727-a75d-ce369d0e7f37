<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customersales', function (Blueprint $table) {
            $table->id();
            $table->string('kode')->unique();
            $table->string('nama_customer');
            $table->text('alamat')->nullable();
            $table->decimal('total_pending', 15, 2)->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customersales');
    }
};
