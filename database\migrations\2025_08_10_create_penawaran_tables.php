<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('penawarans', function (Blueprint $table) {
            $table->id();
            $table->string('nomor')->unique();
            $table->string('perihal');
            $table->string('customer');
            $table->string('lokasi');
            $table->text('notes')->nullable();
            $table->timestamps();
        });

        Schema::create('penawaran_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('penawaran_id')->constrained('penawarans')->onDelete('cascade');
            $table->foreignId('part_inventory_id')->constrained('part_inventories', 'part_inventory_id');
            $table->integer('quantity');
            $table->decimal('price', 15, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('penawaran_items');
        Schema::dropIfExists('penawarans');
    }
};
