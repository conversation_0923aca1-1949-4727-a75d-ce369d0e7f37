import Swal from "sweetalert2";
import bootstrap from '../bootstrap-init';

// Function to get the appropriate badge class based on status
function getStatusBadgeClass(status) {
    switch(status) {
        case 'out stock':
            return 'bg-secondary';
        case 'Proses Out':
            return 'bg-info';
        case 'Done':
            return 'bg-success';
        case 'Proses Return':
            return 'bg-warning';
        case 'Proses PO':
            return 'bg-primary';
        case 'Proses MR':
            return 'bg-danger';
        default:
            return 'bg-secondary';
    }
}

document.addEventListener("DOMContentLoaded", function () {
    const siteId = document.getElementById("site_id").value;
    const tableContainer = document.getElementById("outstock-table-container");
    const startDateInput = document.getElementById("start_date");
    const endDateInput = document.getElementById("end_date");
    const searchInput = document.getElementById("search_input");
    const partCodeInput = document.getElementById("part_code");
    const partCodeSuggestions = document.getElementById(
        "part_code_suggestions"
    );
    const unitCodeInput = document.getElementById("unit_code");
    const unitCodeSuggestions = document.getElementById("unit_code_suggestions");
    const unitIdInput = document.getElementById("unit_id");
    const createStatusSelect = document.getElementById("create_status");
    const poWoFields = document.getElementById("po_wo_fields");
    const createForm = document.getElementById("create-form");
    const totalid = document.getElementById("totalout");

    // Format price inputs on page load
    const priceInputs = document.querySelectorAll('input[id$="price"]');
    priceInputs.forEach(input => {
        if (input.value) {
            const displayId = input.id === 'price' ? 'formatted_create_price' : 'formatted_edit_price';
            formatPriceInput(input, displayId);
        }
    });

    // Set default date values to today
    const today = new Date().toISOString().split('T')[0];
    startDateInput.value = today;
    endDateInput.value = today;

    // Global variables for pagination
    let currentPage = 1;
    const itemsPerPage = 15; // 5 items per page

    function formatDate(dateString) {
        const date = new Date(dateString);
        const day = date.getDate();

        // Array of month names in Indonesian
        const monthNames = [
            'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
            'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
        ];

        const month = monthNames[date.getMonth()];
        const year = date.getFullYear();

        return `${day} ${month} ${year}`;
    }

    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;

        li.appendChild(a);
        return li;
    }

    // Function to render pagination
    function renderPagination(data) {
        const container = document.getElementById('pagination-container');
        container.innerHTML = '';

        if (data.last_page > 1) {
            const pagination = document.createElement('ul');
            pagination.className = 'pagination pagination-rounded  justify-content-center';

            // Previous button
            if (data.current_page > 1) {
                pagination.appendChild(createPaginationItem(data.current_page - 1, '«'));
            }

            // Page numbers
            for (let i = 1; i <= data.last_page; i++) {
                pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
            }

            // Next button
            if (data.current_page < data.last_page) {
                pagination.appendChild(createPaginationItem(data.current_page + 1, '»'));
            }

            container.appendChild(pagination);

            // Add event listeners to pagination links
            container.querySelectorAll('.page-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    currentPage = parseInt(this.dataset.page);
                    loadData();
                });
            });
        }
    }

    function renderTable(data) {
        // Check if we have paginated data
        if (data.data && Array.isArray(data.data)) {
            renderTableBody(data.data);

            // Use the total_quantity from API response for all matching records
            if (data.total_quantity !== undefined) {
                // Format the total quantity with 2 decimal places
                totalid.innerText = parseFloat(data.total_quantity).toFixed(2);
            }

            // Render pagination
            renderPagination(data);
        } else {
            // For non-paginated data (backward compatibility)
            renderTableBody(data);
        }
    }

    function renderTableBody(data) {
        const tbody = document.querySelector(
            "#outstock-table-container table tbody"
        );

        // Bersihkan isi tbody yang ada
        tbody.innerHTML = "";

        if (data.length === 0) {
            const row = document.createElement("tr");
            row.innerHTML = `<td colspan="9" class="text-center">Tidak ada data</td>`;
            tbody.appendChild(row);
            return;
        }

        let pageTotal = 0; // For tracking page total (not used for display)

        data.forEach((item) => {
            pageTotal += item.quantity;
            const row = document.createElement("tr");

            // Check if notes contain 'Part Digabung ke' to determine whether to show delete button
            const isPartMerged = item.notes && item.notes.includes('Part Digabung ke');
            // Check if the record is linked to a unit transaction
            const isLinkedToUnit = item.unit_transaction_parts_id !== null && item.unit_transaction_parts_id !== undefined;
            const status = item.status || "out stock";

            // Determine if we should show the edit button based on status
            const showEditButton = ['Proses Return', 'Proses PO', 'Proses MR','Proses Out','Garansi'].includes(status);

            // Prepare action buttons
            let actionButtons = '';

            if (!isPartMerged && !isLinkedToUnit) {
                // Show delete button if not merged and not linked to unit transaction
                actionButtons += `<button class="btn btn-danger btn-sm delete-btn mr-1" data-id="${item.site_out_stock_id}">Hapus</button>`;

                // Show edit button only for specific statuses
                if (showEditButton) {
                    actionButtons += `<button class="btn btn-primary btn-sm edit-status-btn"
                        data-id="${item.site_out_stock_id}"
                        data-part-code="${item.part_code}"
                        data-part-name="${item.part_name}"
                        data-quantity="${item.quantity}"
                        data-price="${item.price || 0}"
                        data-status="${status}">Edit Status</button>`;
                }
            } else if (isLinkedToUnit) {
                actionButtons = '<span class="text-muted" title="Part ini terhubung dengan transaksi unit dan tidak dapat dihapus"><i class="fas fa-ban"></i></span>';
            } else {
                actionButtons = '<span class="text-muted" title="Part ini telah digabung dan tidak dapat dihapus">Tidak dapat dihapus</span>';
            }

            // Format price as Indonesian Rupiah
            const formattedPrice = formatRupiah(item.price);

            // Handle unit code, HMKM, and LOKASI display, showing '-' if null or empty
            const unitCode = item.unit_code || '-';
            const hmkm = item.HMKM || '-';
            const lokasi = item.LOKASI || '-';
            row.innerHTML = `
                <td>${item.part_code}</td>
                <td>${item.part_name}</td>
                <td>${formatDate(item.created_at)}</td>
                <td class="text-center">${parseFloat(item.quantity).toFixed(2)}</td>
                <td>${unitCode}</td>
                <td>${hmkm}</td>
                <td>${lokasi}</td>
                <td>
                    ${status ? `<span class="badge ${getStatusBadgeClass(status)}">${status}</span>` : ''}
                </td>
                <td class="text-center">
                    <button class="btn btn-sm text-black detail-btn" data-id="${item.site_out_stock_id}">
                        <i class="mdi mdi-information-outline"></i>
                    </button>
                </td>
                <td>
                    ${actionButtons}
                </td>
            `;
            tbody.appendChild(row);
        });

        // Attach event listeners to the delete buttons
        const deleteButtons = document.querySelectorAll(".delete-btn");
        deleteButtons.forEach((button) => {
            button.addEventListener("click", function () {
                const id = this.dataset.id;
                deleteItem(id);
            });
        });

        // Attach event listeners to the edit status buttons
        const editStatusButtons = document.querySelectorAll(".edit-status-btn");
        editStatusButtons.forEach((button) => {
            button.addEventListener("click", function () {
                const id = this.dataset.id;
                const partCode = this.dataset.partCode;
                const partName = this.dataset.partName;
                const quantity = this.dataset.quantity;
                const price = this.dataset.price;
                const status = this.dataset.status;
                openEditStatusModal(id, partCode, partName, quantity, price, status);
            });
        });

        // Initialize tooltips for any elements with title attributes
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    function loadLastActivities() {
        fetch("/last-activities")
            .then((response) => response.json())
            .then((data) => {
                const ul = document.getElementById("activityList");

                if (!ul) {
                    Swal.fire({
                        icon: "error",
                        title: "Kesalahan!",
                        text: 'Element with ID "activityList" not found!',
                    });
                    return;
                }
                ul.innerHTML = "";
                data.forEach((activity) => {
                    const li = document.createElement("li");
                    const a = document.createElement("a");
                    a.href = "#";
                    a.textContent = activity.decription;
                    li.appendChild(a);
                    ul.appendChild(li);
                });
            })
            .catch((error) => {
                Swal.fire({
                    icon: "error",
                    title: "Kesalahan!",
                    text: "Gagal memuat aktivitas terakhir.",
                });
                const ul = document.getElementById("activityList");
                if (ul) {
                    ul.innerHTML = "<li>Error loading activities.</li>";
                }
            });
    }

    function loadData() {
        loadLastActivities();
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        const search = searchInput.value;

        let url = `/sites/outstock/loadData?site_id=${siteId}&page=${currentPage}&per_page=${itemsPerPage}`;

        if (startDate && endDate) {
            url += `&start_date=${startDate}&end_date=${endDate}`;
        }

        if (search) {
            url += `&search=${search}`;
        }

        fetch(url)
            .then((response) => response.json())
            .then((data) => {
                renderTable(data);
            })
            .catch((error) => {
                Swal.fire({
                    icon: "error",
                    title: "Kesalahan!",
                    text: "Gagal memuat data.",
                });
            });
    }

    function createItem(data) {
        Swal.fire({
            title: "Apakah Anda yakin ingin membuat item ini?",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, buat!",
        }).then((result) => {
            if (result.isConfirmed) {
                fetch("/sites/outstock/create", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                    },
                    body: JSON.stringify(data),
                })
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.success) {
                            Swal.fire("Berhasil!", data.success, "success");
                            currentPage = 1; // Reset to first page
                            loadData();
                        } else if (data.error) {
                            Swal.fire("Kesalahan!", data.error, "error");
                        } else if (data.errors) {
                            let errorMessages = "";
                            for (const field in data.errors) {
                                errorMessages +=
                                    data.errors[field].join("<br>") + "<br>";
                            }
                            Swal.fire({
                                icon: "error",
                                title: "Kesalahan Validasi",
                                html: errorMessages,
                            });
                        }
                    })
                    .catch((error) => {
                        Swal.fire({
                            icon: "error",
                            title: "Kesalahan!",
                            text: "Terjadi kesalahan saat membuat item.",
                        });
                    });
            }
        });
    }

    function deleteItem(data) {
        Swal.fire({
            title: "Apakah Anda yakin?",
            text: "Anda tidak akan bisa mengembalikan ini!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, hapus!",
        }).then((result) => {
            if (result.isConfirmed) {
                fetch("/sites/outstock/delete", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                    },
                    body: JSON.stringify({
                        site_out_stock_id: data,
                    }),
                })
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.success) {
                            Swal.fire("Dihapus!", data.success, "success");
                            currentPage = 1; // Reset to first page
                            loadData();
                        } else if (data.error) {
                            Swal.fire("Kesalahan!", data.error, "error");
                        }
                    })
                    .catch((error) => {
                        Swal.fire({
                            icon: "error",
                            title: "Kesalahan!",
                            text: "Terjadi kesalahan saat menghapus item.",
                        });
                    });
            }
        });
    }

    // Function to format price as Indonesian Rupiah
    function formatRupiah(price) {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
        }).format(price || 0);
    }

    // Function to format number with Indonesian thousand separators
    function formatNumber(number) {
        // Ensure we're working with a number, not a string that might be concatenated
        let numValue;

        if (typeof number === 'string') {
            // Remove any non-numeric characters except commas
            const cleanedString = String(number).replace(/[^\d,]/g, '');
            // Replace commas with empty string (we're dealing with whole numbers)
            const numericValue = cleanedString.replace(/,/g, '');
            // Convert to number
            numValue = parseInt(numericValue, 10);
        } else {
            numValue = number;
        }

        // Format with thousand separators
        return new Intl.NumberFormat('id-ID').format(numValue);
    }

    // Function to remove non-numeric characters and properly handle Indonesian number formatting
    function unformatNumber(formattedNumber) {
        // First, remove all non-numeric characters except for decimal separators
        // In Indonesian format, thousand separator is "." and decimal separator is ","
        const cleanedString = String(formattedNumber).replace(/[^\d,]/g, '');

        // Replace comma with dot for proper JavaScript number parsing (if there's a decimal part)
        // But we're dealing with whole numbers here, so we'll just remove any commas
        return cleanedString.replace(/,/g, '');
    }

    function autocomplete(input, suggestionBox) {
        const priceInput = document.getElementById("price");

        input.addEventListener("input", function () {
            const searchTerm = this.value;

            if (!searchTerm) {
                suggestionBox.innerHTML = "";
                suggestionBox.style.display = "none";
                return;
            }

            fetch(`/sites/outstock/getParts?search=${searchTerm}`)
                .then((response) => response.json())
                .then((data) => {
                    suggestionBox.innerHTML = "";
                    if (data.length > 0) {
                        const suggestionList = document.createElement("ul"); // Buat elemen ul
                        suggestionList.style.listStyleType = "none"; // Hilangkan bullet points

                        data.forEach((part) => {
                            const suggestion = document.createElement("li"); // Buat elemen li
                            const formattedPrice = formatRupiah(part.price);
                            suggestion.textContent = `${part.part_name} | Type: ${part.part_type} | Stock: ${parseFloat(part.stock_quantity).toFixed(2)} | Harga: ${formattedPrice}`;
                            suggestion.style.cursor = "pointer"; //tetap menggunakan ini untuk cursor

                            suggestion.addEventListener("click", function () {
                                input.value = part.part_code;
                                // Set the price input value with formatting
                                // Ensure price is treated as a number
                                const numericPrice = parseInt(part.price || 0, 10);
                                priceInput.value = formatNumber(numericPrice);
                                // Update the formatted display
                                const formattedPriceSpan = document.getElementById('formatted_create_price');
                                if (formattedPriceSpan) {
                                    formattedPriceSpan.textContent = formatRupiah(numericPrice);
                                }
                                suggestionBox.innerHTML = "";
                                suggestionBox.style.display = "none";
                            });

                            suggestionList.appendChild(suggestion); // Tambahkan li ke ul
                        });

                        suggestionBox.appendChild(suggestionList); // Tambahkan ul ke suggestionBox
                        suggestionBox.style.display = "block";
                    } else {
                        suggestionBox.style.display = "none";
                    }
                })
                .catch((error) => {
                    Swal.fire({
                        icon: "error",
                        title: "Kesalahan!",
                        text: "Gagal mengambil daftar parts.",
                    });
                });
        });

        document.addEventListener("click", function (event) {
            if (
                !input.contains(event.target) &&
                !suggestionBox.contains(event.target)
            ) {
                suggestionBox.innerHTML = "";
                suggestionBox.style.display = "none";
            }
        });
    }

    function unitAutocomplete(input, suggestionBox, hiddenInput) {
        input.addEventListener("input", function () {
            const searchTerm = this.value;

            if (!searchTerm) {
                suggestionBox.innerHTML = "";
                suggestionBox.style.display = "none";
                return;
            }

            fetch(`/sites/outstock/getUnits?search=${searchTerm}`)
                .then((response) => response.json())
                .then((data) => {
                    suggestionBox.innerHTML = "";
                    if (data.length > 0) {
                        const suggestionList = document.createElement("ul");
                        suggestionList.style.listStyleType = "none";

                        data.forEach((unit) => {
                            const suggestion = document.createElement("li");
                            const hmkm = unit.HMKM || '-';
                            suggestion.textContent = `${unit.unit_code} | ${unit.unit_type} | HM/KM: ${hmkm}`;
                            suggestion.style.cursor = "pointer";

                            suggestion.addEventListener("click", function () {
                                input.value = unit.unit_code;
                                hiddenInput.value = unit.id;
                                suggestionBox.innerHTML = "";
                                suggestionBox.style.display = "none";
                            });

                            suggestionList.appendChild(suggestion);
                        });

                        suggestionBox.appendChild(suggestionList);
                        suggestionBox.style.display = "block";
                    } else {
                        suggestionBox.style.display = "none";
                    }
                })
                .catch((error) => {
                    Swal.fire({
                        icon: "error",
                        title: "Kesalahan!",
                        text: "Gagal mengambil daftar unit.",
                    });
                });
        });

        document.addEventListener("click", function (event) {
            if (
                !input.contains(event.target) &&
                !suggestionBox.contains(event.target)
            ) {
                suggestionBox.innerHTML = "";
                suggestionBox.style.display = "none";
            }
        });
    }

    startDateInput.addEventListener("change", function() {
        currentPage = 1; // Reset to first page when changing date filter
        loadData();
    });
    endDateInput.addEventListener("change", function() {
        currentPage = 1; // Reset to first page when changing date filter
        loadData();
    });
    searchInput.addEventListener("input", function() {
        currentPage = 1; // Reset to first page when searching
        loadData();
    });

    if (partCodeInput && partCodeSuggestions) {
        autocomplete(partCodeInput, partCodeSuggestions);
    }

    // Initialize unit autocomplete
    if (unitCodeInput && unitCodeSuggestions && unitIdInput) {
        unitAutocomplete(unitCodeInput, unitCodeSuggestions, unitIdInput);
    }

    // Add event listener to status select to show/hide PO/WO fields
    if (createStatusSelect && poWoFields) {
        createStatusSelect.addEventListener('change', function() {
            if (this.value === 'Done') {
                poWoFields.style.display = 'block';
            } else {
                poWoFields.style.display = 'none';
            }
        });
    }

    // We'll add the event listener for the status select in the edit modal later, after the variable is defined

    // Initialize unit autocomplete for edit form
    const editUnitCodeInput = document.getElementById('edit_unit_code');
    const editUnitCodeSuggestions = document.getElementById('edit_unit_code_suggestions');
    const editUnitIdInput = document.getElementById('edit_unit_id');
    if (editUnitCodeInput && editUnitCodeSuggestions && editUnitIdInput) {
        unitAutocomplete(editUnitCodeInput, editUnitCodeSuggestions, editUnitIdInput);
    }

    createForm.addEventListener("submit", function (event) {
        event.preventDefault();

        const partCode = document.getElementById("part_code").value;
        const quantity = document.getElementById("quantity").value;
        const priceInput = document.getElementById("price");
        // Extract the numeric value from the formatted input
        const price = unformatNumber(priceInput.value);
        const dateOut = document.getElementById("date_out").value;
        const status = document.getElementById("create_status").value;
        const notes = document.getElementById("notes").value;
        const unitId = document.getElementById("unit_id").value;
        const poNumber = document.getElementById("po_number").value;
        const woNumber = document.getElementById("wo_number").value;

        if (!partCode || !quantity || !dateOut || !price) {
            Swal.fire({
                icon: "error",
                title: "Kesalahan!",
                text: "Harap isi semua kolom yang diperlukan.",
            });
            return;
        }

        // Validate quantity is a valid number
        if (isNaN(parseFloat(quantity)) || parseFloat(quantity) <= 0) {
            Swal.fire({
                icon: "error",
                title: "Kesalahan!",
                text: "Jumlah harus berupa angka positif.",
            });
            return;
        }

        // Validate PO and WO numbers if status is 'Done'
        if (status === 'Done' && (!poNumber || !woNumber)) {
            Swal.fire({
                icon: "error",
                title: "Validasi Gagal",
                text: "PO Number dan WO Number harus diisi jika status Done."
            });
            return;
        }

        const data = {
            part_code: partCode,
            quantity: quantity,
            price: price,
            date_out: dateOut,
            status: status,
            notes: notes,
            unit_id: unitId,
            po_number: poNumber,
            wo_number: woNumber
        };

        createItem(data);
    });

    const deleteButtons = document.querySelectorAll(".delete-btn");
    deleteButtons.forEach((button) => {
        button.addEventListener("click", function () {
            const id = this.dataset.id;
            deleteItem(id);
        });
    });

    loadData();

    // Edit Status Modal Functions
    const editStatusModal = document.getElementById('editStatusModal');
    const editStatusForm = document.getElementById('editStatusForm');
    const editStatusCloseButtons = document.querySelectorAll('.edit-status-close');
    const outstockIdInput = document.getElementById('outstock_id');
    const statusSelect = document.getElementById('status');

    // Add event listener to status select in edit modal
    if (statusSelect) {
        statusSelect.addEventListener('change', function() {
            const editPoWoFields = document.getElementById('edit_po_wo_fields');
            if (this.value === 'Done') {
                editPoWoFields.style.display = 'block';
            } else {
                editPoWoFields.style.display = 'none';
            }
        });
    }

    // Function to open the edit status modal
    function openEditStatusModal(id, partCode, partName, quantity, price, currentStatus) {
        // Set the values in the modal
        outstockIdInput.value = id;
        document.getElementById('edit_part_code').textContent = partCode;
        document.getElementById('edit_part_name').textContent = partName;
        document.getElementById('edit_quantity').textContent = parseFloat(quantity).toFixed(2);

        // Set the price input value with formatting
        const editPriceInput = document.getElementById('edit_price');
        // Ensure price is treated as a number
        const priceValue = parseInt(price, 10);
        editPriceInput.value = formatNumber(priceValue);
        // Update the formatted display
        const formattedPriceSpan = document.getElementById('formatted_edit_price');
        if (formattedPriceSpan) {
            formattedPriceSpan.textContent = formatRupiah(priceValue);
        }

        // Set the current status in the dropdown
        for (let i = 0; i < statusSelect.options.length; i++) {
            if (statusSelect.options[i].value === currentStatus) {
                statusSelect.selectedIndex = i;
                break;
            }
        }

        // Show/hide PO/WO fields based on status
        const editPoWoFields = document.getElementById('edit_po_wo_fields');
        if (currentStatus === 'Done' || statusSelect.value === 'Done') {
            editPoWoFields.style.display = 'block';
        } else {
            editPoWoFields.style.display = 'none';
        }

        // Fetch the current record to get unit, po_number, and wo_number
        fetch(`/sites/outstock/loadData?site_out_stock_id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.data && data.data.length > 0) {
                    const item = data.data[0];
                    console.log('Fetched item details:', item);

                    // Set unit information if available
                    const editUnitCodeInput = document.getElementById('edit_unit_code');
                    const editUnitIdInput = document.getElementById('edit_unit_id');
                    if (editUnitCodeInput && item.unit_code) {
                        editUnitCodeInput.value = item.unit_code;
                    }
                    if (editUnitIdInput) {
                        editUnitIdInput.value = item.unit_id || '';
                    }

                    // Set PO and WO numbers if available
                    const editPoNumberInput = document.getElementById('edit_po_number');
                    const editWoNumberInput = document.getElementById('edit_wo_number');
                    const editNotesInput = document.getElementById('edit_notes');
                    if (editPoNumberInput) {
                        editPoNumberInput.value = item.po_number || '';
                    }
                    if (editWoNumberInput) {
                        editWoNumberInput.value = item.wo_number || '';
                    }
                    if (editNotesInput) {
                        editNotesInput.value = item.notes || '';
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching record details:', error);
            });

        // Show the modal
        const modal = new bootstrap.Modal(editStatusModal);
        modal.show();
    }

    // Close modal when clicking on close buttons
    editStatusCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = bootstrap.Modal.getInstance(editStatusModal);
            if (modal) modal.hide();
        });
    });

    // Function to format price input and update display
    function formatPriceInput(inputElement, displayElementId) {
        // Store cursor position
        const cursorPos = inputElement.selectionStart;
        const originalLength = inputElement.value.length;

        // Get the raw numeric value
        const numericValue = unformatNumber(inputElement.value);

        // Format the value with thousand separators
        if (numericValue) {
            // Parse as integer to avoid issues with string concatenation
            // This ensures we're working with the actual number value
            const numValue = parseInt(numericValue, 10);

            // Update the formatted display
            const formattedPriceSpan = document.getElementById(displayElementId);
            if (formattedPriceSpan) {
                formattedPriceSpan.textContent = formatRupiah(numValue);
            }

            // Format the input value itself
            const formattedValue = formatNumber(numValue);
            inputElement.value = formattedValue;

            // Adjust cursor position based on how many thousand separators were added
            const newLength = inputElement.value.length;
            const lengthDiff = newLength - originalLength;
            inputElement.setSelectionRange(cursorPos + lengthDiff, cursorPos + lengthDiff);
        }
    }

    // Add event listener to update formatted price when price input changes
    const editPriceInput = document.getElementById('edit_price');
    if (editPriceInput) {
        // Format on input (as user types)
        editPriceInput.addEventListener('input', function(e) {
            formatPriceInput(this, 'formatted_edit_price');
        });

        // Format on blur (when user clicks away)
        editPriceInput.addEventListener('blur', function() {
            formatPriceInput(this, 'formatted_edit_price');
        });

        // Format on focus (when user focuses on the input)
        editPriceInput.addEventListener('focus', function() {
            formatPriceInput(this, 'formatted_edit_price');
        });

        // Format on click (when user clicks on the input)
        editPriceInput.addEventListener('click', function() {
            formatPriceInput(this, 'formatted_edit_price');
        });

        // Format on paste
        editPriceInput.addEventListener('paste', function(e) {
            // Use setTimeout to allow the paste to complete before formatting
            setTimeout(() => {
                formatPriceInput(this, 'formatted_edit_price');
            }, 0);
        });
    }

    // Add event listener to update formatted price when price input changes in the create form
    const createPriceInput = document.getElementById('price');
    if (createPriceInput) {
        // Format on input (as user types)
        createPriceInput.addEventListener('input', function(e) {
            formatPriceInput(this, 'formatted_create_price');
        });

        // Format on blur (when user clicks away)
        createPriceInput.addEventListener('blur', function() {
            formatPriceInput(this, 'formatted_create_price');
        });

        // Format on focus (when user focuses on the input)
        createPriceInput.addEventListener('focus', function() {
            formatPriceInput(this, 'formatted_create_price');
        });

        // Format on click (when user clicks on the input)
        createPriceInput.addEventListener('click', function() {
            formatPriceInput(this, 'formatted_create_price');
        });

        // Format on paste
        createPriceInput.addEventListener('paste', function(e) {
            // Use setTimeout to allow the paste to complete before formatting
            setTimeout(() => {
                formatPriceInput(this, 'formatted_create_price');
            }, 0);
        });
    }

    // Handle form submission
    editStatusForm.addEventListener('submit', function(event) {
        event.preventDefault();

        const id = outstockIdInput.value;
        const newStatus = statusSelect.value;
        const priceInput = document.getElementById('edit_price');
        // Extract the numeric value from the formatted input
        const newPrice = unformatNumber(priceInput.value);
        const unitId = document.getElementById('edit_unit_id').value;
        const poNumber = document.getElementById('edit_po_number').value;
        const woNumber = document.getElementById('edit_wo_number').value;

        // Show confirmation dialog, especially for 'Done' status
        let confirmMessage = 'Apakah Anda yakin ingin mengubah status?';

        if (newStatus === 'Done') {
            confirmMessage = 'PERHATIAN: Status "Done" tidak dapat diubah lagi setelah disimpan. Lanjutkan?';
        }

        Swal.fire({
            title: 'Konfirmasi',
            text: confirmMessage,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, Ubah Status!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                updateStatus(id, newStatus, newPrice);
            }
        });
    });

    // Detail Modal Elements
    const detailModal = document.getElementById('detailModal');
    const detailCloseButtons = document.querySelectorAll('.detail-close');

    // Function to open the detail modal
    function openDetailModal(id) {
        // Fetch the record details
        fetch(`/sites/outstock/loadData?site_out_stock_id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.data && data.data.length > 0) {
                    const item = data.data[0];

                    // Set the title in the modal header
                    document.getElementById('detail_title').textContent = `${item.part_name} (${item.part_code})`;

                    // Set the values in the modal
                    document.getElementById('detail_part_code').textContent = item.part_code || '-';
                    document.getElementById('detail_part_name').textContent = item.part_name || '-';
                    document.getElementById('detail_quantity').textContent = item.quantity ? parseFloat(item.quantity).toFixed(2) : '-';
                    document.getElementById('detail_created_at').textContent = formatDate(item.created_at) || '-';
                    // Format status with badge
                    const statusElement = document.getElementById('detail_status');
                    if (item.status) {
                        statusElement.innerHTML = `<span class="badge ${getStatusBadgeClass(item.status)}">${item.status}</span>`;
                    } else {
                        statusElement.textContent = '-';
                    }
                    document.getElementById('detail_unit').textContent = item.unit_code || '-';
                    // Make sure to display HMKM and LOKASI from unit_transactions if available
                    document.getElementById('detail_hmkm').textContent = item.HMKM || '-';
                    document.getElementById('detail_lokasi').textContent = item.LOKASI || '-';
                    document.getElementById('detail_price').textContent = formatRupiah(item.price) || '-';
                    document.getElementById('detail_po_number').textContent = item.po_number || '-';
                    document.getElementById('detail_wo_number').textContent = item.wo_number || '-';
                    document.getElementById('detail_notes').textContent = item.notes || '-';

                    // Show the modal
                    const modal = new bootstrap.Modal(detailModal);
                    modal.show();
                }
            })
            .catch(error => {
                console.error('Error fetching record details:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Kesalahan!',
                    text: 'Terjadi kesalahan saat mengambil detail record.'
                });
            });
    }

    // Close detail modal when clicking on close buttons
    detailCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = bootstrap.Modal.getInstance(detailModal);
            if (modal) modal.hide();
        });
    });

    // Add event delegation for detail buttons
    document.addEventListener('click', function(event) {
        if (event.target.closest('.detail-btn')) {
            const button = event.target.closest('.detail-btn');
            const id = button.getAttribute('data-id');
            openDetailModal(id);
        }
    });

    // Function to update the status via AJAX
    function updateStatus(id, status, price) {
        // Get unit_id, po_number, wo_number, and notes values
        const unitId = document.getElementById('edit_unit_id').value;
        const poNumber = document.getElementById('edit_po_number').value;
        const woNumber = document.getElementById('edit_wo_number').value;
        const notes = document.getElementById('edit_notes').value;

        // Validate PO and WO numbers if status is 'Done'
        if (status === 'Done' && (!poNumber || !woNumber)) {
            Swal.fire({
                icon: 'error',
                title: 'Validasi Gagal',
                text: 'PO Number dan WO Number harus diisi jika status Done.'
            });
            return;
        }

        fetch(`/sites/outstock/update-status/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                status: status,
                price: price,
                unit_id: unitId,
                po_number: poNumber,
                wo_number: woNumber,
                notes: notes
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close the modal
                const modal = bootstrap.Modal.getInstance(editStatusModal);
                if (modal) modal.hide();

                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil!',
                    text: data.message
                });

                // Reload the data
                loadData();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Kesalahan!',
                    text: data.message || 'Terjadi kesalahan saat mengubah status.'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Kesalahan!',
                text: 'Terjadi kesalahan saat menghubungi server.'
            });
        });
    }
});
