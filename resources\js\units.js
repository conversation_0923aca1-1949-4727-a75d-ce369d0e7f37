import Swal from "sweetalert2";

// Global variables for pagination
let currentPage = 1;
const itemsPerPage = 10; // 10 items per page
let selectedUnitId = null;
let partsCurrentPage = 1;

// Function to create pagination item
function createPaginationItem(page, text, isActive = false) {
    const li = document.createElement('li');
    li.className = `page-item ${isActive ? 'active' : ''}`;

    const a = document.createElement('a');
    a.className = 'page-link';
    a.href = '#';
    a.textContent = text;
    a.dataset.page = page;

    li.appendChild(a);
    return li;
}

// Function to render pagination
function renderPagination(data, containerId, loadFunction) {
    try {
        const paginationContainer = document.getElementById(containerId);
        if (!paginationContainer) return;

        paginationContainer.innerHTML = '';

        if (data.total <= data.per_page) return;

        const pagination = document.createElement('ul');
        pagination.className = 'pagination pagination-rounded  pagination-rounded justify-content-center mb-0';

        // Previous button
        if (data.current_page > 1) {
            pagination.appendChild(createPaginationItem(data.current_page - 1, '«'));
        }

        // Page numbers
        const totalPages = data.last_page;
        const currentPage = data.current_page;

        // Calculate range of pages to show
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);

        // Adjust start if we're near the end
        if (endPage - startPage < 4) {
            startPage = Math.max(1, endPage - 4);
        }

        // First page
        if (startPage > 1) {
            pagination.appendChild(createPaginationItem(1, '1'));
            if (startPage > 2) {
                const ellipsis = createPaginationItem(startPage - 1, '...');
                ellipsis.classList.add('disabled');
                pagination.appendChild(ellipsis);
            }
        }

        // Page numbers
        for (let i = startPage; i <= endPage; i++) {
            pagination.appendChild(createPaginationItem(i, i.toString(), i === currentPage));
        }

        // Last page
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsis = createPaginationItem(endPage + 1, '...');
                ellipsis.classList.add('disabled');
                pagination.appendChild(ellipsis);
            }
            pagination.appendChild(createPaginationItem(totalPages, totalPages.toString()));
        }

        // Next button
        if (data.current_page < totalPages) {
            pagination.appendChild(createPaginationItem(data.current_page + 1, '»'));
        }

        paginationContainer.appendChild(pagination);

        // Add event listeners to pagination links
        paginationContainer.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = parseInt(this.dataset.page);
                loadFunction(page);
            });
        });
    } catch (error) {
        console.error('Error rendering pagination:', error);
    }
}

// Function to load units with pagination
async function loadUnits(page = 1) {
    currentPage = page;
    const searchTerm = document.getElementById('unit-search')?.value || '';

    // Show loading indicator
    const tbody = document.getElementById('unitsTable');
    tbody.innerHTML = '<tr><td colspan="4" class="text-center">Memuat...</td></tr>';

    try {
        // Build query parameters
        const params = new URLSearchParams({
            page: page,
            per_page: itemsPerPage
        });

        // Add search parameter if provided
        if (searchTerm) {
            params.append('search', searchTerm);
        }

        const response = await fetch(`/units-list?${params}`);

        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Server returned non-JSON response. Please check server logs.');
        }

        const data = await response.json();

        // Update the search input with the current search term
        // This ensures the search term is preserved when navigating between pages
        const searchInput = document.getElementById('unit-search');
        if (searchInput && searchTerm) {
            searchInput.value = searchTerm;
        }

        if (data.data && data.data.length > 0) {
            tbody.innerHTML = data.data.map(unit => `
                <tr class="border-t">
                    <td class="px-4 py-2">${unit.unit_code}</td>
                    <td class="px-4 py-2">${unit.unit_type}</td>
                    <td class="px-4 py-2">${unit.site.site_name}</td>
                    <td class="px-4 py-2">
                        <button onclick="editUnit(${unit.id})" class="text-blue-500">Edit</button>
                        <button onclick="deleteUnit(${unit.id})" class="text-red-500 ml-2">Delete</button>
                        <button onclick="showParts(${unit.id})" class="text-green-500 ml-2">View Parts</button>
                    </td>
                </tr>
            `).join('');
        } else {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center">Tidak ada unit yang ditemukan</td></tr>';
        }

        // Render pagination
        renderPagination({
            current_page: data.current_page || 1,
            per_page: data.per_page || itemsPerPage,
            last_page: data.last_page || 1,
            total: data.total || 0
        }, 'unitsPagination', loadUnits);
    } catch (error) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center">Gagal memuat unit</td></tr>';
    }
}

// Function to load parts for a unit with pagination
async function loadParts(unitId, page = 1) {
    partsCurrentPage = page;
    selectedUnitId = unitId;

    // Show loading indicator
    const tbody = document.getElementById('partsTable');
    tbody.innerHTML = '<tr><td colspan="4" class="text-center">Memuat...</td></tr>';

    try {
        // Build query parameters
        const params = new URLSearchParams({
            page: page,
            per_page: itemsPerPage
        });

        const response = await fetch(`/units/${unitId}/parts?${params}`);

        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Server returned non-JSON response. Please check server logs.');
        }

        const data = await response.json();

        if (data.data && data.data.length > 0) {
            tbody.innerHTML = data.data.map(part => `
                <tr class="border-t" data-part-inventory-id="${part.part_inventory_id}">
                    <td class="px-4 py-2">${part.part_inventory.part.part_name} (${part.part_inventory.part.part_code})</td>
                    <td class="px-4 py-2">${part.quantity}</td>
                    <td class="px-4 py-2">${formatRupiah(part.price)}</td>
                    <td class="px-4 py-2">${part.eum}</td>
                    <td class="px-4 py-2">
                        <button onclick="editPart(${part.id})" class="text-blue-500">Edit</button>
                        <button onclick="deletePart(${part.id})" class="text-red-500 ml-2">Delete</button>
                    </td>
                </tr>
            `).join('');
        } else {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center">Tidak ada part yang ditemukan untuk unit ini</td></tr>';
        }

        // Render pagination
        renderPagination({
            current_page: data.current_page || 1,
            per_page: data.per_page || itemsPerPage,
            last_page: data.last_page || 1,
            total: data.total || 0
        }, 'partsPagination', (page) => loadParts(unitId, page));
    } catch (error) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center">Gagal memuat part</td></tr>';
    }
}

// Function to show parts section for a unit
function showParts(unitId) {
    selectedUnitId = unitId;
    const partsSection = document.getElementById('partsSection');
    partsSection.classList.remove('hidden');
    document.getElementById('partForm').dataset.unitId = unitId;

    // Get unit details to display in the title
    fetch(`/units/${unitId}`)
        .then(response => response.json())
        .then(unit => {
            // Update the title with unit information
            const title = partsSection.querySelector('h2');
            if (title) {
                title.textContent = `Parts for Unit: ${unit.unit_code} - ${unit.unit_type}`;
            }
        })
        .catch(error => {
            console.error('Error fetching unit details:', error);
        });

    loadParts(unitId, 1);
}

// Function to hide parts section
function hidePartsSection() {
    document.getElementById('partsSection').classList.add('hidden');
    resetPartForm();
}

// Function to edit a unit
async function editUnit(unitId) {
    try {
        const response = await fetch(`/units/${unitId}`);
        const unit = await response.json();

        document.getElementById('unitId').value = unit.id;
        document.getElementById('site_id').value = unit.site_id;
        document.getElementById('unit_code').value = unit.unit_code;
        document.getElementById('unit_type').value = unit.unit_type;
        document.getElementById('eum').value = unit.eum;
        document.getElementById('nopr').value = unit.nopr;
        document.getElementById('noqtn').value = unit.noqtn;
        document.getElementById('do_number').value = unit.do_number || '';
        document.getElementById('noSPB').value = unit.noSPB || '';
        document.getElementById('pekerjaan').value = unit.pekerjaan || '';
        document.getElementById('HMKM').value = unit.HMKM || '';
        document.getElementById('SHIFT').value = unit.SHIFT || '';
        document.getElementById('LOKASI').value = unit.LOKASI || '';
    } catch (error) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Gagal memuat detail unit'
        })
    }
}

// Function to delete a unit
function deleteUnit(unitId) {
    Swal.fire({
        title: 'Yakin ingin menghapus?',
        text: "Data yang dihapus tidak dapat dikembalikan!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, hapus!'
    }).then(async (result) => {
        if (result.isConfirmed) {
            try {
                const response = await fetch(`/units/${unitId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    Swal.fire(
                        'Deleted',
                        result.message,
                        'success'
                    );
                    loadUnits(currentPage);
                } else {
                    throw new Error(result.message || 'Gagal menghapus unit');
                }
            } catch (error) {
                Swal.fire(
                    'Error',
                    'Maaf Units Memiliki Part Terkait',
                    'error'
                );
            }
        }
    });
}

// Function to edit a part
async function editPart(partId) {
    try {
        const response = await fetch(`/units/parts/${partId}`);
        const part = await response.json();

        document.getElementById('partId').value = part.id;
        document.getElementById('part_inventory_id').value = part.part_inventory_id;
        document.getElementById('part_search').value = `${part.part_inventory.part.part_name} (${part.part_inventory.part.part_code})`;
        document.getElementById('quantity').value = part.quantity;
        document.getElementById('price').value = part.price;
        document.getElementById('eum').value = part.eum;
        document.getElementById('price_display').value = formatRupiah(part.price);
    } catch (error) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Gagal memuat detail unit'
        })
    }
}

// Function to delete a part
function deletePart(partId) {
    Swal.fire({
        title: 'Yakin ingin menghapus?',
        text: "Data yang dihapus tidak dapat dikembalikan!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, hapus!'
    }).then(async (result) => {
        if (result.isConfirmed) {
            try {
                const response = await fetch(`/units/parts/${partId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    Swal.fire(
                        'Deleted',
                        result.message,
                        'success'
                    );
                    loadParts(selectedUnitId, partsCurrentPage);
                } else {
                    throw new Error(result.message || 'Gagal menghapus part');
                }
            } catch (error) {
                Swal.fire(
                    'Error',
                    'Maaf Part Memiliki Part Terkait',
                    'error'
                );
            }
        }
    });
}

// Function to reset the unit form
function resetForm() {
    document.getElementById('unitForm').reset();
    document.getElementById('unitId').value = '';
}

// Function to reset the part form
function resetPartForm() {
    document.getElementById('partForm').reset();
    document.getElementById('partId').value = '';
    document.getElementById('part_inventory_id').value = '';
    document.getElementById('part_search').value = '';
    document.getElementById('price').value = '0';
    document.getElementById('eum').value = 'AE';
    document.getElementById('price_display').value = formatRupiah(0);
    document.getElementById('quantity').value = '1';
}

// Format number to Indonesian Rupiah
function formatRupiah(number) {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(number);
}

// Parse Rupiah string to number
function parseRupiah(rupiahString) {
    if (!rupiahString) return 0;
    return parseInt(rupiahString.replace(/[^,\d]/g, ''));
}

// Initialize part inventory autocomplete
function initPartInventoryAutocomplete() {
    const partSearchInput = document.getElementById('part_search');
    const partInventoryInput = document.getElementById('part_inventory_id');
    const partSuggestions = document.getElementById('part_suggestions');
    const quantityInput = document.getElementById('quantity');
    const priceInput = document.getElementById('price');
    const priceDisplay = document.getElementById('price_display');
    const eumInput = document.getElementById('eum');

    // Set default quantity to 1
    if (quantityInput && quantityInput.value === '') {
        quantityInput.value = 1;
    }

    if (!partSearchInput || !partInventoryInput || !partSuggestions) return;

    partSearchInput.addEventListener('input', debounce(async function() {
        const query = this.value.trim();
        if (query.length < 2) {
            partSuggestions.innerHTML = '';
            partSuggestions.style.display = 'none';
            return;
        }

        try {
            const response = await fetch(`/part-inventories/search?query=${query}`);
            const data = await response.json();

            if (data.length > 0) {
                partSuggestions.innerHTML = data.map(item => `
                    <div class="p-2 border-b cursor-pointer hover:bg-gray-100"
                         data-id="${item.part_inventory_id}"
                         data-name="${item.part.part_name}"
                         data-code="${item.part.part_code}"
                         data-price="${item.price || 0}">
                        ${item.part.part_name} (${item.part.part_code})
                    </div>
                `).join('');

                partSuggestions.style.display = 'block';

                // Add click event to suggestions
                partSuggestions.querySelectorAll('div').forEach(div => {
                    div.addEventListener('click', function() {
                        const partId = this.dataset.id;
                        const partName = this.dataset.name;
                        const partCode = this.dataset.code;
                        const partPrice = this.dataset.price || 0;

                        // Check if this part is already added to the unit
                        if (isPartAlreadyAdded(partId)) {
                            Swal.fire({
                                icon: 'warning',
                                title: 'Part Duplikat',
                                text: 'Part ini sudah ditambahkan ke unit.'
                            });
                            return;
                        }

                        // Set the part inventory ID and name
                        partInventoryInput.value = partId;
                        partSearchInput.value = `${partName} (${partCode})`;

                        // Set default quantity to 1 if not already set
                        if (quantityInput && (!quantityInput.value || quantityInput.value === '')) {
                            quantityInput.value = 1;
                        }

                        // Set price from part inventory (default to 0 if not available)
                        if (priceInput && priceDisplay) {
                            priceInput.value = partPrice;
                            priceDisplay.value = formatRupiah(partPrice);
                        }

                        // Set default EUM to 'AE' if not already set
                        if (eumInput && (!eumInput.value || eumInput.value === '')) {
                            eumInput.value = 'AE';
                        }

                        partSuggestions.style.display = 'none';
                    });
                });
            } else {
                partSuggestions.innerHTML = '<div class="p-2">Tidak ada part yang ditemukan</div>';
                partSuggestions.style.display = 'block';
            }
        } catch (error) {
            console.error('Error searching parts:', error);
        }
    }, 300));

    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!partSearchInput.contains(e.target) && !partSuggestions.contains(e.target)) {
            partSuggestions.style.display = 'none';
        }
    });

    // Initialize price formatter
    if (priceDisplay && priceInput) {
        priceDisplay.addEventListener('input', function() {
            // Format the input as Rupiah
            const numValue = parseRupiah(this.value);
            priceInput.value = numValue;
            this.value = formatRupiah(numValue);
        });
    }
}

// Check if a part is already added to the unit
function isPartAlreadyAdded(partInventoryId) {
    const partsTable = document.getElementById('partsTable');
    if (!partsTable) return false;

    // Get all part IDs from the table
    const partIds = Array.from(partsTable.querySelectorAll('tr')).map(row => {
        return row.dataset.partInventoryId;
    });

    return partIds.includes(partInventoryId);
}

// Debounce function to limit API calls
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

// Document ready function
document.addEventListener('DOMContentLoaded', function() {
    // Set default quantity to 1 when the page loads
    const quantityInput = document.getElementById('quantity');
    if (quantityInput) {
        quantityInput.value = 1;
    }
    // Add event listener for unit search
    const unitSearchInput = document.getElementById('unit-search');
    if (unitSearchInput) {
        unitSearchInput.addEventListener('input', debounce(function() {
            // Always reset to page 1 when searching
            loadUnits(1);
        }, 300));
    }

    // Add event listener for close parts section button
    const closePartsSectionBtn = document.getElementById('closePartsSection');
    if (closePartsSectionBtn) {
        closePartsSectionBtn.addEventListener('click', function() {
            hidePartsSection();
        });
    }

    // Initialize unit form submission
    const unitForm = document.getElementById('unitForm');
    if (unitForm) {
        unitForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const unitId = document.getElementById('unitId').value;
            const url = unitId ? `/units/${unitId}` : '/units';
            const method = unitId ? 'PUT' : 'POST';

            try {
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (!csrfToken) {
                    throw new Error('CSRF token not found. Please refresh the page.');
                }

                const response = await fetch(url, {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                    },
                    body: JSON.stringify(Object.fromEntries(formData))
                });

                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('Server returned non-JSON response. Please check server logs.');
                }

                const result = await response.json();

                if (response.ok) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: result.message
                    });
                    resetForm();
                    loadUnits(currentPage);
                } else {
                    throw new Error(result.message || 'Gagal menyimpan unit');
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Gagal menyimpan unit'
                });
            }
        });
    }

    // Initialize part form submission
    const partForm = document.getElementById('partForm');
    if (partForm) {
        partForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const partId = document.getElementById('partId').value;
            const unitId = this.dataset.unitId;

            if (!unitId) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Tidak ada unit yang dipilih'
                });
                return;
            }

            const url = partId ? `/units/parts/${partId}` : `/units/${unitId}/parts`;
            const method = partId ? 'PUT' : 'POST';

            try {
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (!csrfToken) {
                    throw new Error('CSRF token not found. Please refresh the page.');
                }

                const response = await fetch(url, {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                    },
                    body: JSON.stringify(Object.fromEntries(formData))
                });

                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('Server returned non-JSON response. Please check server logs.');
                }

                const result = await response.json();

                if (response.ok) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: result.message
                    });
                    resetPartForm();
                    loadParts(unitId, partsCurrentPage);
                } else {
                    throw new Error(result.message || 'Gagal menyimpan part');
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Gagal menyimpan part'
                });
            }
        });
    }

    // Initialize part inventory autocomplete
    initPartInventoryAutocomplete();

    // Initial load of units
    loadUnits(1);

    // Expose functions to window object for inline event handlers
    window.loadUnits = loadUnits;
    window.loadParts = loadParts;
    window.showParts = showParts;
    window.hidePartsSection = hidePartsSection;
    window.editUnit = editUnit;
    window.deleteUnit = deleteUnit;
    window.editPart = editPart;
    window.deletePart = deletePart;
    window.resetForm = resetForm;
    window.resetPartForm = resetPartForm;
});
