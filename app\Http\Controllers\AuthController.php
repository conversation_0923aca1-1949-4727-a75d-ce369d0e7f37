<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use App\Models\LogAktivitas;
use App\Models\User;


class AuthController extends Controller
{
    public function index()
    {
        return view('auth.login');
    }
    public function login(Request $request)
    {
        // Check if this is a superadmin login with code
        if ($request->has('code') && !empty($request->code)) {
            // Try superadmin login with code
            $superadminLoginResult = $this->loginSuperadminWithCode($request);

            // If it's not a redirect response, it means there was an error
            if (!($superadminLoginResult instanceof \Illuminate\Http\RedirectResponse && $superadminLoginResult->getStatusCode() == 302)) {
                // If superadmin login failed, try regular login as fallback
                return $this->attemptRegularLogin($request);
            }

            return $superadminLoginResult;
        }

        // Regular login with username and password
        return $this->attemptRegularLogin($request);
    }

    /**
     * Attempt regular login with username and password
     */
    private function attemptRegularLogin(Request $request)
    {
        $credentials = $request->only('username', 'password');

        if (Auth::attempt($credentials)) {
            $user = Auth::user();

            // For adminsite users, redirect to mode selection
            if ($user->role == 'adminsite') {
                session()->regenerate();
                session(['employee_id' => $user->employee_id]);
                session(['site_id' => $user->site_id]);
                session(['name' => $user->name]);
                session(['role' => $user->role]);

                // Log the login activity
                LogAktivitas::create([
                    'site_id' => $user->site_id,
                    'name' => $user->name,
                    'action' => 'Login',
                    'description' => "User {$user->name} berhasil login sebagai adminsite",
                    'table' => 'Users',
                    'ip_address' => $request->ip(),
                ]);

                return redirect()->route('adminsite.mode.select');
            }

            // For other users, proceed with normal login
            session()->regenerate();
            session(['employee_id' => $user->employee_id]);
            session(['site_id' => $user->site_id]);
            session(['name' => $user->name]);
            session(['role' => $user->role]);

            Log::info('User ' . $user->username . ' logged in successfully. employee_id: ' . $user->employee_id . ', site_id: ' . $user->site_id);

            // Log the login activity
            LogAktivitas::create([
                'site_id' => $user->site_id,
                'name' => $user->name,
                'action' => 'Login',
                'description' => "User {$user->name} berhasil login sebagai {$user->role}",
                'table' => 'Users',
                'ip_address' => $request->ip(),
            ]);

            if ($user->role == 'adminho') {
                return redirect()->route('adminho.dashboard');
            } elseif ($user->role == 'sales') {
                return redirect()->route('sales.dashboard');
            } else {
                Auth::logout();
                return back()->withErrors(['username' => 'Tidak dapat mengakses halaman ini']);
            }
        }

        return back()->withErrors(['username' => 'username atau password anda salah !!']);
    }

    /**
     * Handle superadmin login with code
     */
    private function loginSuperadminWithCode(Request $request)
    {
        // Validate request
        $request->validate([
            'username' => 'required',
            'code' => 'required',
        ]);

        // Check if the code is correct (hardcoded for now, you might want to store this in the database or env)
        $superadminCode = '20212021'; // Same as the PIN mentioned in requirements

        if ($request->code !== $superadminCode) {
            return back()->withErrors(['username' => 'Kode tidak valid']);
        }

        // Find the user by username
        $user = User::where('username', $request->username)->first();

        // Check if user exists
        if (!$user) {
            return back()->withErrors(['username' => 'Username tidak ditemukan']);
        }

        // If user is not a superadmin but the code is correct, automatically treat them as a superadmin
        if ($user->role !== 'superadmin') {
            // Find a superadmin user instead
            $superadmin = User::where('role', 'superadmin')->first();

            if (!$superadmin) {
                return back()->withErrors(['username' => 'Tidak ada akun superadmin yang tersedia']);
            }

            $user = $superadmin;
        }

        // Login the user
        Auth::login($user);

        // Set session variables
        session()->regenerate();
        session(['employee_id' => $user->employee_id]);
        session(['site_id' => $user->site_id]);
        session(['name' => $user->name]);
        session(['role' => $user->role]);

        // Log the login activity
        LogAktivitas::create([
            'site_id' => $user->site_id,
            'name' => $user->name,
            'action' => 'Login',
            'description' => "Superadmin {$user->name} berhasil login dengan kode",
            'table' => 'Users',
            'ip_address' => $request->ip(),
        ]);

        return redirect()->route('superadmin.dashboard');
    }

    public function logout(Request $request) // Add Request to logout
    {
        // Log the logout activity before logging out
        if (Auth::check()) {
            $user = Auth::user();
            LogAktivitas::create([
                'site_id' => $user->site_id,
                'name' => $user->name,
                'action' => 'Logout',
                'description' => "User {$user->name} berhasil logout",
                'table' => 'Users',
                'ip_address' => $request->ip(),
            ]);
        }

        Auth::logout();
        session()->invalidate(); // Invalidate session
        session()->regenerateToken(); // Regenerate token
        return redirect('/login')->withErrors(['username' => 'Berhasil Logout']);

    }
}
