/**
 * Superadmin Dashboard Sites Data
 * Handles loading sites data via AJAX for the dashboard
 */

document.addEventListener('DOMContentLoaded', function() {
    // Load sites data via AJAX
    loadSitesData();
});

/**
 * Load sites data via AJAX
 */
function loadSitesData() {
    // Show skeleton loader
    const contentWrapper = document.querySelector('.content-wrapper');
    const skeletonLoader = document.getElementById('skeleton-loader');

    if (contentWrapper && skeletonLoader) {
        contentWrapper.style.display = 'none';
        skeletonLoader.style.display = 'block';
    }

    // Get the date range and filters
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const divisionFilter = document.getElementById('division-filter');
    const siteFilter = document.getElementById('site-filter');

    const startDate = startDateInput ? startDateInput.value : '';
    const endDate = endDateInput ? endDateInput.value : '';
    const selectedDivision = divisionFilter ? divisionFilter.value : '';
    const selectedSite = siteFilter ? siteFilter.value : '';

    // Build query parameters
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (selectedDivision) params.append('division', selectedDivision);
    if (selectedSite) params.append('site', selectedSite);

    // Log the request parameters for debugging
    console.log('Fetching sites data with params:', params.toString());

    // Fetch sites data via AJAX
    fetch(`/superadmin/sites-data?${params.toString()}`)
        .then(response => {
            if (!response.ok) {
                console.error('Server response error:', response.status, response.statusText);
                return response.json().then(errorData => {
                    throw new Error(errorData.error || 'Network response was not ok');
                }).catch(e => {
                    if (e instanceof SyntaxError) {
                        // If the response is not valid JSON
                        throw new Error(`Server error: ${response.status} ${response.statusText}`);
                    }
                    throw e;
                });
            }
            return response.json();
        })
        .then(data => {
            // Check if the response contains an error
            if (data.error) {
                throw new Error(data.error);
            }

            console.log('Sites data loaded successfully');

            // Store the data in the sitesData element
            const sitesDataElement = document.getElementById('sitesData');
            if (sitesDataElement) {
                sitesDataElement.setAttribute('data-sites-data', JSON.stringify(data));

                // Dispatch a custom event to notify other scripts that the data is loaded
                const event = new CustomEvent('sitesDataLoaded', { detail: data });
                document.dispatchEvent(event);
            }

            // Hide skeleton loader, show content
            if (contentWrapper && skeletonLoader) {
                skeletonLoader.style.display = 'none';
                contentWrapper.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error loading sites data:', error);

            // Show detailed error message
            if (contentWrapper) {
                contentWrapper.innerHTML = `
                    <div class="container-fluid py-4">
                        <div class="alert alert-danger">
                            <i class="mdi mdi-alert-circle-outline me-2"></i>
                            Gagal memuat data: ${error.message}
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-danger" onclick="loadSitesData()">
                                    <i class="mdi mdi-refresh me-1"></i> Coba Lagi
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                contentWrapper.style.display = 'block';
            }

            // Hide skeleton loader
            if (skeletonLoader) {
                skeletonLoader.style.display = 'none';
            }
        });
}

// Listen for date range change events
document.addEventListener('dateRangeChanged', function() {
    // Reload sites data when date range changes
    loadSitesData();
});

// Listen for filter change events
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to filter elements
    const divisionFilter = document.getElementById('division-filter');
    const siteFilter = document.getElementById('site-filter');
    const searchBtn = document.getElementById('searchBtn');

    if (searchBtn) {
        // The search button is handled by the form submission
        // No need to add event listener here
    }
});
