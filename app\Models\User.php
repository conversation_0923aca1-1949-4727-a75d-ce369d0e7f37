<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    use  HasFactory, Notifiable, HasUuids;

    protected $table = 'users';
    protected $primaryKey = 'employee_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'employee_id',
        'site_id',
        'username',
        'password',
        'email',
        'role',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
    ];
    public static function getRoles(): array
    {
        return ['adminho', 'adminsite', 'sales','superadmin', 'karyawan'];
    }
    public function site()
    {
        return $this->belongsTo(Site::class, 'site_id', 'site_id');
    }

    public function warehouseInStocks()
    {
        return $this->hasMany(WarehouseInStock::class, 'employee_id', 'employee_id');
    }

    public function warehouseOutStocks()
    {
        return $this->hasMany(WarehouseOutStock::class, 'employee_id', 'employee_id');
    }

    public function siteInStocks()
    {
        return $this->hasMany(SiteInStock::class, 'employee_id', 'employee_id');
    }

    public function siteOutStocks()
    {
        return $this->hasMany(SiteOutStock::class, 'employee_id', 'employee_id');
    }

    public function returnStocks()
    {
        return $this->hasMany(ReturnStock::class, 'employee_id', 'employee_id');
    }

    public function partTrackingsRequestor()
    {
        return $this->hasMany(PartTracking::class, 'requestor_id', 'employee_id');
    }

    public function partTrackingsReceiver()
    {
        return $this->hasMany(PartTracking::class, 'receiver_id', 'employee_id');
    }

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (empty($model->employee_id)) {
                $model->employee_id = Str::uuid()->toString();
            }
        });
    }
}
