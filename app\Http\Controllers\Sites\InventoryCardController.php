<?php

namespace App\Http\Controllers\Sites;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PartInventory;
use App\Models\Site;
use App\Models\Part;
use Illuminate\View\View;

class InventoryCardController extends Controller
{
    public function __construct()
    {
        // No middleware check, we'll handle it in each method
    }

    public function index(Request $request): View
    {
        // Check if user is logged in and has a site_id
        if (!session('site_id') || !session('role')) {
            return redirect('/login')->withErrors(['username' => 'Silakan login terlebih dahulu']);
        }

        $siteId = session('site_id');
        $site = Site::findOrFail($siteId);

        $parts = PartInventory::with(['part', 'site'])
            ->where('site_id', $siteId)
            ->orderBy('updated_at', 'desc')
            ->paginate(15);

        return view('sites.inventorycard', compact('site', 'parts'));
    }

    public function getInventoryData(Request $request)
    {
        // Check if user is logged in and has a site_id
        if (!session('site_id') || !session('role')) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $siteId = session('site_id');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15);
        $searchTerm = $request->input('search', '');
        $partType = $request->input('part_type', '');

        $query = PartInventory::with(['part', 'site'])
            ->where('site_id', $siteId);

        // Apply search filter if provided
        if (!empty($searchTerm)) {
            $query->where(function($q) use ($searchTerm) {
                $q->whereHas('part', function($subQ) use ($searchTerm) {
                    $subQ->where('part_code', 'LIKE', "%{$searchTerm}%")
                        ->orWhere('part_name', 'LIKE', "%{$searchTerm}%");
                })
                ->orWhere('site_part_name', 'LIKE', "%{$searchTerm}%");
            });
        }

        // Apply part type filter if provided
        if (!empty($partType)) {
            $query->whereHas('part', function($q) use ($partType) {
                $q->where('part_type', $partType);
            });
        }

        // Get total count for pagination
        $total = $query->count();

        // Get paginated data
        $inventories = $query->skip(($page - 1) * $perPage)
                            ->take($perPage)
                            ->get();

        $data = [];
        foreach ($inventories as $inventory) {
            $status = '-';

            // Determine status based on stock levels
            if ($inventory->min_stock > 0) {
                if ($inventory->stock_quantity <= $inventory->min_stock) {
                    $status = 'Not Ready';
                } else {
                    $status = 'Ready';
                }
            }

            $itemData = [
                'part_code' => $inventory->part->part_code,
                'part_name' => $inventory->site_part_name ?? $inventory->part->part_name,
                'stock_quantity' => $inventory->stock_quantity,
                'min_stock' => $inventory->min_stock,
                'max_stock' => $inventory->max_stock,
                'price' => $inventory->price ?? 0,
                'status' => $status
            ];

            // Add itemcode for IMK site
            if ($siteId === 'IMK') {
                // For IMK site, use the site_part_name as a reference to find the item code
                // This is a placeholder - in a real implementation, you would need to retrieve the actual item code
                $itemData['itemcode'] = $inventory->item_code;
            }

            $data[] = $itemData;
        }

        return response()->json([
            'data' => $data,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    public function getPartTypes()
    {
        // Check if user is logged in and has a site_id
        if (!session('site_id') || !session('role')) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $partTypes = Part::PART_TYPES;
        return response()->json(['part_types' => $partTypes]);
    }
}
