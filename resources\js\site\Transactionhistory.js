import Swal from 'sweetalert2';

document.addEventListener('DOMContentLoaded', function() {
    const statusFilter = document.getElementById('status_filter');
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    const searchInput = document.getElementById('search');
    let currentPage = 1;

    // Set default date range (3 days before today until today)
    const today = new Date();
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(today.getDate() - 3);

    // Format dates as YYYY-MM-DD
    startDate.value = threeDaysAgo.toISOString().split('T')[0];
    endDate.value = today.toISOString().split('T')[0];

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function loadTransactions(page = 1) {
        const params = new URLSearchParams({
            status: statusFilter.value,
            start_date: startDate.value,
            end_date: endDate.value,
            search: searchInput.value,
            page: page,
            use_last_date: true // Enable the feature to use last date with data
        });

        // Updated endpoint URL
        fetch(`/site/transactions/history?${params}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Check if we're showing data from a different date
                const lastDataAlert = document.getElementById('last-data-alert');
                if (data.usedLastDate && data.lastDate) {
                    // Update the date inputs to reflect the actual date being shown
                    startDate.value = data.lastDate;
                    endDate.value = data.lastDate;

                    // Show the alert
                    lastDataAlert.style.display = 'block';
                    lastDataAlert.querySelector('span').textContent =
                        `Tidak ada data untuk periode yang dipilih. Menampilkan data dari tanggal ${formatDate(data.lastDate)}.`;
                } else {
                    // Hide the alert if we're showing data from the selected date range
                    lastDataAlert.style.display = 'none';
                }

                renderTable(data);
                renderPagination(data);
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Gagal memuat data transaksi'
                });
            });
    }

    function renderTable(data) {
        const tbody = document.getElementById('transaction-table-body');
        tbody.innerHTML = '';
        let i=1;
        data.data.forEach(transaction => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${i++}</td>
                <td>${transaction.part.part_code}</td>
                <td>${transaction.part.part_name}</td>
                <td>${new Date(transaction.transaction_date).toLocaleDateString('id-ID')}</td>
                <td>${transaction.quantity_sent}</td>
                <td>${transaction.quantity_received || '-'}</td>
                <td>
                    <span class="badge badge-${getStatusBadgeClass(transaction.status)}">
                        ${transaction.status}
                    </span>
                </td>
                <td>
                    ${transaction.surat_jalan_path ?
                        `<a href="/storage/${transaction.surat_jalan_path}" target="_blank" class="btn btn-sm btn-primary">
                            <i class="mdi mdi-file-document"></i> Lihat
                         </a>` :
                        '-'}
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    function getStatusBadgeClass(status) {
        switch (status) {
            case 'intransit': return 'warning text-black';
            case 'pending': return 'info';
            case 'selesai': return 'success ';
            default: return 'secondary';
        }
    }

    function renderPagination(data) {
        const container = document.getElementById('pagination-container');
        container.innerHTML = '';

        if (data.last_page > 1) {
            const pagination = document.createElement('ul');
            pagination.className = 'pagination pagination-rounded  justify-content-center';
            if (data.current_page > 1) {
                pagination.appendChild(createPaginationItem(data.current_page - 1, '«'));
            }
            for (let i = 1; i <= data.last_page; i++) {
                pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
            }
            if (data.current_page < data.last_page) {
                pagination.appendChild(createPaginationItem(data.current_page + 1, '»'));
            }
            container.appendChild(pagination);
        }
    }

    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item${isActive ? ' active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.addEventListener('click', (e) => {
            e.preventDefault();
            loadTransactions(page);
        });

        li.appendChild(a);
        return li;
    }

    // Event listeners
    statusFilter.addEventListener('change', () => loadTransactions(1));
    startDate.addEventListener('change', () => loadTransactions(1));
    endDate.addEventListener('change', () => loadTransactions(1));
    searchInput.addEventListener('input', debounce(() => loadTransactions(1), 500));

    // Format date to a more readable format (DD MMMM YYYY)
    function formatDate(dateString) {
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        return new Date(dateString).toLocaleDateString('id-ID', options);
    }

    // Initial load
    loadTransactions();
});


