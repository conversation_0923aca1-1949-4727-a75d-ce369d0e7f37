/**
 * Superadmin Scaling JavaScript
 * This file contains the dynamic scaling functionality for superadmin pages
 * to make the UI more compact and usable on high-resolution displays.
 * It also includes responsive adjustments for mobile devices.
 */

document.addEventListener('DOMContentLoaded', function() {
    const dashboardContainer = document.querySelector('.dashboard-container');

    if (!dashboardContainer) {
        console.warn('Dashboard container not found. Scaling functionality will not be applied.');
        return;
    }

    // Function to dynamically adjust scaling based on screen width
    function adjustScaling() {
        const screenWidth = window.innerWidth;

        // For mobile devices, ensure no scaling is applied
        if (screenWidth <= 992) {
            // Reset styles for mobile screens
            dashboardContainer.style.transform = '';
            dashboardContainer.style.transformOrigin = '';
            dashboardContainer.style.width = '';
            dashboardContainer.style.maxWidth = '';
            document.body.style.minHeight = '';

            // Add a class to the body to indicate mobile view is active
            document.body.classList.remove('scaling-active');
            document.body.classList.add('mobile-view');

            // Adjust chart heights for better mobile display
            const chartContainers = document.querySelectorAll('.chart-container');
            chartContainers.forEach(container => {
                if (screenWidth <= 576) {
                    container.style.height = '150px';
                } else {
                    container.style.height = '180px';
                }
            });
        }
        // For screens wider than 1920px, apply dynamic scaling
        else if (screenWidth >= 1920) {
            // Calculate scale factor (50% at 1920px, gradually decreasing as screen gets larger)
            const scaleFactor = Math.max(0.5, 1920 / screenWidth);

            // Apply scaling transform
            dashboardContainer.style.transform = `scale(${scaleFactor})`;
            dashboardContainer.style.transformOrigin = 'top center';
            dashboardContainer.style.width = `${(100 / scaleFactor)}%`;
            dashboardContainer.style.maxWidth = 'none';

            // Adjust body height to match scaled content
            document.body.style.minHeight = `${50 * scaleFactor}vh`;

            // Add a class to the body to indicate scaling is active
            document.body.classList.add('scaling-active');
            document.body.classList.remove('mobile-view');
        } else {
            // Reset styles for normal desktop screens
            dashboardContainer.style.transform = '';
            dashboardContainer.style.transformOrigin = '';
            dashboardContainer.style.width = '';
            dashboardContainer.style.maxWidth = '';
            document.body.style.minHeight = '';

            // Remove both classes
            document.body.classList.remove('scaling-active');
            document.body.classList.remove('mobile-view');
        }
    }

    // Initial adjustment
    adjustScaling();

    // Adjust scaling on window resize
    window.addEventListener('resize', adjustScaling);

    // Log that scaling functionality has been initialized
    console.log('Superadmin scaling functionality initialized.');
});
