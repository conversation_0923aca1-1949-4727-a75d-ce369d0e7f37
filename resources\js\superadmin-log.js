document.addEventListener('DOMContentLoaded', function() {
    // Global variables for pagination
    let currentPage = 1;
    const itemsPerPage = 20; // 20 items per page

    // Set CSRF token for all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    });

    // Load logs on page load
    loadLogs();

    // Add event listeners for filters
    document.getElementById('site_filter').addEventListener('change', function() {
        currentPage = 1; // Reset to first page when changing site filter
        loadLogs();
    });

    document.getElementById('start_date').addEventListener('change', function() {
        currentPage = 1; // Reset to first page when changing date filter
        loadLogs();
    });

    document.getElementById('end_date').addEventListener('change', function() {
        currentPage = 1; // Reset to first page when changing date filter
        loadLogs();
    });

    // Add debounce for search input
    let searchTimeout;
    document.getElementById('search_input').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            currentPage = 1; // Reset to first page when searching
            loadLogs();
        }, 500);
    });

    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;

        li.appendChild(a);
        return li;
    }

    // Function to render pagination
    function renderPagination(data) {
        const container = document.getElementById('pagination-container');
        container.innerHTML = '';

        if (data.last_page > 1) {
            const pagination = document.createElement('ul');
            pagination.className = 'pagination pagination-rounded justify-content-center';

            // Previous button
            if (data.current_page > 1) {
                pagination.appendChild(createPaginationItem(data.current_page - 1, '«'));
            }

            // First page
            pagination.appendChild(createPaginationItem(1, '1', data.current_page === 1));

            // Ellipsis if needed
            if (data.current_page > 3) {
                const ellipsis = document.createElement('li');
                ellipsis.className = 'page-item disabled';
                const span = document.createElement('span');
                span.className = 'page-link';
                span.innerHTML = '&hellip;';
                ellipsis.appendChild(span);
                pagination.appendChild(ellipsis);
            }

            // Pages around current page
            for (let i = Math.max(2, data.current_page - 1); i <= Math.min(data.last_page - 1, data.current_page + 1); i++) {
                pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
            }

            // Ellipsis if needed
            if (data.current_page < data.last_page - 2) {
                const ellipsis = document.createElement('li');
                ellipsis.className = 'page-item disabled';
                const span = document.createElement('span');
                span.className = 'page-link';
                span.innerHTML = '&hellip;';
                ellipsis.appendChild(span);
                pagination.appendChild(ellipsis);
            }

            // Last page if not already added
            if (data.last_page > 1) {
                pagination.appendChild(createPaginationItem(data.last_page, data.last_page, data.current_page === data.last_page));
            }

            // Next button
            if (data.current_page < data.last_page) {
                pagination.appendChild(createPaginationItem(data.current_page + 1, '»'));
            }

            container.appendChild(pagination);

            // Add event listeners to pagination links
            container.querySelectorAll('.page-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    currentPage = parseInt(this.dataset.page);
                    loadLogs();
                });
            });
        }
    }

    // Function to load logs with filters and pagination
    function loadLogs() {
        const tableBody = document.querySelector('#logTable tbody');
        
        // Show loading skeleton
        tableBody.innerHTML = '';
        for (let i = 0; i < 5; i++) {
            tableBody.innerHTML += `<tr><td colspan="7"><div class="skeleton-row"></div></td></tr>`;
        }

        const siteId = document.getElementById('site_filter').value;
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const search = document.getElementById('search_input').value;

        let url = `/superadmin/log/data?page=${currentPage}&per_page=${itemsPerPage}`;

        // Add filters if provided
        if (siteId && siteId !== 'all') {
            url += `&site_id=${siteId}`;
        }

        if (startDate && endDate) {
            url += `&start_date=${startDate}&end_date=${endDate}`;
        }

        if (search) {
            url += `&search=${encodeURIComponent(search)}`;
        }

        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                updateLogTable(data);
                renderPagination(data);
            })
            .catch(error => {
                console.error('Error loading logs:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Kesalahan!',
                    text: 'Gagal memuat data log aktivitas.'
                });
                tableBody.innerHTML = `<tr><td colspan="7" class="text-center">Gagal memuat data. Silakan coba lagi.</td></tr>`;
            });
    }

    // Function to update the log table with data
    function updateLogTable(data) {
        const tableBody = document.querySelector('#logTable tbody');
        tableBody.innerHTML = '';

        // Check if we have data
        if (!data.data || data.data.length === 0) {
            // Display a message in the table when no data is available
            const emptyRow = `
                <tr>
                    <td colspan="7" class="text-center py-3">Tidak ada data log aktivitas yang tersedia.</td>
                </tr>
            `;
            tableBody.innerHTML = emptyRow;
            return;
        }

        // If we have data, display it
        let i = (data.current_page - 1) * data.per_page + 1;
        data.data.forEach(log => {
            const row = `
                <tr>
                    <td>${i++}</td>
                    <td>${log.site_name || '-'}</td>
                    <td>${log.name || '-'}</td>
                    <td>${log.action || '-'}</td>
                    <td>${log.description || '-'}</td>
                    <td>${log.table || '-'}</td>
                    <td>${log.formatted_created_at || '-'}</td>
                </tr>
            `;
            tableBody.innerHTML += row;
        });
    }
});
