<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\JasaKaryawan;
use App\Models\Part;
use App\Models\PartInventory;
use App\Models\Requisition;
use App\Models\RequisitionDetail;
use App\Models\Penawaran;
use App\Models\PenawaranItem;
use App\Models\Site;
use App\Models\UnitTransaction;
use App\Models\UnitTransactionPart;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SuperadminController extends Controller
{
    /**
     * Display the superadmin dashboard
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function dashboard(Request $request)
    {
        // Get date range parameters
        $startDateParam = $request->input('start_date');
        $endDateParam = $request->input('end_date');

        // Get filter parameters
        $divisionFilter = $request->input('division');
        $siteFilter = $request->input('site');

        // Initialize date variables
        $startDate = null;
        $endDate = null;
        $date = null;
        $selectedMonth = null;
        $monthName = null;
        $dateRangeText = null;

        // Handle date range parameters if provided
        if ($startDateParam && $endDateParam) {
            // Parse date range parameters
            $startDate = Carbon::parse($startDateParam)->startOfDay();
            $endDate = Carbon::parse($endDateParam)->endOfDay();

            // Format for display
            $startDateFormatted = $startDate->locale('id')->translatedFormat('j F Y');
            $endDateFormatted = $endDate->locale('id')->translatedFormat('j F Y');
            $dateRangeText = "$startDateFormatted - $endDateFormatted";

            // Use the middle date for month-based functions
            $date = $startDate->copy()->addDays($endDate->diffInDays($startDate) / 2);

            // Format dates for database queries
            $startDateDb = $startDate->format('Y-m-d H:i:s');
            $endDateDb = $endDate->format('Y-m-d H:i:s');
        } else {
            // Fallback to current month if no date range provided
            $now = Carbon::now();
            $selectedMonth = $request->input('month', $now->format('Y-m'));
            $date = Carbon::createFromFormat('Y-m', $selectedMonth);
            $monthName = $date->locale('id')->translatedFormat('F Y');

            // Set date range to the entire month
            $startDate = $date->copy()->startOfMonth();
            $endDate = $date->copy()->endOfMonth();

            // Format dates for database queries
            $startDateDb = $startDate->format('Y-m-d H:i:s');
            $endDateDb = $endDate->format('Y-m-d H:i:s');
        }

        // Get previous period for comparison (same length as selected period)
        $periodLength = $endDate->diffInDays($startDate) + 1;
        $prevStartDate = $startDate->copy()->subDays($periodLength);
        $prevEndDate = $prevStartDate->copy()->addDays($periodLength - 1);
        $prevDate = $prevStartDate->copy()->addDays($periodLength / 2);

        // Get site income data for the selected period with filters
        $siteIncomeData = $this->getSiteIncomeData($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);

        // Get financial data with filters
        $costData = $this->getCostData($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);
        $profitMarginData = $this->getProfitMarginData($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);
        $accountsReceivableData = $this->getAccountsReceivableData($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);
        $accountsPayableData = $this->getAccountsPayableData($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);

        // Get site-specific unit counts with filters
        $siteUnitCounts = $this->getSiteUnitCounts($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);

        // Get site details for each site
        $siteDetails = [];
        foreach ($siteIncomeData as $incomeData) {
            $siteId = $incomeData['site_id'];

            // Skip if site filter is applied and doesn't match
            if ($siteFilter && $siteFilter !== $siteId) {
                continue;
            }

            // Get repair/pending invoices
            $repairPendingInvoices = $this->getRepairPendingInvoices($siteId, $startDateDb, $endDateDb, $divisionFilter);

            // Get parts not ready (stock <= min_stock)
            $partsNotReady = $this->getPartsNotReady($siteId, '', $divisionFilter);

            // Get pending requests to HO
            $pendingRequests = $this->getPendingRequests($siteId, $startDateDb, $endDateDb, $divisionFilter);

            // Get best-selling parts by type
            $bestPartSales = $this->getBestPartSalesByType($siteId, $startDateDb, $endDateDb, $divisionFilter);

            $siteDetails[$siteId] = [
                'repair_pending_invoices' => $repairPendingInvoices,
                'part_not_ready' => $partsNotReady,
                'pending_requests' => $pendingRequests,
                'best_part_sales' => $bestPartSales
            ];
        }

        // Get previous period data for comparison with filters
        $prevStartDateDb = $prevStartDate->format('Y-m-d H:i:s');
        $prevEndDateDb = $prevEndDate->format('Y-m-d H:i:s');
        $prevSiteIncomeData = $this->getSiteIncomeData($prevDate, $divisionFilter, $siteFilter, $prevStartDateDb, $prevEndDateDb);

        // Combine site income data with unit counts and details
        $sitesData = [];
        foreach ($siteIncomeData as $incomeData) {
            $siteId = $incomeData['site_id'];

            // Skip if site filter is applied and doesn't match
            if ($siteFilter && $siteFilter !== $siteId) {
                continue;
            }

            // Find previous period data for this site
            $prevSiteData = null;
            foreach ($prevSiteIncomeData as $prevData) {
                if ($prevData['site_id'] === $siteId) {
                    $prevSiteData = $prevData;
                    break;
                }
            }

            // Calculate trend (percentage change from previous period)
            $trend = 0;
            $trendDirection = 'stable'; // 'up', 'down', or 'stable'

            if ($prevSiteData) {
                $prevPercentage = $prevSiteData['percentage_of_target'];
                $currentPercentage = $incomeData['percentage_of_target'];

                $trend = $currentPercentage - $prevPercentage;

                if ($trend > 0) {
                    $trendDirection = 'up';
                } elseif ($trend < 0) {
                    $trendDirection = 'down';
                }
            }

            // Get POs and Invoices data for this site with filters
            $posAndInvoicesData = $this->getSitePOsAndInvoicesData($siteId, $date, $divisionFilter, $startDateDb, $endDateDb);

            $sitesData[$siteId] = array_merge(
                $incomeData,
                $siteUnitCounts[$siteId] ?? [],
                [
                    'details' => $siteDetails[$siteId] ?? [],
                    'trend' => round($trend, 1),
                    'trend_direction' => $trendDirection,
                    'total_pos_amount' => $posAndInvoicesData['total_pos_amount'],
                    'ready_invoices_amount' => $posAndInvoicesData['ready_invoices_amount'],
                    'total_pos_count' => $posAndInvoicesData['total_pos_count'],
                    'ready_invoices_count' => $posAndInvoicesData['ready_invoices_count'],
                    'invoice_percentage' => $posAndInvoicesData['percentage']
                ]
            );
        }

        // Get additional dashboard information with filters
        $totalInvoiceAmount = $this->getTotalInvoiceAmount($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);

        // Calculate profit margin percentage: (Laba Bersih ÷ Piutang) × 100%
        $profitMarginPercentage = $accountsReceivableData > 0 ? ($totalInvoiceAmount / $accountsReceivableData) * 100 : 0;

        // Get previous period's total invoice amount for comparison with filters
        $prevTotalInvoiceAmount = $this->getTotalInvoiceAmount($prevDate, $divisionFilter, $siteFilter, $prevStartDateDb, $prevEndDateDb);

        // Calculate comparison with previous period
        $invoiceDifference = $totalInvoiceAmount - $prevTotalInvoiceAmount;
        $invoiceDifferencePercent = $prevTotalInvoiceAmount > 0
            ? round(($invoiceDifference / $prevTotalInvoiceAmount) * 100, 1)
            : 0;

        // Get units data with filters
        $unitsReadyForWO = $this->getUnitsReadyForWO($divisionFilter, $siteFilter);
        $unitsReadyForPO = $this->getUnitsReadyForPO($divisionFilter, $siteFilter);
        $unitsInProgress = $this->getUnitsInProgress($divisionFilter, $siteFilter);

        // Get units with "Belum PO" and "Proses PO" status for the selected period with filters
        $unitsBelumPO = $this->getUnitsBelumPO($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);
        $unitsProsesPO = $this->getUnitsProsesPO($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);

        // Best parts data moved to parts page

        // Get monthly invoice data for the entire year with filters
        $currentYear = $date->year;
        $monthlyInvoiceData = $this->getMonthlyInvoiceData($currentYear, $divisionFilter, $siteFilter);

        // Get totalMonthly Reportamount for the selected period with filters
        $totalJasaKaryawanAmount = $this->getJasaKaryawanAmount($date, null, $siteFilter, $startDateDb, $endDateDb);
        $doneJasaKaryawanAmount = $this->getJasaKaryawanAmount($date, 'done', $siteFilter, $startDateDb, $endDateDb);

        // Get allMonthly Reportdata for the selected period with filters
        $jasaKaryawanData = $this->getAllJasaKaryawanData($date, $siteFilter, $startDateDb, $endDateDb);

        return view('superadmin.dashboard', compact(
            'siteIncomeData',
            'sitesData',
            'totalInvoiceAmount',
            'invoiceDifference',
            'invoiceDifferencePercent',
            'unitsReadyForWO',
            'unitsReadyForPO',
            'unitsInProgress',
            'unitsBelumPO',
            'unitsProsesPO',
            'selectedMonth',
            'monthName',
            'monthlyInvoiceData',
            'currentYear',
            'totalJasaKaryawanAmount',
            'doneJasaKaryawanAmount',
            'jasaKaryawanData',
            'costData',
            'profitMarginData',
            'accountsReceivableData',
            'accountsPayableData',
            'profitMarginPercentage',
            'divisionFilter',
            'siteFilter',
            'startDate',
            'endDate',
            'dateRangeText'
        ));
    }

    /**
     * Calculate site income data
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    private function getSiteIncomeData($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Define site targets
        $siteTargets = [
            'PPA' => *********, // IDR 300,000,000
            'IMK' => *********, // IDR 200,000,000
            'UDU' => *********, // IDR 200,000,000
        ];

        // Get sites based on filter
        $sitesQuery = Site::where('site_id', '!=', 'WHO');

        // Apply site filter if provided
        if ($siteFilter) {
            $sitesQuery->where('site_id', $siteFilter);
        }

        $sites = $sitesQuery->get();

        $siteIncomeData = [];
        $totalIncome = 0;

        // Set date range for filtering if not provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        // Calculate income for each site
        foreach ($sites as $site) {
            // Get all unit transactions for this site that have invoices
            $query = UnitTransaction::where('site_id', $site->site_id)
                ->whereHas('invoices')
                ->with(['invoices', 'parts']);

            // Apply date filter if provided
            if ($startDate && $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }

            // Apply division filter if provided
            if ($divisionFilter) {
                $query->whereHas('parts.partInventory.part', function($q) use ($divisionFilter) {
                    $q->where('part_type', $divisionFilter);
                });
            }

            $unitTransactions = $query->get();

            $siteIncome = 0;

            // Calculate total income from invoices
            foreach ($unitTransactions as $transaction) {
                foreach ($transaction->parts as $part) {
                    // If division filter is applied, only count parts of that type
                    if ($divisionFilter) {
                        if ($part->partInventory && $part->partInventory->part && $part->partInventory->part->part_type === $divisionFilter) {
                            $siteIncome += $part->price * $part->quantity;
                        }
                    } else {
                        $siteIncome += $part->price * $part->quantity;
                    }
                }
            }

            // Add PPN (11%)
            $siteIncome += $siteIncome * 0.11;

            // Get target for this site (default to 0 if not defined)
            $target = $siteTargets[$site->site_id] ?? 0;

            // Calculate percentage of target
            $percentageOfTarget = $target > 0 ? ($siteIncome / $target) * 100 : 0;

            // Add to total income
            $totalIncome += $siteIncome;

            // Add to site income data array
            $siteIncomeData[] = [
                'site_id' => $site->site_id,
                'site_name' => $site->site_name,
                'site_address' => $site->address,
                'income' => $siteIncome,
                'target' => $target,
                'percentage_of_target' => $percentageOfTarget,
            ];
        }

        // Calculate percentage of total income for each site
        foreach ($siteIncomeData as &$data) {
            $data['percentage_of_total'] = $totalIncome > 0 ? ($data['income'] / $totalIncome) * 100 : 0;
        }

        return $siteIncomeData;
    }

    /**
     * Calculate total invoice amount for the selected period
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return float
     */
    private function getTotalInvoiceAmount($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Set date range for filtering if not provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        // Get all invoices for the selected month
        $query = Invoice::with(['unitTransactions.parts.part']);

        // Apply date filter if provided
        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Apply site filter if provided
        if ($siteFilter) {
            $query->whereHas('unitTransactions', function($q) use ($siteFilter) {
                $q->where('site_id', $siteFilter);
            });
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('unitTransactions.parts.part', function($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        $invoices = $query->get();

        $totalAmount = 0;

        foreach ($invoices as $invoice) {
            $subtotal = 0;

            foreach ($invoice->unitTransactions as $transaction) {
                // Skip if site filter is applied and doesn't match
                if ($siteFilter && $transaction->site_id !== $siteFilter) {
                    continue;
                }

                foreach ($transaction->parts as $part) {
                    // If division filter is applied, only count parts of that type
                    if ($divisionFilter) {
                        if ($part->partInventory && $part->partInventory->part && $part->partInventory->part->part_type === $divisionFilter) {
                            $subtotal += $part->price * $part->quantity;
                        }
                    } else {
                        $subtotal += $part->price * $part->quantity;
                    }
                }
            }

            // Add PPN
            $ppn = $invoice->ppn ?? 0.11;
            $totalAmount += $subtotal + ($subtotal * $ppn);
        }

        // GetMonthly Reportwith 'done' status for the selected month
        $jasaKaryawanAmount = $this->getJasaKaryawanAmount($date, 'done', $siteFilter);

        // AddMonthly Reportamount to total
        $totalAmount += $jasaKaryawanAmount;

        return $totalAmount;
    }

    /**
     * GetMonthly Reportamount for the selected period and status
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $status
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return float
     */
    private function getJasaKaryawanAmount($date = null, $status = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Set date range for filtering if not provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d');
            }
        } else {
            // Convert datetime format to date format if needed
            if (strpos($startDate, ' ') !== false) {
                $startDate = substr($startDate, 0, 10);
            }
            if (strpos($endDate, ' ') !== false) {
                $endDate = substr($endDate, 0, 10);
            }
        }

        // Build query
        $query = JasaKaryawan::query();

        // Apply date filter if provided
        if ($startDate && $endDate) {
            $query->whereBetween('date', [$startDate, $endDate]);
        }

        // Apply status filter if provided
        if ($status) {
            $query->where('status', $status);
        }

        // Apply site filter if provided
        if ($siteFilter) {
            $query->where('site_id', $siteFilter);
        }

        // Sum the amount
        return $query->sum('amount');
    }

    /**
     * GetMonthly Reportdata for a specific site and period
     *
     * @param  string  $siteId
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    private function getSiteJasaKaryawanData($siteId, $date = null, $startDate = null, $endDate = null)
    {
        try {
            // Set date range for filtering if not provided
            if (!$startDate || !$endDate) {
                if ($date) {
                    $startDate = $date->copy()->startOfMonth()->format('Y-m-d');
                    $endDate = $date->copy()->endOfMonth()->format('Y-m-d');
                }
            } else {
                // Convert datetime format to date format if needed
                if (strpos($startDate, ' ') !== false) {
                    $startDate = substr($startDate, 0, 10);
                }
                if (strpos($endDate, ' ') !== false) {
                    $endDate = substr($endDate, 0, 10);
                }
            }

            // Get allMonthly Reportfor this site
            $query = JasaKaryawan::where('site_id', $siteId);

            // Apply date filter if provided
            if ($startDate && $endDate) {
                $query->whereBetween('date', [$startDate, $endDate]);
            }

            // Get total amount for all statuses
            $totalAmount = (clone $query)->sum('amount');

            // Get total amount for 'done' status
            $doneAmount = (clone $query)->where('status', 'done')->sum('amount');

            // Get counts by status
            $submittedCount = (clone $query)->where('status', 'submitted')->count();
            $approvedCount = (clone $query)->where('status', 'approved')->count();
            $rejectedCount = (clone $query)->where('status', 'rejected')->count();
            $doneCount = (clone $query)->where('status', 'done')->count();

            return [
                'total_amount' => $totalAmount,
                'done_amount' => $doneAmount,
                'submitted_count' => $submittedCount,
                'approved_count' => $approvedCount,
                'rejected_count' => $rejectedCount,
                'done_count' => $doneCount,
                'total_count' => $submittedCount + $approvedCount + $rejectedCount + $doneCount
            ];
        } catch (\Exception $e) {
            Log::error('Error in getSiteJasaKaryawanData for site ' . $siteId . ': ' . $e->getMessage());

            // Return default values
            return [
                'total_amount' => 0,
                'done_amount' => 0,
                'submitted_count' => 0,
                'approved_count' => 0,
                'rejected_count' => 0,
                'done_count' => 0,
                'total_count' => 0
            ];
        }
    }

    /**
     * Calculate remaining invoice amount (unpaid invoices)
     *
     * @return float
     */
    private function getRemainingInvoiceAmount()
    {
        $unpaidInvoices = Invoice::where('payment_status', '!=', 'Lunas')
            ->orWhereNull('payment_status')
            ->with(['unitTransactions.parts'])
            ->get();

        $remainingAmount = 0;

        foreach ($unpaidInvoices as $invoice) {
            $subtotal = 0;

            foreach ($invoice->unitTransactions as $transaction) {
                foreach ($transaction->parts as $part) {
                    $subtotal += $part->price * $part->quantity;
                }
            }

            // Add PPN
            $ppn = $invoice->ppn ?? 0.11;
            $remainingAmount += $subtotal + ($subtotal * $ppn);
        }

        return $remainingAmount;
    }

    /**
     * Calculate cost data for the selected period
     * Currently using dummy data (70% of total invoice amount)
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return float
     */
    private function getCostData($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Get total invoice amount for the period with filters
        $totalInvoiceAmount = $this->getTotalInvoiceAmount($date, $divisionFilter, $siteFilter, $startDate, $endDate);

        // For now, use dummy data - 70% of total invoice amount
        $costAmount = $totalInvoiceAmount * 0.7;

        return $costAmount;
    }

    /**
     * Calculate profit margin data for the selected period
     * Sum of all invoices for the period
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return float
     */
    private function getProfitMarginData($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Set date range for filtering if not provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        // Get all invoices for the selected month
        $query = Invoice::with(['unitTransactions.parts.partInventory.part']);

        // Apply date filter if provided
        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Apply site filter if provided
        if ($siteFilter) {
            $query->whereHas('unitTransactions', function($q) use ($siteFilter) {
                $q->where('site_id', $siteFilter);
            });
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('unitTransactions.parts.partInventory.part', function($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        $invoices = $query->get();

        $totalMargin = 0;

        foreach ($invoices as $invoice) {
            $subtotal = 0;

            foreach ($invoice->unitTransactions as $transaction) {
                // Skip if site filter is applied and doesn't match
                if ($siteFilter && $transaction->site_id !== $siteFilter) {
                    continue;
                }

                foreach ($transaction->parts as $part) {
                    // If division filter is applied, only count parts of that type
                    if ($divisionFilter) {
                        if ($part->partInventory && $part->partInventory->part && $part->partInventory->part->part_type === $divisionFilter) {
                            $subtotal += $part->price * $part->quantity;
                        }
                    } else {
                        $subtotal += $part->price * $part->quantity;
                    }
                }
            }

            // Calculate margin (30% of subtotal)
            $margin = $subtotal * 0.3;
            $totalMargin += $margin;
        }

        return $totalMargin;
    }

    /**
     * Calculate accounts receivable data (piutang)
     * Sum of all invoices based on the current date filter
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return float
     */
    private function getAccountsReceivableData($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Get total invoice amount for the period with filters
        $totalInvoiceAmount = $this->getTotalInvoiceAmount($date, $divisionFilter, $siteFilter, $startDate, $endDate);

        // Return the actual total invoice amount as piutang
        return $totalInvoiceAmount;
    }

    /**
     * Calculate accounts payable data
     * Currently using dummy data (25% of total invoice amount)
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return float
     */
    private function getAccountsPayableData($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Get total invoice amount for the period with filters
        $totalInvoiceAmount = $this->getTotalInvoiceAmount($date, $divisionFilter, $siteFilter, $startDate, $endDate);

        // For now, use dummy data - 25% of total invoice amount
        $accountsPayable = $totalInvoiceAmount * 0.25;

        return $accountsPayable;
    }

    /**
     * Get monthly invoice data for the entire year, grouped by site
     *
     * @param  int  $year
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @return array
     */
    private function getMonthlyInvoiceData($year = null, $divisionFilter = null, $siteFilter = null)
    {
        // If no year provided, use current year
        if (!$year) {
            $year = Carbon::now()->year;
        }

        $monthNames = [];
        $siteMonthlyData = [];
        $siteColors = [
            'WHO' => 'rgba(235, 49, 36, 0.7)', // Red for Warehouse/HO
        ];

        // Get all sites for grouping
        $sites = Site::all(['site_id', 'site_name']);

        // Assign colors to sites
        $colorIndex = 0;
        $baseColors = [
            'rgba(34, 82, 151, 0.7)',    // Primary blue
            'rgba(88, 192, 246, 0.7)',   // Secondary blue
            'rgba(81, 9, 104, 0.7)',     // Dark purple
            'rgba(254, 255, 140, 0.7)',  // Warning yellow
            'rgba(151, 247, 132, 0.7)',  // Success green
        ];

        foreach ($sites as $site) {
            if ($site->site_id != 'WHO') { // Skip WHO as we already assigned it
                $siteColors[$site->site_id] = $baseColors[$colorIndex % count($baseColors)];
                $colorIndex++;
            }
        }

        // Initialize arrays with zeros for all months and sites
        for ($month = 1; $month <= 12; $month++) {
            $date = Carbon::createFromDate($year, $month, 1);
            $monthNames[] = $date->locale('id')->translatedFormat('M'); // Short month name (Jan, Feb, etc.)

            foreach ($sites as $site) {
                if (!isset($siteMonthlyData[$site->site_id])) {
                    $siteMonthlyData[$site->site_id] = [
                        'name' => $site->site_name,
                        'data' => array_fill(1, 12, 0),
                        'color' => $siteColors[$site->site_id] ?? 'rgba(34, 82, 151, 0.7)'
                    ];
                }
            }

            // Add a category for invoices without site (assign to Warehouse/HO)
            if (!isset($siteMonthlyData['WHO'])) {
                $siteMonthlyData['WHO'] = [
                    'name' => 'Warehouse/HO',
                    'data' => array_fill(1, 12, 0),
                    'color' => $siteColors['WHO']
                ];
            }
        }

        // Get all invoices for the year
        $startDate = Carbon::createFromDate($year, 1, 1)->startOfYear()->format('Y-m-d H:i:s');
        $endDate = Carbon::createFromDate($year, 12, 31)->endOfYear()->format('Y-m-d H:i:s');

        $query = Invoice::whereBetween('created_at', [$startDate, $endDate])
            ->with(['unitTransactions.parts.partInventory.part', 'unitTransactions.site', 'penawaran.items.partInventory.part']);

        // Apply site filter if provided
        if ($siteFilter) {
            $query->whereHas('unitTransactions', function($q) use ($siteFilter) {
                $q->where('site_id', $siteFilter);
            });
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('unitTransactions.parts.partInventory.part', function($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        $invoices = $query->get();

        // Process each invoice
        foreach ($invoices as $invoice) {
            // Skip if no invoice date
            if (!$invoice->tanggal_invoice) {
                continue;
            }

            // Get the month from the invoice date
            $invoiceMonth = $invoice->tanggal_invoice->month;

            // If this is a penawaran invoice
            if ($invoice->penawaran_id) {
                // Load the penawaran if not already loaded
                if (!$invoice->relationLoaded('penawaran')) {
                    $invoice->load('penawaran.items.partInventory.part');
                }

                // Calculate subtotal from penawaran items
                if ($invoice->penawaran && $invoice->penawaran->items) {
                    $subtotal = 0;
                    foreach ($invoice->penawaran->items as $item) {
                        // Skip if no part inventory or quantity
                        if (!$item->partInventory || !$item->quantity) {
                            continue;
                        }

                        // Apply division filter if provided
                        if ($divisionFilter && $item->partInventory->part && $item->partInventory->part->part_type !== $divisionFilter) {
                            continue;
                        }

                        $subtotal += $item->price * $item->quantity;
                    }

                    // Add PPN
                    $ppn = $invoice->ppn ?? 0.11;
                    $totalWithPpn = $subtotal + ($subtotal * $ppn);

                    // Add to WHO/Warehouse data since penawaran doesn't have site
                    $siteMonthlyData['WHO']['data'][$invoiceMonth] += $totalWithPpn;
                }
            } else {
                // For unit transaction invoices, group by site
                $siteTotals = [];

                // Calculate subtotal from unit transactions grouped by site
                foreach ($invoice->unitTransactions as $transaction) {
                    // Skip if no parts
                    if (!$transaction->parts) {
                        continue;
                    }

                    // Apply site filter if provided
                    if ($siteFilter && $transaction->site_id !== $siteFilter) {
                        continue;
                    }

                    $siteId = $transaction->site_id ?? 'WHO';
                    if (!isset($siteTotals[$siteId])) {
                        $siteTotals[$siteId] = 0;
                    }

                    foreach ($transaction->parts as $part) {
                        // Skip if no part inventory or quantity
                        if (!$part->partInventory || !$part->quantity) {
                            continue;
                        }

                        // Apply division filter if provided
                        if ($divisionFilter && $part->partInventory->part && $part->partInventory->part->part_type !== $divisionFilter) {
                            continue;
                        }

                        $siteTotals[$siteId] += $part->price * $part->quantity;
                    }
                }

                // If no unit transactions with site_id, assign to WHO
                if (empty($siteTotals)) {
                    // Calculate total from invoice
                    $subtotal = $invoice->getSubtotalAttribute();
                    $ppn = $invoice->ppn ?? 0.11;
                    $totalWithPpn = $subtotal + ($subtotal * $ppn);

                    $siteMonthlyData['WHO']['data'][$invoiceMonth] += $totalWithPpn;
                } else {
                    // Add PPN and add to site data
                    foreach ($siteTotals as $siteId => $subtotal) {
                        $ppn = $invoice->ppn ?? 0.11;
                        $totalWithPpn = $subtotal + ($subtotal * $ppn);

                        // If site doesn't exist in our data (unlikely), add to WHO
                        if (!isset($siteMonthlyData[$siteId])) {
                            $siteId = 'WHO';
                        }

                        $siteMonthlyData[$siteId]['data'][$invoiceMonth] += $totalWithPpn;
                    }
                }
            }
        }

        // Prepare datasets for Chart.js
        $datasets = [];
        foreach ($siteMonthlyData as $siteId => $siteData) {
            // Skip if site filter is applied and this is not the filtered site
            if ($siteFilter && $siteId !== $siteFilter && $siteId !== 'WHO') {
                continue;
            }

            // Convert to array of values (preserve order)
            // Always use 0 for months with no data to ensure continuous lines
            $monthlyValues = [];
            for ($month = 1; $month <= 12; $month++) {
                // Always use the value, even if it's 0, to ensure continuous lines
                $value = $siteData['data'][$month];
                $monthlyValues[] = $value;
            }

            $datasets[] = [
                'label' => $siteData['name'],
                'data' => $monthlyValues,
                'backgroundColor' => $siteData['color'],
                'borderColor' => str_replace('0.7', '1', $siteData['color']),
                'borderWidth' => 3,
                'pointBackgroundColor' => str_replace('0.7', '1', $siteData['color']),
                'pointBorderColor' => '#fff',
                'pointBorderWidth' => 2
            ];
        }

        return [
            'labels' => $monthNames,
            'datasets' => $datasets
        ];
    }

    /**
     * Get sites data via AJAX for the dashboard
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSitesData(Request $request)
    {
        try {
            // Log the request for debugging
            Log::info('Sites Data Request', [
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
                'month' => $request->input('month'),
                'division' => $request->input('division'),
                'site' => $request->input('site')
            ]);

            // Get date range parameters
            $startDateParam = $request->input('start_date');
            $endDateParam = $request->input('end_date');
            $selectedMonth = $request->input('month');

            // Get filter parameters
            $divisionFilter = $request->input('division');
            $siteFilter = $request->input('site');

            // Initialize date variables
            $date = null;
            $startDate = null;
            $endDate = null;

            // Handle date range parameters if provided
            try {
                if ($startDateParam && $endDateParam) {
                    // Parse date range parameters
                    $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
                    $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');

                    // Use the middle date for month-based functions
                    $middleDate = Carbon::parse($startDateParam)->addDays(
                        Carbon::parse($endDateParam)->diffInDays(Carbon::parse($startDateParam)) / 2
                    );
                    $date = $middleDate;
                } else if ($selectedMonth) {
                    // Fallback to month parameter if provided
                    $date = Carbon::createFromFormat('Y-m', $selectedMonth);
                    $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                    $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
                } else {
                    // Default to current month
                    $date = Carbon::now();
                    $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                    $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
                }
            } catch (\Exception $e) {
                Log::error('Error parsing dates: ' . $e->getMessage());
                // Fallback to current month
                $date = Carbon::now();
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }

            // Get site income data for the selected period with filters
            $siteIncomeData = $this->getSiteIncomeData($date, $divisionFilter, $siteFilter, $startDate, $endDate);

            // Get site-specific unit counts with filters
            $siteUnitCounts = $this->getSiteUnitCounts($date, $divisionFilter, $siteFilter, $startDate, $endDate);

            // Get site details for each site
            $siteDetails = [];
            foreach ($siteIncomeData as $incomeData) {
                $siteId = $incomeData['site_id'];

                // Skip if site filter is applied and doesn't match
                if ($siteFilter && $siteFilter !== $siteId) {
                    continue;
                }

                try {
                    // Get repair/pending invoices with filters
                    $repairPendingInvoices = $this->getRepairPendingInvoices($siteId, $startDate, $endDate, $divisionFilter);

                    // Get parts not ready (stock <= min_stock) with filters
                    $partsNotReady = $this->getPartsNotReady($siteId, '', $divisionFilter);

                    // Get pending requests to HO with filters
                    $pendingRequests = $this->getPendingRequests($siteId, $startDate, $endDate, $divisionFilter);

                    // Get best-selling parts by type with filters
                    $bestPartSales = $this->getBestPartSalesByType($siteId, $startDate, $endDate, $divisionFilter);

                    $siteDetails[$siteId] = [
                        'repair_pending_invoices' => $repairPendingInvoices,
                        'part_not_ready' => $partsNotReady,
                        'pending_requests' => $pendingRequests,
                        'best_part_sales' => $bestPartSales
                    ];
                } catch (\Exception $e) {
                    Log::error('Error getting site details for site ' . $siteId . ': ' . $e->getMessage());
                    // Set default values
                    $siteDetails[$siteId] = [
                        'repair_pending_invoices' => ['count' => 0, 'value' => 0],
                        'part_not_ready' => ['count' => 0, 'items' => []],
                        'pending_requests' => ['count' => 0, 'items' => []],
                        'best_part_sales' => []
                    ];
                }
            }

            // Get previous month data for comparison with filters
            try {
                $prevDate = $date->copy()->subMonth();
                $prevStartDate = $prevDate->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $prevEndDate = $prevDate->copy()->endOfMonth()->format('Y-m-d H:i:s');
                $prevSiteIncomeData = $this->getSiteIncomeData($prevDate, $divisionFilter, $siteFilter, $prevStartDate, $prevEndDate);
            } catch (\Exception $e) {
                Log::error('Error getting previous month data: ' . $e->getMessage());
                $prevSiteIncomeData = [];
            }

            // Combine site income data with unit counts and details
            $sitesData = [];
            foreach ($siteIncomeData as $incomeData) {
                $siteId = $incomeData['site_id'];

                // Skip if site filter is applied and doesn't match
                if ($siteFilter && $siteFilter !== $siteId) {
                    continue;
                }

                // Find previous month data for this site
                $prevSiteData = null;
                foreach ($prevSiteIncomeData as $prevData) {
                    if ($prevData['site_id'] === $siteId) {
                        $prevSiteData = $prevData;
                        break;
                    }
                }

                // Calculate trend (percentage change from previous month)
                $trend = 0;
                $trendDirection = 'stable'; // 'up', 'down', or 'stable'

                if ($prevSiteData) {
                    $prevPercentage = $prevSiteData['percentage_of_target'];
                    $currentPercentage = $incomeData['percentage_of_target'];

                    $trend = $currentPercentage - $prevPercentage;

                    if ($trend > 0) {
                        $trendDirection = 'up';
                    } elseif ($trend < 0) {
                        $trendDirection = 'down';
                    }
                }

                try {
                    // Get POs and Invoices data for this site with filters
                    $posAndInvoicesData = $this->getSitePOsAndInvoicesData($siteId, $date, $divisionFilter, $startDate, $endDate);

                    $sitesData[$siteId] = array_merge(
                        $incomeData,
                        $siteUnitCounts[$siteId] ?? [],
                        [
                            'details' => $siteDetails[$siteId] ?? [],
                            'trend' => round($trend, 1),
                            'trend_direction' => $trendDirection,
                            'total_pos_amount' => $posAndInvoicesData['total_pos_amount'],
                            'ready_invoices_amount' => $posAndInvoicesData['ready_invoices_amount'],
                            'total_pos_count' => $posAndInvoicesData['total_pos_count'],
                            'ready_invoices_count' => $posAndInvoicesData['ready_invoices_count'],
                            'invoice_percentage' => $posAndInvoicesData['percentage']
                        ]
                    );
                } catch (\Exception $e) {
                    Log::error('Error getting POs and Invoices data for site ' . $siteId . ': ' . $e->getMessage());
                    // Set default values
                    $sitesData[$siteId] = array_merge(
                        $incomeData,
                        $siteUnitCounts[$siteId] ?? [],
                        [
                            'details' => $siteDetails[$siteId] ?? [],
                            'trend' => round($trend, 1),
                            'trend_direction' => $trendDirection,
                            'total_pos_amount' => 0,
                            'ready_invoices_amount' => 0,
                            'total_pos_count' => 0,
                            'ready_invoices_count' => 0,
                            'invoice_percentage' => 0
                        ]
                    );
                }
            }

            // Get jasa karyawan data for each site
            foreach ($sitesData as $siteId => &$siteData) {
                try {
                    $jasaKaryawanData = $this->getSiteJasaKaryawanData($siteId, $date, $startDate, $endDate);
                    if (!empty($jasaKaryawanData) && isset($jasaKaryawanData['total_count']) && $jasaKaryawanData['total_count'] > 0) {
                        $siteData['jasa_karyawan'] = $jasaKaryawanData;
                    }
                } catch (\Exception $e) {
                    Log::error('Error getting jasa karyawan data for site ' . $siteId . ': ' . $e->getMessage());
                    // Continue with other sites
                }
            }

            // Get belum PO and proses PO data for each site
            foreach ($sitesData as $siteId => &$siteData) {
                try {
                    $belumPO = UnitTransactionPart::whereHas('unitTransaction', function ($query) use ($siteId, $startDate, $endDate) {
                        $query->where('site_id', $siteId)
                            ->whereBetween('created_at', [$startDate, $endDate])
                            ->where('status', 'belum po');
                    })->count();

                    $belumPOAmount = UnitTransactionPart::whereHas('unitTransaction', function ($query) use ($siteId, $startDate, $endDate) {
                        $query->where('site_id', $siteId)
                            ->whereBetween('created_at', [$startDate, $endDate])
                            ->where('status', 'belum po');
                    })->sum(DB::raw('price * quantity'));

                    $prosesPO = UnitTransactionPart::whereHas('unitTransaction', function ($query) use ($siteId, $startDate, $endDate) {
                        $query->where('site_id', $siteId)
                            ->whereBetween('created_at', [$startDate, $endDate])
                            ->where('status', 'proses po');
                    })->count();

                    $prosesPOAmount = UnitTransactionPart::whereHas('unitTransaction', function ($query) use ($siteId, $startDate, $endDate) {
                        $query->where('site_id', $siteId)
                            ->whereBetween('created_at', [$startDate, $endDate])
                            ->where('status', 'proses po');
                    })->sum(DB::raw('price * quantity'));

                    $siteData['belum_po'] = $belumPO;
                    $siteData['belum_po_amount'] = $belumPOAmount;
                    $siteData['proses_po'] = $prosesPO;
                    $siteData['proses_po_amount'] = $prosesPOAmount;
                } catch (\Exception $e) {
                    Log::error('Error getting PO data for site ' . $siteId . ': ' . $e->getMessage());
                    // Set default values
                    $siteData['belum_po'] = 0;
                    $siteData['belum_po_amount'] = 0;
                    $siteData['proses_po'] = 0;
                    $siteData['proses_po_amount'] = 0;
                }
            }

            // Convert to array for JSON response
            $sitesDataArray = [];
            foreach ($sitesData as $siteData) {
                $sitesDataArray[] = $siteData;
            }

            // Log success
            Log::info('Sites Data Response', [
                'count' => count($sitesDataArray)
            ]);

            return response()->json($sitesDataArray);
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in getSitesData: ' . $e->getMessage() . "\n" . $e->getTraceAsString());

            // Return error response
            return response()->json(['error' => 'Failed to get sites data: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get total POs and ready invoices data for a site
     *
     * @param string $siteId
     * @param \Carbon\Carbon|null $date
     * @param string|null $divisionFilter
     * @param string|null $startDate
     * @param string|null $endDate
     * @return array
     */
    private function getSitePOsAndInvoicesData($siteId, $date = null, $divisionFilter = null, $startDate = null, $endDate = null)
    {
        // Set date range for filtering if not provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        // Get all unit transactions for this site (Total POs)
        $totalPOsQuery = UnitTransaction::where('site_id', $siteId)
            ->with(['parts.partInventory.part']);

        // Apply date filter if provided
        if ($startDate && $endDate) {
            $totalPOsQuery->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $totalPOsQuery->whereHas('parts.partInventory.part', function($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        $totalPOsTransactions = $totalPOsQuery->get();
        $totalPOsAmount = 0;

        // Calculate total amount for all unit transactions
        foreach ($totalPOsTransactions as $transaction) {
            foreach ($transaction->parts as $part) {
                // If division filter is applied, only count parts of that type
                if ($divisionFilter) {
                    if ($part->partInventory && $part->partInventory->part && $part->partInventory->part->part_type === $divisionFilter) {
                        $totalPOsAmount += $part->price * $part->quantity;
                    }
                } else {
                    $totalPOsAmount += $part->price * $part->quantity;
                }
            }
        }

        // Get unit transactions that have invoices (Ready Invoices)
        $readyInvoicesQuery = UnitTransaction::where('site_id', $siteId)
            ->whereHas('invoices')
            ->with(['parts.partInventory.part']);

        // Apply date filter if provided
        if ($startDate && $endDate) {
            $readyInvoicesQuery->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $readyInvoicesQuery->whereHas('parts.partInventory.part', function($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        $readyInvoicesTransactions = $readyInvoicesQuery->get();
        $readyInvoicesAmount = 0;

        // Calculate total amount for unit transactions with invoices
        foreach ($readyInvoicesTransactions as $transaction) {
            foreach ($transaction->parts as $part) {
                // If division filter is applied, only count parts of that type
                if ($divisionFilter) {
                    if ($part->partInventory && $part->partInventory->part && $part->partInventory->part->part_type === $divisionFilter) {
                        $readyInvoicesAmount += $part->price * $part->quantity;
                    }
                } else {
                    $readyInvoicesAmount += $part->price * $part->quantity;
                }
            }
        }

        // GetMonthly Reportdata for this site
        try {
            $jasaKaryawanData = $this->getSiteJasaKaryawanData($siteId, $date, $startDate, $endDate);

            // Ensure jasaKaryawanData has the expected structure
            if (!isset($jasaKaryawanData['total_amount'])) {
                $jasaKaryawanData['total_amount'] = 0;
            }
            if (!isset($jasaKaryawanData['done_amount'])) {
                $jasaKaryawanData['done_amount'] = 0;
            }

            // AddMonthly Report'done' amount to total POs amount (target)
            $totalPOsAmount += $jasaKaryawanData['total_amount'];

            // AddMonthly Report'done' amount to ready invoices amount
            $readyInvoicesAmount += $jasaKaryawanData['done_amount'];
        } catch (\Exception $e) {
            Log::error('Error getting jasa karyawan data for site ' . $siteId . ': ' . $e->getMessage());
            // Don't add anything to the totals
            $jasaKaryawanData = [
                'total_amount' => 0,
                'done_amount' => 0,
                'total_count' => 0,
                'done_count' => 0
            ];
        }

        // Calculate percentage based on amounts
        $percentage = $totalPOsAmount > 0 ? ($readyInvoicesAmount / $totalPOsAmount) * 100 : 0;

        return [
            'total_pos_amount' => $totalPOsAmount,
            'ready_invoices_amount' => $readyInvoicesAmount,
            'total_pos_count' => $totalPOsTransactions->count(),
            'ready_invoices_count' => $readyInvoicesTransactions->count(),
            'percentage' => $percentage,
            'jasa_karyawan' => $jasaKaryawanData
        ];
    }

    /**
     * Get site-specific unit status counts
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    private function getSiteUnitCounts($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Get sites based on filter
        $sitesQuery = Site::where('site_id', '!=', 'WHO');

        // Apply site filter if provided
        if ($siteFilter) {
            $sitesQuery->where('site_id', $siteFilter);
        }

        $sites = $sitesQuery->get();
        $siteUnitCounts = [];

        // Set date range for filtering if not provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        foreach ($sites as $site) {
            $query = UnitTransaction::where('site_id', $site->site_id);

            // Apply date filter if provided
            if ($startDate && $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }

            // Apply division filter if provided
            if ($divisionFilter) {
                $divisionFilterQuery = function($q) use ($divisionFilter) {
                    $q->whereHas('parts.partInventory.part', function($q2) use ($divisionFilter) {
                        $q2->where('part_type', $divisionFilter);
                    });
                };

                $query->where(function($q) use ($divisionFilterQuery) {
                    $divisionFilterQuery($q);
                });
            }

            $readyWOQuery = clone $query;
            $readyWOQuery->where('status', 'Ready WO');
            if ($divisionFilter) {
                $readyWOQuery->whereHas('parts.partInventory.part', function($q) use ($divisionFilter) {
                    $q->where('part_type', $divisionFilter);
                });
            }
            $readyWO = $readyWOQuery->count();

            $readyPOQuery = clone $query;
            $readyPOQuery->where('status', 'Ready PO');
            if ($divisionFilter) {
                $readyPOQuery->whereHas('parts.partInventory.part', function($q) use ($divisionFilter) {
                    $q->where('part_type', $divisionFilter);
                });
            }
            $readyPO = $readyPOQuery->count();

            $inProgressQuery = clone $query;
            $inProgressQuery->where('status', 'On Process');
            if ($divisionFilter) {
                $inProgressQuery->whereHas('parts.partInventory.part', function($q) use ($divisionFilter) {
                    $q->where('part_type', $divisionFilter);
                });
            }
            $inProgress = $inProgressQuery->count();

            // Get "Belum PO" transactions (all statuses except "Ready PO", "Selesai", "Pending", "Perbaikan")
            $belumPOQuery = (clone $query)->whereNotIn('status', ['Ready PO', 'Selesai', 'Pending', 'perbaikan']);

            // Apply date filter if provided
            if ($startDate && $endDate) {
                $belumPOQuery->whereBetween('created_at', [$startDate, $endDate]);
            }

            // Apply division filter if provided
            if ($divisionFilter) {
                $belumPOQuery->whereHas('parts.partInventory.part', function($q) use ($divisionFilter) {
                    $q->where('part_type', $divisionFilter);
                });
            }

            $belumPOTransactions = $belumPOQuery->with(['parts.partInventory.part'])->get();

            $belumPOCount = $belumPOTransactions->count();
            $belumPOAmount = 0;

            foreach ($belumPOTransactions as $transaction) {
                foreach ($transaction->parts as $part) {
                    // If division filter is applied, only count parts of that type
                    if ($divisionFilter) {
                        if ($part->partInventory && $part->partInventory->part && $part->partInventory->part->part_type === $divisionFilter) {
                            $belumPOAmount += $part->price * $part->quantity;
                        }
                    } else {
                        $belumPOAmount += $part->price * $part->quantity;
                    }
                }
            }

            // Get "Proses Invoice" transactions ("Ready PO", "Pending", "Perbaikan") - excluding "Selesai"
            $prosesPOQuery = (clone $query)->whereIn('status', ['Ready PO', 'Pending', 'perbaikan']);

            // Apply date filter if provided
            if ($startDate && $endDate) {
                $prosesPOQuery->whereBetween('created_at', [$startDate, $endDate]);
            }

            // Apply division filter if provided
            if ($divisionFilter) {
                $prosesPOQuery->whereHas('parts.partInventory.part', function($q) use ($divisionFilter) {
                    $q->where('part_type', $divisionFilter);
                });
            }

            $prosesPOTransactions = $prosesPOQuery->with(['parts.partInventory.part'])->get();

            $prosesPOCount = $prosesPOTransactions->count();
            $prosesPOAmount = 0;

            foreach ($prosesPOTransactions as $transaction) {
                foreach ($transaction->parts as $part) {
                    // If division filter is applied, only count parts of that type
                    if ($divisionFilter) {
                        if ($part->partInventory && $part->partInventory->part && $part->partInventory->part->part_type === $divisionFilter) {
                            $prosesPOAmount += $part->price * $part->quantity;
                        }
                    } else {
                        $prosesPOAmount += $part->price * $part->quantity;
                    }
                }
            }

            $siteUnitCounts[$site->site_id] = [
                'site_id' => $site->site_id,
                'site_name' => $site->site_name,
                'ready_wo' => $readyWO,
                'ready_po' => $readyPO,
                'in_progress' => $inProgress,
                'belum_po' => $belumPOCount,
                'belum_po_amount' => $belumPOAmount,
                'proses_po' => $prosesPOCount,
                'proses_po_amount' => $prosesPOAmount,
                'total' => $readyWO + $readyPO + $inProgress
            ];
        }

        return $siteUnitCounts;
    }

    /**
     * Count units ready for Work Order (WO)
     *
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @return int
     */
    private function getUnitsReadyForWO($divisionFilter = null, $siteFilter = null)
    {
        $query = UnitTransaction::where('status', 'Ready WO');

        // Apply site filter if provided
        if ($siteFilter) {
            $query->where('site_id', $siteFilter);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('parts.partInventory.part', function($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        return $query->count();
    }

    /**
     * Count units ready for Purchase Order (PO)
     *
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @return int
     */
    private function getUnitsReadyForPO($divisionFilter = null, $siteFilter = null)
    {
        $query = UnitTransaction::where('status', 'Ready PO');

        // Apply site filter if provided
        if ($siteFilter) {
            $query->where('site_id', $siteFilter);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('parts.partInventory.part', function($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        return $query->count();
    }

    /**
     * Count units in progress (unit transactions)
     *
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @return int
     */
    private function getUnitsInProgress($divisionFilter = null, $siteFilter = null)
    {
        $query = UnitTransaction::where('status', 'On Process');

        // Apply site filter if provided
        if ($siteFilter) {
            $query->where('site_id', $siteFilter);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('parts.partInventory.part', function($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        return $query->count();
    }

    /**
     * Get units with "Belum PO" status (all statuses except "Ready PO", "Selesai", "Pending", "Perbaikan")
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    private function getUnitsBelumPO($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Create query for unit transactions with statuses other than the specified ones
        $query = UnitTransaction::whereNotIn('status', ['Ready PO', 'Selesai', 'Pending', 'perbaikan']);

        // Apply date filter if provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Apply site filter if provided
        if ($siteFilter) {
            $query->where('site_id', $siteFilter);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('parts.partInventory.part', function($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        // Get transactions with their parts
        $transactions = $query->with(['parts.partInventory.part'])->get();

        // Calculate total amount
        $totalAmount = 0;
        foreach ($transactions as $transaction) {
            foreach ($transaction->parts as $part) {
                // If division filter is applied, only count parts of that type
                if ($divisionFilter) {
                    if ($part->partInventory && $part->partInventory->part && $part->partInventory->part->part_type === $divisionFilter) {
                        $totalAmount += $part->price * $part->quantity;
                    }
                } else {
                    $totalAmount += $part->price * $part->quantity;
                }
            }
        }

        return [
            'count' => $transactions->count(),
            'amount' => $totalAmount
        ];
    }

    /**
     * Get units with "Proses Invoice" status ("Ready PO", "Pending", "Perbaikan") - excluding "Selesai"
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    private function getUnitsProsesPO($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Create query for unit transactions with the specified statuses
        // Exclude "Selesai" status as requested
        $query = UnitTransaction::whereIn('status', ['Ready PO', 'Pending', 'perbaikan']);

        // Apply date filter if provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Apply site filter if provided
        if ($siteFilter) {
            $query->where('site_id', $siteFilter);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('parts.partInventory.part', function($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        // Get transactions with their parts
        $transactions = $query->with(['parts.partInventory.part'])->get();

        // Calculate total amount
        $totalAmount = 0;
        foreach ($transactions as $transaction) {
            foreach ($transaction->parts as $part) {
                // If division filter is applied, only count parts of that type
                if ($divisionFilter) {
                    if ($part->partInventory && $part->partInventory->part && $part->partInventory->part->part_type === $divisionFilter) {
                        $totalAmount += $part->price * $part->quantity;
                    }
                } else {
                    $totalAmount += $part->price * $part->quantity;
                }
            }
        }

        return [
            'count' => $transactions->count(),
            'amount' => $totalAmount
        ];
    }

    /**
     * Get detailed information for a specific site
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $siteId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSiteDetails(Request $request, $siteId)
    {
        // Get date range parameters
        $startDateParam = $request->input('start_date');
        $endDateParam = $request->input('end_date');
        $selectedMonth = $request->input('month');

        // Get filter parameters
        $divisionFilter = $request->input('division');

        // Initialize date variables
        $date = null;
        $startDate = null;
        $endDate = null;

        // Handle date range parameters if provided
        if ($startDateParam && $endDateParam) {
            // Parse date range parameters
            $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
            $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');

            // Use the middle date for month-based functions
            $middleDate = Carbon::parse($startDateParam)->addDays(
                Carbon::parse($endDateParam)->diffInDays(Carbon::parse($startDateParam)) / 2
            );
            $date = $middleDate;
        } else if ($selectedMonth) {
            // Fallback to month parameter if provided
            $date = Carbon::createFromFormat('Y-m', $selectedMonth);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        } else {
            // Default to current month
            $date = Carbon::now();
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        }

        // 1. Get repair/pending invoices with filters
        $repairPendingInvoices = $this->getRepairPendingInvoices($siteId, $startDate, $endDate, $divisionFilter);

        // 2. Get parts not ready (stock <= min_stock) with filters
        $partsNotReady = $this->getPartsNotReady($siteId, '', $divisionFilter);

        // 3. Get pending requests to HO with filters
        $pendingRequests = $this->getPendingRequests($siteId, $startDate, $endDate, $divisionFilter);

        // 4. Get best-selling parts by type with filters
        $bestPartSales = $this->getBestPartSalesByType($siteId, $startDate, $endDate, $divisionFilter);

        return response()->json([
            'repair_pending_invoices' => $repairPendingInvoices,
            'part_not_ready' => $partsNotReady,
            'pending_requests' => $pendingRequests,
            'best_part_sales' => $bestPartSales,
            'division_filter' => $divisionFilter
        ]);
    }

    /**
     * Get repair/pending invoices for a site
     *
     * @param  string  $siteId
     * @param  string  $startDate
     * @param  string  $endDate
     * @param  string|null  $divisionFilter
     * @return array
     */
    private function getRepairPendingInvoices($siteId, $startDate, $endDate, $divisionFilter = null)
    {
        try {
            // Get unit transactions with repair or pending status
            $query = UnitTransaction::where('site_id', $siteId)
                ->whereIn('status', ['repair', 'pending'])
                ->whereBetween('created_at', [$startDate, $endDate]);

            // Apply division filter if provided
            if ($divisionFilter) {
                $query->whereHas('parts', function($q) use ($divisionFilter) {
                    $q->whereHas('partInventory', function($q2) use ($divisionFilter) {
                        $q2->whereHas('part', function($q3) use ($divisionFilter) {
                            $q3->where('part_type', $divisionFilter);
                        });
                    });
                });
            }

            $transactions = $query->with(['invoices', 'parts.partInventory.part'])->get();

            $totalValue = 0;

            foreach ($transactions as $transaction) {
                $transactionTotal = 0;

                // Get parts from UnitTransactionPart model with part inventory and part
                $parts = UnitTransactionPart::where('unit_transaction_id', $transaction->id)
                    ->with(['partInventory.part'])
                    ->get();

                foreach ($parts as $part) {
                    // If division filter is applied, only count parts of that type
                    if ($divisionFilter) {
                        // Eager load the part relationship to avoid N+1 query problem
                        if ($part->partInventory && $part->partInventory->part && $part->partInventory->part->part_type === $divisionFilter) {
                            $transactionTotal += $part->price * $part->quantity;
                        }
                    } else {
                        $transactionTotal += $part->price * $part->quantity;
                    }
                }

                // Add PPN if applicable
                if ($transaction->invoices->isNotEmpty()) {
                    $ppn = $transaction->invoices->first()->ppn ?? 0.11;
                    $transactionTotal *= (1 + $ppn); // Multiply by (1 + ppn) instead of adding
                } else {
                    $transactionTotal *= 1.11; // Default PPN (multiply by 1.11 for 11%)
                }

                $totalValue += $transactionTotal;
            }

            return [
                'count' => $transactions->count(),
                'value' => $totalValue
            ];
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in getRepairPendingInvoices: ' . $e->getMessage());

            // Return empty data
            return [
                'count' => 0,
                'value' => 0
            ];
        }
    }

    /**
     * Get parts that are not ready (stock <= average of min and max stock)
     *
     * @param  string  $siteId
     * @param  string  $search
     * @param  string|null  $divisionFilter
     * @return array
     */
    private function getPartsNotReady($siteId, $search = '', $divisionFilter = null)
    {
        // Base query
        $query = DB::table('part_inventories')
            ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
            ->where('part_inventories.site_id', $siteId);

        // Apply search filter if provided
        if (!empty($search)) {
            $query->where(function($q) use ($search) {
                $q->where('parts.part_name', 'like', "%{$search}%")
                  ->orWhere('parts.part_code', 'like', "%{$search}%")
                  ->orWhere('part_inventories.site_part_name', 'like', "%{$search}%");
            });
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->where('parts.part_type', $divisionFilter);
        }

        // Special case for PPA site which has min_stock and max_stock = 0
        if ($siteId === 'PPA') {
            // For PPA, consider parts with stock <= 5 as "not ready"
            $query->where('part_inventories.stock_quantity', '<=', 5);
        } else {
            // For other sites, use the standard logic
            $query->whereRaw('part_inventories.stock_quantity <= ((part_inventories.min_stock + part_inventories.max_stock) / 2)')
                ->where(function($query) {
                    $query->where('part_inventories.min_stock', '>', 0)
                          ->orWhere('part_inventories.max_stock', '>', 0);
                }); // Only include parts with either min_stock or max_stock > 0
        }

        $parts = $query->select(
                'parts.part_code',
                'parts.part_name',
                'parts.part_type',
                'part_inventories.stock_quantity as stock',
                'part_inventories.min_stock',
                'part_inventories.max_stock'
            )
            ->get();

        return [
            'count' => $parts->count(),
            'items' => $parts
        ];
    }

    /**
     * Get pending requests to HO
     *
     * @param  string  $siteId
     * @param  string  $startDate
     * @param  string  $endDate
     * @param  string|null  $divisionFilter
     * @return array
     */
    private function getPendingRequests($siteId, $startDate, $endDate, $divisionFilter = null)
    {
        try {
            // Get requisitions that are not completed
            $query = DB::table('requisitions')
                ->where('requisitions.site_id', $siteId)
                ->where('requisitions.status', '!=', 'selesai')
                ->whereBetween('requisitions.created_at', [$startDate, $endDate])
                ->join('requisition_details', 'requisitions.requisition_id', '=', 'requisition_details.requisition_id')
                ->join('parts', 'requisition_details.part_code', '=', 'parts.part_code');

            // Apply division filter if provided
            if ($divisionFilter) {
                $query->where('parts.part_type', $divisionFilter);
            }

            $requisitions = $query->select(
                    'requisitions.requisition_id as request_id',
                    'parts.part_name',
                    'parts.part_type',
                    'requisition_details.quantity',
                    'requisitions.status'
                )
                ->get();

            return [
                'count' => $requisitions->count(),
                'items' => $requisitions
            ];
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in getPendingRequests: ' . $e->getMessage());

            // Return empty data
            return [
                'count' => 0,
                'items' => collect([])
            ];
        }
    }

    /**
     * Get best-selling parts by type (AC, TYRE, FABRIKASI)
     *
     * @param  string  $siteId
     * @param  string  $startDate
     * @param  string  $endDate
     * @param  string|null  $divisionFilter
     * @return array
     */
    private function getBestPartSalesByType($siteId, $startDate, $endDate, $divisionFilter = null)
    {
        try {
            // Define the part types we want to track
            $partTypes = Part::distinct()->pluck('part_type')->toArray();

            // Filter out empty part types
            $partTypes = array_filter($partTypes, function($type) {
                return !empty($type);
            });

            // If no part types found, use default ones
            if (empty($partTypes)) {
                $partTypes = ['AC', 'TYRE', 'FABRIKASI', 'PERLENGKAPAN AC', 'PERSEDIAAN LAINNYA'];
            }

            // If division filter is applied, only show that division
            if ($divisionFilter) {
                $partTypes = [$divisionFilter];
            }

            $result = [];

            foreach ($partTypes as $partType) {
                // Get top 5 best-selling parts for this type
                $topParts = DB::table('unit_transaction_parts')
                    ->join('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
                    ->join('part_inventories', 'unit_transaction_parts.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
                    ->where('unit_transactions.site_id', $siteId)
                    ->where('parts.part_type', $partType)
                    ->whereBetween('unit_transactions.created_at', [$startDate, $endDate])
                    ->select(
                        'parts.part_code',
                        'parts.part_name',
                        'parts.part_type',
                        DB::raw('SUM(unit_transaction_parts.quantity) as total_quantity'),
                        DB::raw('SUM(unit_transaction_parts.price * unit_transaction_parts.quantity) as total_value'),
                        DB::raw('AVG(unit_transaction_parts.price) as avg_price')
                    )
                    ->groupBy('parts.part_code', 'parts.part_name', 'parts.part_type')
                    ->orderByDesc('total_value') // Order by total value instead of quantity
                    ->limit(5) // Limit to top 5 best-selling parts
                    ->get();

                // Calculate total revenue for this part type
                $totalTypeRevenue = $topParts->sum('total_value');

                // Add PPN (11%)
                $totalTypeRevenueWithPPN = $totalTypeRevenue * 1.11;

                // Calculate percentage contribution for each part
                foreach ($topParts as $part) {
                    $part->contribution_percent = $totalTypeRevenue > 0
                        ? round(($part->total_value / $totalTypeRevenue) * 100, 1)
                        : 0;
                }

                $result[$partType] = [
                    'count' => $topParts->count(),
                    'items' => $topParts,
                    'total_revenue' => $totalTypeRevenue,
                    'total_revenue_with_ppn' => $totalTypeRevenueWithPPN
                ];
            }

            return $result;
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in getBestPartSalesByType: ' . $e->getMessage());

            // Return empty data
            $emptyResult = [];
            foreach ($partTypes as $partType) {
                $emptyResult[$partType] = [
                    'count' => 0,
                    'items' => collect([]),
                    'total_revenue' => 0,
                    'total_revenue_with_ppn' => 0
                ];
            }
            return $emptyResult;
        }
    }

    /**
     * Get best-selling parts across all sites by type
     *
     * @param  string  $startDate
     * @param  string  $endDate
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @return array
     */
    private function getBestPartsAcrossAllSites($startDate, $endDate, $divisionFilter = null, $siteFilter = null)
    {
        try {
            // Get settings from session or use defaults
            $limit = session('best_parts_limit', 5);
            $sortBy = session('best_parts_sort_by', 'value');

            // Define the specific part types we want to track (only these three)
            $partTypes = ['AC', 'TYRE', 'FABRIKASI'];

            // If division filter is applied, only show that division if it's one of the allowed types
            if ($divisionFilter) {
                $divisionFilter = strtoupper($divisionFilter);
                if (in_array($divisionFilter, $partTypes)) {
                    $partTypes = [$divisionFilter];
                } else {
                    // If invalid division filter, return empty results
                    $emptyResult = [];
                    foreach (['AC', 'TYRE', 'FABRIKASI'] as $type) {
                        $emptyResult[$type] = [
                            'count' => 0,
                            'items' => collect([]),
                            'total_revenue' => 0,
                            'total_revenue_with_ppn' => 0
                        ];
                    }
                    return $emptyResult;
                }
            }

            $result = [];

            foreach ($partTypes as $partType) {
                // Get top parts for this type across all sites based on settings
                $query = DB::table('unit_transaction_parts')
                    ->join('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
                    ->join('part_inventories', 'unit_transaction_parts.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
                    ->join('sites', 'unit_transactions.site_id', '=', 'sites.site_id')
                    ->where('parts.part_type', $partType)
                    ->whereBetween('unit_transactions.created_at', [$startDate, $endDate]);

                // Apply site filter if provided
                if ($siteFilter) {
                    $query->where('unit_transactions.site_id', $siteFilter);
                }

                $query->select(
                    'parts.part_code',
                    'parts.part_name',
                    'parts.part_type',
                    DB::raw('SUM(unit_transaction_parts.quantity) as total_quantity'),
                    DB::raw('SUM(unit_transaction_parts.price * unit_transaction_parts.quantity) as total_value'),
                    DB::raw('AVG(unit_transaction_parts.price) as avg_price'),
                    DB::raw('COUNT(DISTINCT unit_transactions.site_id) as site_count')
                )
                ->groupBy('parts.part_code', 'parts.part_name', 'parts.part_type');

                // Apply sorting based on settings
                if ($sortBy === 'quantity') {
                    $query->orderByDesc('total_quantity');
                } else {
                    $query->orderByDesc('total_value');
                }

                // Apply limit based on settings
                $topParts = $query->limit($limit)->get();

                // Calculate total revenue for this part type
                $totalTypeRevenue = $topParts->sum('total_value');

                // Add PPN (11%)
                $totalTypeRevenueWithPPN = $totalTypeRevenue * 1.11;

                // Calculate percentage contribution for each part
                foreach ($topParts as $part) {
                    $part->contribution_percent = $totalTypeRevenue > 0
                        ? round(($part->total_value / $totalTypeRevenue) * 100, 1)
                        : 0;
                }

                $result[$partType] = [
                    'count' => $topParts->count(),
                    'items' => $topParts,
                    'total_revenue' => $totalTypeRevenue,
                    'total_revenue_with_ppn' => $totalTypeRevenueWithPPN
                ];
            }

            // Ensure all three part types are always present in the result
            $finalResult = [];
            foreach (['AC', 'TYRE', 'FABRIKASI'] as $type) {
                $finalResult[$type] = $result[$type] ?? [
                    'count' => 0,
                    'items' => collect([]),
                    'total_revenue' => 0,
                    'total_revenue_with_ppn' => 0
                ];
            }

            return $finalResult;
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in getBestPartsAcrossAllSites: ' . $e->getMessage());

            // Return empty data for all three part types
            $emptyResult = [];
            foreach (['AC', 'TYRE', 'FABRIKASI'] as $partType) {
                $emptyResult[$partType] = [
                    'count' => 0,
                    'items' => collect([]),
                    'total_revenue' => 0,
                    'total_revenue_with_ppn' => 0
                ];
            }
            return $emptyResult;
        }
    }

    /**
     * Save best parts settings to session
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveBestPartsSettings(Request $request)
    {
        // Validate request
        $request->validate([
            'limit' => 'required|integer|min:1|max:20',
            'sort_by' => 'required|in:value,quantity',
        ]);

        // Save settings to session
        session(['best_parts_limit' => $request->limit]);
        session(['best_parts_sort_by' => $request->sort_by]);

        return response()->json([
            'success' => true,
            'message' => 'Pengaturan berhasil disimpan',
        ]);
    }

    /**
     * Get best parts data for AJAX request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBestPartsData(Request $request)
    {
        // Get date range parameters
        $startDateParam = $request->input('start_date');
        $endDateParam = $request->input('end_date');
        $selectedMonth = $request->input('month');
        $divisionFilter = $request->input('division');
        $siteFilter = $request->input('site');

        // Initialize date variables
        $startDate = null;
        $endDate = null;

        // Handle date range parameters if provided
        if ($startDateParam && $endDateParam) {
            // Parse date range parameters
            $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
            $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');
        } else if ($selectedMonth) {
            // Fallback to month parameter if provided
            $date = Carbon::createFromFormat('Y-m', $selectedMonth);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        } else {
            // Default to current month
            $date = Carbon::now();
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        }

        // Normalize division filter (convert to uppercase for consistency)
        if ($divisionFilter) {
            $divisionFilter = strtoupper($divisionFilter);
        }

        // Get best parts across all sites with filters
        $bestPartsAllSites = $this->getBestPartsAcrossAllSites($startDate, $endDate, $divisionFilter, $siteFilter);

        // Log the request parameters and date range for debugging
        Log::info('Best Parts Data Request', [
            'start_date' => $startDateParam,
            'end_date' => $endDateParam,
            'month' => $selectedMonth,
            'division' => $divisionFilter,
            'site' => $siteFilter,
            'used_start_date' => $startDate,
            'used_end_date' => $endDate
        ]);

        return response()->json($bestPartsAllSites);
    }

    /**
     * Get units with specific status for dashboard modal
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $status
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnitsByStatus(Request $request, $status)
    {
        // Get selected date range
        $startDateInput = $request->input('start_date');
        $endDateInput = $request->input('end_date');
        $siteId = $request->input('site_id');

        // Set default date range if not provided
        if (!$startDateInput || !$endDateInput) {
            $now = Carbon::now();
            $startDate = $now->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $now->format('Y-m-d H:i:s');
        } else {
            $startDate = Carbon::createFromFormat('Y-m-d', $startDateInput)->startOfDay()->format('Y-m-d H:i:s');
            $endDate = Carbon::createFromFormat('Y-m-d', $endDateInput)->endOfDay()->format('Y-m-d H:i:s');
        }

        // Build the query based on the status parameter
        if ($status === 'belum-po') {
            // Get units with "Belum PO" status (all statuses except "Ready PO", "Selesai", "Pending", "Perbaikan")
            $query = UnitTransaction::whereNotIn('status', ['Ready PO', 'Selesai', 'Pending', 'perbaikan']);
        } elseif ($status === 'proses-invoice') {
            // Get units with "Proses Invoice" status ("Ready PO", "Pending", "Perbaikan")
            $query = UnitTransaction::whereIn('status', ['Ready PO', 'Pending', 'perbaikan']);
        } else {
            return response()->json(['error' => 'Invalid status parameter'], 400);
        }

        // Apply date filter
        $query->whereBetween('created_at', [$startDate, $endDate]);

        // Apply site filter if provided
        if ($siteId) {
            $query->where('site_id', $siteId);
        }

        // Get transactions with their parts and unit
        $transactions = $query->with(['parts.partInventory.part', 'unit'])->get();

        // Format the data for the response
        $formattedTransactions = $transactions->map(function ($transaction) {
            // Calculate total price for this transaction
            $totalPrice = 0;

            // Check if unit exists
            $unitName = 'Unknown Unit';
            $unitCode = 'Unknown Code';

            if ($transaction->unit) {
                $unitName = $transaction->unit->unit_type ?? 'Unknown Unit';
                $unitCode = $transaction->unit->unit_code ?? 'Unknown Code';
            }

            // Format parts data for detail view
            $formattedParts = collect();

            if ($transaction->parts && $transaction->parts->count() > 0) {
                $formattedParts = $transaction->parts->map(function ($part) {
                    $partName = 'Unknown Part';
                    $partCode = 'Unknown Code';

                    if ($part->partInventory && $part->partInventory->part) {
                        $partName = $part->partInventory->part->part_name ?? 'Unknown Part';
                        $partCode = $part->partInventory->part->part_code ?? 'Unknown Code';
                    }

                    $price = $part->price ?? 0;
                    $quantity = $part->quantity ?? 0;
                    $total = $price * $quantity;

                    return [
                        'id' => $part->id,
                        'part_name' => $partName,
                        'part_code' => $partCode,
                        'quantity' => $quantity,
                        'price' => $price,
                        'total' => $total
                    ];
                });

                // Calculate total price
                foreach ($transaction->parts as $part) {
                    $price = $part->price ?? 0;
                    $quantity = $part->quantity ?? 0;
                    $totalPrice += $price * $quantity;
                }
            }

            return [
                'id' => $transaction->id,
                'unit_name' => $unitName,
                'unit_code' => $unitCode,
                'total_price' => $totalPrice,
                'status' => $transaction->status,
                'notes' => $transaction->remarks ?? '-',
                'created_at' => $transaction->created_at->format('d-m-Y H:i'),
                'updated_at' => $transaction->updated_at->format('d-m-Y H:i'),
                'attachment_path' => $transaction->attachment_path,
                'parts' => $formattedParts
            ];
        });

        return response()->json([
            'status' => $status,
            'transactions' => $formattedTransactions
        ]);
    }

    /**
     * Get detailed division parts data for modal display
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDivisionPartsDetail(Request $request)
    {
        try {
            // Get parameters
            $startDateParam = $request->input('start_date');
            $endDateParam = $request->input('end_date');
            $selectedMonth = $request->input('month');
            $divisionFilter = $request->input('division');
            $siteFilter = $request->input('site');

            // Log the incoming request parameters for debugging
            Log::info('getDivisionPartsDetail called with parameters:', [
                'start_date' => $startDateParam,
                'end_date' => $endDateParam,
                'month' => $selectedMonth,
                'division' => $divisionFilter,
                'site' => $siteFilter
            ]);

            // Initialize date variables
            $startDate = null;
            $endDate = null;

            // Handle date range parameters if provided
            if ($startDateParam && $endDateParam) {
                $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
                $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');
            } elseif ($selectedMonth) {
                // Parse month parameter (format: YYYY-MM)
                $monthDate = Carbon::createFromFormat('Y-m', $selectedMonth);
                $startDate = $monthDate->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $monthDate->copy()->endOfMonth()->format('Y-m-d H:i:s');
            } else {
                // Default to current month
                $currentDate = Carbon::now();
                $startDate = $currentDate->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $currentDate->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }

            // Normalize division filter
            if ($divisionFilter) {
                $divisionFilter = strtoupper($divisionFilter);
            }

            // Log the processed date range and filters
            Log::info('Processed parameters:', [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'division_filter' => $divisionFilter,
                'site_filter' => $siteFilter
            ]);

            // Validate division filter - only allow AC, TYRE, FABRIKASI
            if ($divisionFilter && !in_array($divisionFilter, ['AC', 'TYRE', 'FABRIKASI'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid division filter. Only AC, TYRE, and FABRIKASI are allowed.',
                    'data' => [],
                    'summary' => [
                        'total_items' => 0,
                        'total_quantity' => 0,
                        'total_value' => 0,
                        'total_value_with_ppn' => 0,
                        'division' => $divisionFilter
                    ]
                ]);
            }

            // Build query for unit transaction parts with detailed information
            // Only include unit transactions that have invoices (indicating they are completed sales)
            $query = DB::table('unit_transaction_parts')
                ->join('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
                ->join('part_inventories', 'unit_transaction_parts.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
                ->join('sites', 'unit_transactions.site_id', '=', 'sites.site_id')
                ->leftJoin('units', 'unit_transactions.unit_id', '=', 'units.id')
                ->leftJoin('invoice_unit_transactions', 'unit_transactions.id', '=', 'invoice_unit_transactions.unit_transaction_id')
                ->leftJoin('invoices', 'invoice_unit_transactions.invoice_id', '=', 'invoices.id')
                ->whereNotNull('invoices.id') // Only include transactions that have invoices
                ->whereBetween('unit_transactions.created_at', [$startDate, $endDate]);

            // Apply division filter if provided
            if ($divisionFilter) {
                $query->where('parts.part_type', $divisionFilter);
                Log::info('Applied division filter:', ['division' => $divisionFilter]);
            }

            // Apply site filter if provided
            if ($siteFilter) {
                $query->where('unit_transactions.site_id', $siteFilter);
                Log::info('Applied site filter:', ['site' => $siteFilter]);
            }

            // Log the SQL query for debugging
            $sql = $query->toSql();
            $bindings = $query->getBindings();
            Log::info('SQL Query:', ['sql' => $sql, 'bindings' => $bindings]);

            // Select detailed information
            $partsData = $query->select(
                'unit_transaction_parts.id',
                'parts.part_code',
                'parts.part_name',
                'parts.part_type',
                'part_inventories.site_part_name',
                'unit_transaction_parts.quantity',
                'unit_transaction_parts.price',
                DB::raw('(unit_transaction_parts.quantity * unit_transaction_parts.price) as total_value'),
                'unit_transactions.created_at as transaction_date',
                'unit_transactions.wo_number',
                'unit_transactions.do_number',
                'unit_transactions.status as transaction_status',
                'sites.site_name',
                'sites.site_id',
                'units.unit_code',
                'units.unit_name',
                'invoices.no_invoice',
                'invoices.tanggal_invoice'
            )
            ->orderBy('unit_transactions.created_at', 'desc')
            ->get();

            // Log the number of records found
            Log::info('Query results:', [
                'total_records' => $partsData->count(),
                'division_filter' => $divisionFilter,
                'date_range' => [$startDate, $endDate]
            ]);

            // If no data found, let's check if there's any data at all in the date range
            if ($partsData->isEmpty()) {
                $totalRecordsInRange = DB::table('unit_transaction_parts')
                    ->join('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
                    ->whereBetween('unit_transactions.created_at', [$startDate, $endDate])
                    ->count();

                Log::info('No data found for division. Total records in date range:', [
                    'total_in_range' => $totalRecordsInRange,
                    'division' => $divisionFilter
                ]);

                // Check what part types exist in the date range
                $existingPartTypes = DB::table('unit_transaction_parts')
                    ->join('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
                    ->join('part_inventories', 'unit_transaction_parts.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
                    ->whereBetween('unit_transactions.created_at', [$startDate, $endDate])
                    ->distinct()
                    ->pluck('parts.part_type');

                Log::info('Existing part types in date range:', ['part_types' => $existingPartTypes->toArray()]);
            }

            // Calculate totals
            $totalQuantity = $partsData->sum('quantity');
            $totalValue = $partsData->sum('total_value');
            $totalValueWithPPN = $totalValue * 1.11;

            // Format the data for display
            $formattedData = $partsData->map(function($item) {
                return [
                    'id' => $item->id,
                    'part_code' => $item->part_code,
                    'part_name' => $item->part_name,
                    'part_type' => $item->part_type,
                    'site_part_name' => $item->site_part_name,
                    'quantity' => $item->quantity,
                    'price' => $item->price,
                    'total_value' => $item->total_value,
                    'transaction_date' => Carbon::parse($item->transaction_date)->format('d-m-Y'),
                    'transaction_date_time' => Carbon::parse($item->transaction_date)->format('d-m-Y H:i'),
                    'wo_number' => $item->wo_number,
                    'do_number' => $item->do_number,
                    'transaction_status' => $item->transaction_status,
                    'site_name' => $item->site_name,
                    'site_id' => $item->site_id,
                    'unit_code' => $item->unit_code,
                    'unit_name' => $item->unit_name,
                    'invoice_number' => $item->no_invoice,
                    'invoice_date' => $item->tanggal_invoice ? Carbon::parse($item->tanggal_invoice)->format('d-m-Y') : null
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedData,
                'summary' => [
                    'total_items' => $partsData->count(),
                    'total_quantity' => $totalQuantity,
                    'total_value' => $totalValue,
                    'total_value_with_ppn' => $totalValueWithPPN,
                    'division' => $divisionFilter,
                    'date_range' => [
                        'start' => $startDate,
                        'end' => $endDate
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error in getDivisionPartsDetail: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Gagal memuat data detail divisi',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the parts information page
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function parts(Request $request)
    {
        // Get all sites
        $allSites = Site::all();

        // Reorder sites to ensure Warehouse (WHO) is first
        $warehouseSite = $allSites->where('site_id', 'WHO')->first();
        $otherSites = $allSites->where('site_id', '!=', 'WHO');

        // Create a new collection with Warehouse first
        $sites = collect([$warehouseSite])->merge($otherSites)->filter();

        // Get date parameters from request
        $startDateParam = $request->input('start_date');
        $endDateParam = $request->input('end_date');
        $selectedMonth = $request->input('month');

        // Initialize date variables
        $date = Carbon::now();
        $startDate = null;
        $endDate = null;

        // Determine date range based on parameters
        if ($startDateParam && $endDateParam) {
            // Use provided date range
            $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
            $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');

            // Format month name for display
            $startDateObj = Carbon::parse($startDateParam);
            $endDateObj = Carbon::parse($endDateParam);

            if ($startDateObj->format('Y-m') === $endDateObj->format('Y-m')) {
                // Same month
                $monthName = $startDateObj->locale('id')->translatedFormat('F Y');
            } else {
                // Different months
                $monthName = $startDateObj->locale('id')->translatedFormat('d F Y') . ' - ' .
                             $endDateObj->locale('id')->translatedFormat('d F Y');
            }

            // Set selected month for compatibility
            $selectedMonth = $startDateObj->format('Y-m');
        } elseif ($selectedMonth) {
            // Use selected month
            $date = Carbon::createFromFormat('Y-m', $selectedMonth);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            $monthName = $date->locale('id')->translatedFormat('F Y');
        } else {
            // Default to current month
            $selectedMonth = $date->format('Y-m');
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            $monthName = $date->locale('id')->translatedFormat('F Y');
        }

        // Get part requisition data
        $requisitions = $this->getAllRequisitions();

        // Get penawaran data
        $penawarans = $this->getAllPenawarans();

        // Get part readiness data for all sites
        $sitePartsData = [];
        foreach ($sites as $site) {
            $sitePartsData[$site->site_id] = [
                'site_name' => $site->site_name,
                'parts_ready' => $this->getPartsReady($site->site_id),
                'parts_not_ready' => $this->getPartsNotReady($site->site_id)
            ];
        }

        // Get best parts across all sites
        $bestPartsAllSites = $this->getBestPartsAcrossAllSites($startDate, $endDate);

        // Get current year for display
        $currentYear = $date->year;

        return view('superadmin.parts', compact(
            'sites',
            'selectedMonth',
            'monthName',
            'requisitions',
            'penawarans',
            'sitePartsData',
            'bestPartsAllSites',
            'currentYear',
            'startDateParam',
            'endDateParam'
        ));
    }

    /**
     * Get parts data for AJAX request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPartsData(Request $request)
    {
        // Get search parameter
        $search = $request->input('search', '');

        // Get date parameters from request
        $startDateParam = $request->input('start_date');
        $endDateParam = $request->input('end_date');
        $selectedMonth = $request->input('month');

        // Initialize date variables
        $date = Carbon::now();
        $startDate = null;
        $endDate = null;

        // Determine date range based on parameters
        if ($startDateParam && $endDateParam) {
            // Use provided date range
            $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
            $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');
        } elseif ($selectedMonth) {
            // Use selected month
            $date = Carbon::createFromFormat('Y-m', $selectedMonth);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        } else {
            // Default to current month
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        }

        // Get all sites
        $allSites = Site::all();

        // Reorder sites to ensure Warehouse (WHO) is first
        $warehouseSite = $allSites->where('site_id', 'WHO')->first();
        $otherSites = $allSites->where('site_id', '!=', 'WHO');

        // Create a new collection with Warehouse first
        $sites = collect([$warehouseSite])->merge($otherSites)->filter();

        // Get part requisition data
        $requisitions = $this->getAllRequisitions();

        // Get penawaran data
        $penawarans = $this->getAllPenawarans();

        // Get part readiness data for all sites
        $sitePartsData = [];
        foreach ($sites as $site) {
            $sitePartsData[$site->site_id] = [
                'site_name' => $site->site_name,
                'parts_ready' => $this->getPartsReady($site->site_id, $search),
                'parts_not_ready' => $this->getPartsNotReady($site->site_id, $search)
            ];
        }

        // Get best parts across all sites with the date range
        $bestPartsAllSites = $this->getBestPartsAcrossAllSites($startDate, $endDate);

        return response()->json([
            'requisitions' => $requisitions,
            'penawarans' => $penawarans,
            'site_parts_data' => $sitePartsData,
            'best_parts_all_sites' => $bestPartsAllSites
        ]);
    }

    /**
     * Get all penawarans with their items and calculate totals
     *
     * @return array
     */
    protected function getAllPenawarans()
    {
        // Get all penawarans with their items
        $penawarans = Penawaran::with(['items.partInventory.part'])
            ->orderBy('created_at', 'desc')
            ->limit(15)
            ->get();

        $formattedPenawarans = [];

        foreach ($penawarans as $penawaran) {
            $total = 0;
            $itemCount = 0;

            // Calculate total and count items
            foreach ($penawaran->items as $item) {
                $total += $item->quantity * $item->price;
                $itemCount++;
            }

            // Format date
            $createdDate = Carbon::parse($penawaran->created_at);
            $tanggalPenawaran = $penawaran->tanggal_penawaran ? Carbon::parse($penawaran->tanggal_penawaran)->format('d/m/Y') : $createdDate->format('d/m/Y');

            // Get status counts
            $statusCounts = [
                'Ready' => 0,
                'In Order' => 0,
                'Not Ready' => 0
            ];

            foreach ($penawaran->items as $item) {
                if (isset($statusCounts[$item->status])) {
                    $statusCounts[$item->status]++;
                }
            }

            // Calculate readiness percentage
            $readyPercentage = $itemCount > 0 ? round(($statusCounts['Ready'] / $itemCount) * 100) : 0;

            $formattedPenawarans[] = [
                'id' => $penawaran->id,
                'nomor' => $penawaran->nomor,
                'perihal' => $penawaran->perihal,
                'customer' => $penawaran->customer,
                'tanggal' => $tanggalPenawaran,
                'status' => $penawaran->status,
                'total' => $total,
                'item_count' => $itemCount,
                'ready_count' => $statusCounts['Ready'],
                'in_order_count' => $statusCounts['In Order'],
                'not_ready_count' => $statusCounts['Not Ready'],
                'ready_percentage' => $readyPercentage
            ];
        }

        return $formattedPenawarans;
    }

    /**
     * Get penawaran detail by ID
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPenawaranDetail($id)
    {
        try {
            // Find the penawaran with its items and related data
            $penawaran = Penawaran::with(['items.partInventory.part'])
                ->findOrFail($id);

            // Return the penawaran data as JSON
            return response()->json($penawaran);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Penawaran tidak ditemukan'], 404);
        }
    }

    /**
     * Get all requisitions with status and age information
     *
     * @return array
     */
    private function getAllRequisitions()
    {
        try {
            $requisitions = Requisition::with(['site', 'requisitionDetails.part'])
                ->whereIn('status', ['diajukan', 'pending'])
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($requisition) {
                    // Calculate age in days
                    $createdDate = Carbon::parse($requisition->created_at);
                    $now = Carbon::now();

                    // If created today, show as "Hari ini"
                    if ($createdDate->isToday()) {
                        $ageInDays = 0;
                    } else {
                        // Use diffInDays to get whole days
                        $ageInDays = (int)$createdDate->diffInDays($now);
                    }

                    return [
                        'id' => $requisition->requisition_id,
                        'title' => $requisition->title,
                        'site_name' => $requisition->site->site_name,
                        'status' => $requisition->status,
                        'created_at' => $requisition->created_at->format('d-m-Y'),
                        'age_days' => $ageInDays,
                        'details_count' => $requisition->requisitionDetails->count(),
                        'notes' => $requisition->notes
                    ];
                });

            return $requisitions->toArray();
        } catch (\Exception $e) {
            Log::error('Error in getAllRequisitions: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get parts that are ready (stock > average of min and max stock)
     *
     * @param  string  $siteId
     * @param  string  $search
     * @return array
     */
    private function getPartsReady($siteId, $search = '')
    {
        // Base query
        $query = DB::table('part_inventories')
            ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
            ->where('part_inventories.site_id', $siteId);

        // Apply search filter if provided
        if (!empty($search)) {
            $query->where(function($q) use ($search) {
                $q->where('parts.part_name', 'like', "%{$search}%")
                  ->orWhere('parts.part_code', 'like', "%{$search}%")
                  ->orWhere('part_inventories.site_part_name', 'like', "%{$search}%");
            });
        }

        // Special case for PPA site which has min_stock and max_stock = 0
        if ($siteId === 'PPA') {
            // For PPA, consider parts with stock > 5 as "ready"
            $query->where('part_inventories.stock_quantity', '>', 5);
        } else {
            // For other sites, use the standard logic
            $query->whereRaw('part_inventories.stock_quantity > ((part_inventories.min_stock + part_inventories.max_stock) / 2)')
                ->where(function($query) {
                    $query->where('part_inventories.min_stock', '>', 0)
                          ->orWhere('part_inventories.max_stock', '>', 0);
                }); // Include parts with either min_stock or max_stock > 0
        }

        $parts = $query->select(
                'parts.part_code',
                'parts.part_name',
                'part_inventories.stock_quantity as stock',
                'part_inventories.min_stock',
                'part_inventories.max_stock'
            )
            ->get();

        return [
            'count' => $parts->count(),
            'items' => $parts
        ];
    }

    /**
     * Get allMonthly Reportdata for the selected period
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    private function getAllJasaKaryawanData($date = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        try {
            // Set date range for filtering if not provided
            if (!$startDate || !$endDate) {
                if ($date) {
                    $startDate = $date->copy()->startOfMonth()->format('Y-m-d');
                    $endDate = $date->copy()->endOfMonth()->format('Y-m-d');
                }
            } else {
                // Convert datetime format to date format if needed
                if (strpos($startDate, ' ') !== false) {
                    $startDate = substr($startDate, 0, 10);
                }
                if (strpos($endDate, ' ') !== false) {
                    $endDate = substr($endDate, 0, 10);
                }
            }

            // Get allMonthly Reportfor the selected month
            $query = JasaKaryawan::with(['site', 'employee'])
                ->orderBy('date', 'desc');

            // Apply date filter if provided
            if ($startDate && $endDate) {
                $query->whereBetween('date', [$startDate, $endDate]);
            }

            // Apply site filter if provided
            if ($siteFilter) {
                $query->where('site_id', $siteFilter);
            }

            $jasaKaryawan = $query->get();

            // Calculate total amount
            $totalAmount = $jasaKaryawan->sum('amount');

            // Calculate total amount with PPN
            $totalAmountWithPPN = $totalAmount + ($totalAmount * 0.11);

            return [
                'items' => $jasaKaryawan,
                'count' => $jasaKaryawan->count(),
                'total_amount' => $totalAmount,
                'total_amount_with_ppn' => $totalAmountWithPPN
            ];
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in getAllJasaKaryawanData: ' . $e->getMessage());

            // Return empty data
            return [
                'items' => collect([]),
                'count' => 0,
                'total_amount' => 0,
                'total_amount_with_ppn' => 0
            ];
        }
    }

    /**
     * GetMonthly Reportdata for AJAX request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getJasaKaryawanData(Request $request)
    {
        // Get date range parameters
        $startDateParam = $request->input('start_date');
        $endDateParam = $request->input('end_date');
        $selectedMonth = $request->input('month', Carbon::now()->format('Y-m'));

        // Initialize date variables
        $date = null;
        $startDate = null;
        $endDate = null;

        // Handle date range parameters if provided
        if ($startDateParam && $endDateParam) {
            // Parse date range parameters
            $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
            $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');

            // Use the middle date for month-based functions
            $middleDate = Carbon::parse($startDateParam)->addDays(
                Carbon::parse($endDateParam)->diffInDays(Carbon::parse($startDateParam)) / 2
            );
            $date = $middleDate;
        } else {
            // Fallback to month parameter
            $date = Carbon::createFromFormat('Y-m', $selectedMonth);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        }

        // Get allMonthly Reportdata for the selected period
        $jasaKaryawanData = $this->getAllJasaKaryawanData($date, null, $startDate, $endDate);

        return response()->json($jasaKaryawanData);
    }

    /**
     * Display the price list page
     *
     * @return \Illuminate\View\View
     */
    public function priceList()
    {
        // Get all sites
        $allSites = Site::all();

        // Reorder sites to ensure Warehouse (WHO) is first
        $warehouseSite = $allSites->where('site_id', 'WHO')->first();
        $otherSites = $allSites->where('site_id', '!=', 'WHO');

        // Create a new collection with Warehouse first
        $sites = collect([$warehouseSite])->merge($otherSites)->filter();

        return view('superadmin.price-list', compact('sites'));
    }

    /**
     * Get price list data for AJAX request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPriceListData(Request $request)
    {
        // Get parameters from request
        $siteId = $request->input('site_id', 'WHO');
        $search = $request->input('search', '');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 5);
        $partType = $request->input('part_type', 'all');

        // Different query logic based on site
        if ($siteId === 'WHO') {
            // For Warehouse, show all parts from the parts table
            $query = Part::query()
                ->select('parts.part_code', 'parts.part_name', 'parts.part_type', 'parts.price as base_price')
                ->with(['partInventories' => function($query) {
                    $query->select('part_inventories.part_inventory_id', 'part_inventories.part_code', 'part_inventories.site_id', 'part_inventories.price', 'part_inventories.site_part_name')
                        ->with('site:site_id,site_name');
                }]);

            // Apply search filter if provided
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('parts.part_name', 'like', "%{$search}%")
                      ->orWhere('parts.part_code', 'like', "%{$search}%");
                });
            }

            // Apply part type filter if provided
            if ($partType !== 'all') {
                $query->where('parts.part_type', $partType);
            }
        } else {
            // For other sites, only show parts that exist in that site's inventory
            $query = Part::query()
                ->select('parts.part_code', 'parts.part_name', 'parts.part_type', 'parts.price as base_price')
                ->join('part_inventories', 'parts.part_code', '=', 'part_inventories.part_code')
                ->where('part_inventories.site_id', $siteId)
                ->with(['partInventories' => function($query) use ($siteId) {
                    $query->select('part_inventories.part_inventory_id', 'part_inventories.part_code', 'part_inventories.site_id', 'part_inventories.price', 'part_inventories.site_part_name')
                        ->where('part_inventories.site_id', $siteId)
                        ->with('site:site_id,site_name');
                }]);

            // Apply search filter if provided
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('parts.part_name', 'like', "%{$search}%")
                      ->orWhere('parts.part_code', 'like', "%{$search}%")
                      ->orWhere('part_inventories.site_part_name', 'like', "%{$search}%");
                });
            }

            // Apply part type filter if provided
            if ($partType !== 'all') {
                $query->where('parts.part_type', $partType);
            }
        }

        // Get total count before pagination
        $total = $query->count();

        // Apply pagination
        $parts = $query->orderBy('parts.part_name')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        // Get all part types for filtering
        $partTypes = Part::distinct()->pluck('part_type')->filter()->values();

        // Format the response
        return response()->json([
            'parts' => $parts,
            'part_types' => $partTypes,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage)
            ]
        ]);
    }

    /**
     * Display the superadmin invoices page
     *
     * @return \Illuminate\View\View
     */
    public function invoices()
    {
        return view('superadmin.invoices');
    }

    /**
     * Get invoices data for the superadmin
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInvoicesData(Request $request)
    {
        // Get filter parameters
        $search = $request->input('search', '');
        $status = $request->input('status', '');
        $siteId = $request->input('site_id', '');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 10);
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        // Build query
        $query = Invoice::with(['unitTransactions.site']);

        // Apply search filter if provided
        if (!empty($search)) {
            $query->where(function($q) use ($search) {
                $q->where('no_invoice', 'like', "%{$search}%")
                  ->orWhere('customer', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if (!empty($status)) {
            if ($status === 'Jatuh Tempo') {
                // For 'Jatuh Tempo', we need to check due dates
                $query->where(function($q) {
                    $q->where('payment_status', '!=', 'Lunas')
                      ->where(function($q2) {
                          $q2->where('due_date', '<', now())
                            ->orWhereRaw('DATE_ADD(tanggal_invoice, INTERVAL 30 DAY) < NOW()');
                      });
                });
            } else {
                $query->where('payment_status', $status);
            }
        }

        // Apply site filter if provided
        if (!empty($siteId)) {
            if ($siteId === 'non-site') {
                // For non-site invoices, get invoices that don't have unit transactions
                $query->whereDoesntHave('unitTransactions');
            } else {
                // For specific site, get invoices that have unit transactions with that site
                $query->whereHas('unitTransactions', function($q) use ($siteId) {
                    $q->where('site_id', $siteId);
                });
            }
        }

        // Apply date range filter if provided
        if (!empty($startDate) && !empty($endDate)) {
            $query->whereBetween('tanggal_invoice', [$startDate, $endDate]);
        }

        // Get total count before pagination
        $total = $query->count();

        // Apply pagination and ordering
        $invoices = $query->orderBy('created_at', 'desc')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        // Process invoices to add site information and calculate totals
        $processedInvoices = $invoices->map(function($invoice) {
            // Get site information from the first unit transaction
            $site = null;
            if ($invoice->unitTransactions->isNotEmpty() && $invoice->unitTransactions->first()->site) {
                $site = [
                    'site_id' => $invoice->unitTransactions->first()->site->site_id,
                    'site_name' => $invoice->unitTransactions->first()->site->site_name
                ];
            }

            // Calculate subtotal, tax, and total
            $subtotal = $invoice->getSubtotalAttribute();
            $taxAmount = $invoice->getTaxAmountAttribute();
            $totalAmount = $invoice->getTotalAmountAttribute();

            return [
                'id' => $invoice->id,
                'no_invoice' => $invoice->no_invoice,
                'site' => $site,
                'customer' => $invoice->customer,
                'location' => $invoice->location,
                'tanggal_invoice' => $invoice->tanggal_invoice ? $invoice->tanggal_invoice->format('Y-m-d') : null,
                'due_date' => $invoice->due_date ? $invoice->due_date->format('Y-m-d') : null,
                'subtotal' => $subtotal,
                'ppn' => $invoice->ppn,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'payment_status' => $invoice->payment_status,
                'invoice_status' => $invoice->invoice_status,
                'signed_document_path' => $invoice->signed_document_path,
                'document_path' => $invoice->document_path,
                'unit_list' => $invoice->unit_list
            ];
        });

        // Get all sites for filtering (exclude warehouse)
        $sites = Site::where('site_id', '!=', 'WHO')->get(['site_id', 'site_name']);

        // Format the response
        return response()->json([
            'invoices' => $processedInvoices,
            'sites' => $sites,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage)
            ]
        ]);
    }

    /**
     * Get invoice details for the superadmin
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInvoiceDetails($id)
    {
        try {
            // Find the invoice with its unit transactions
            $invoice = Invoice::with([
                'unitTransactions.unit',
                'unitTransactions.parts.partInventory.part',
                'unitTransactions.site'
            ])->findOrFail($id);

            // Calculate subtotal, tax, and total
            $subtotal = $invoice->getSubtotalAttribute();
            $taxAmount = $invoice->getTaxAmountAttribute();
            $totalAmount = $invoice->getTotalAmountAttribute();

            // Format the response
            $response = [
                'id' => $invoice->id,
                'no_invoice' => $invoice->no_invoice,
                'customer' => $invoice->customer,
                'location' => $invoice->location,
                'sn' => $invoice->sn,
                'trouble' => $invoice->trouble,
                'lokasi' => $invoice->lokasi,
                'tanggal_invoice' => $invoice->tanggal_invoice ? $invoice->tanggal_invoice->format('Y-m-d') : null,
                'due_date' => $invoice->due_date ? $invoice->due_date->format('Y-m-d') : null,
                'ppn' => $invoice->ppn,
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'payment_status' => $invoice->payment_status,
                'payment_date' => $invoice->payment_date ? $invoice->payment_date->format('Y-m-d') : null,
                'payment_notes' => $invoice->payment_notes,
                'notes' => $invoice->notes,
                'signed_document_path' => $invoice->signed_document_path,
                'document_path' => $invoice->document_path,
                'unit_transactions' => $invoice->unitTransactions->map(function($transaction) {
                    return [
                        'id' => $transaction->id,
                        'unit' => $transaction->unit ? [
                            'id' => $transaction->unit->id,
                            'unit_code' => $transaction->unit->unit_code,
                            'unit_name' => $transaction->unit->unit_name
                        ] : null,
                        'site' => $transaction->site ? [
                            'site_id' => $transaction->site->site_id,
                            'site_name' => $transaction->site->site_name
                        ] : null,
                        'status' => $transaction->status,
                        'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
                        'parts' => $transaction->parts->map(function($part) {
                            return [
                                'id' => $part->id,
                                'part_name' => $part->partInventory && $part->partInventory->part ?
                                    $part->partInventory->part->part_name : 'Unknown Part',
                                'site_part_name' => $part->partInventory ? $part->partInventory->site_part_name : null,
                                'quantity' => $part->quantity,
                                'price' => $part->price,
                                'total' => $part->quantity * $part->price
                            ];
                        })
                    ];
                })
            ];

            return response()->json([
                'success' => true,
                'invoice' => $response
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get invoice details: ' . $e->getMessage()
            ], 500);
        }
    }
}
