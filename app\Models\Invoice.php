<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    protected $fillable = [
        'customer',
        'location',
        'no_invoice',
        'po_number',
        'sn',
        'trouble',
        'lokasi',
        'model_unit',
        'hmkm',
        'tanggal_invoice',
        'due_date',
        'ppn',
        'payment_status',
        'payment_date',
        'payment_notes',
        'notes',
        'transfer_to',
        'bank_account',
        'bank_branch',
        'signed_document_path',
        'document_path',
        'status',
        'penawaran_id',
        'site_id',
        'direct_subtotal'
    ];

    protected $casts = [
        'tanggal_invoice' => 'date',
        'due_date' => 'date',
        'payment_date' => 'date',
        'ppn' => 'float'
    ];

    protected $appends = ['invoice_status', 'subtotal', 'tax_amount', 'total_amount', 'unit_list'];

    /**
     * Get the invoice status based on payment status and due date
     */
    public function getInvoiceStatusAttribute()
    {
        // If payment status is 'Lunas', return 'Lunas'
        if ($this->payment_status === 'Lunas') {
            return 'Lunas';
        }

        // If due date is past and payment status is not 'Lunas',
        // mark as 'Jatuh Tempo'
        if ($this->due_date && $this->due_date->isPast()) {
            return 'Jatuh Tempo';
        }

        // If no due date is set, use the old logic (30 days from invoice date)
        if (!$this->due_date && $this->tanggal_invoice && $this->tanggal_invoice->addDays(30)->isPast()) {
            return 'Jatuh Tempo';
        }

        // Otherwise, return the current payment status
        return $this->payment_status;
    }

    /**
     * Get the unit transactions associated with this invoice
     */
    public function unitTransactions()
    {
        return $this->belongsToMany(UnitTransaction::class, 'invoice_unit_transactions')
            ->withTimestamps();
    }

    /**
     * Calculate the subtotal amount for this invoice
     */
    public function getSubtotalAttribute()
    {
        // If this is a direct invoice with a direct_subtotal value, return that
        if ($this->direct_subtotal !== null && $this->direct_subtotal !== '0.00') {
            // If direct_subtotal is set, return it directly
            return $this->direct_subtotal;
        }

        $subtotal = 0;

        // If this is a penawaran invoice
        if ($this->penawaran_id) {
            // Load the penawaran with its items if not already loaded
            if (!$this->relationLoaded('penawaran') || !$this->penawaran->relationLoaded('items')) {
                $this->load('penawaran.items');
            }

            // Calculate subtotal from penawaran items
            if ($this->penawaran) {
                foreach ($this->penawaran->items as $item) {
                    $subtotal += $item->price * $item->quantity;
                }
            }
        } else {
            // For unit transaction invoices
            // Make sure unit transactions are loaded with their parts
            $transactions = $this->unitTransactions()->with('parts')->get();

            foreach ($transactions as $transaction) {
                foreach ($transaction->parts as $part) {
                    $subtotal += $part->price * $part->quantity;
                }
            }
        }

        return $subtotal;
    }

    /**
     * Calculate the tax amount for this invoice
     */
    public function getTaxAmountAttribute()
    {
        return $this->subtotal * $this->ppn;
    }

    /**
     * Calculate the total amount after tax for this invoice
     */
    public function getTotalAmountAttribute()
    {
        return $this->subtotal + $this->tax_amount;
    }

    /**
     * Get a comma-separated list of unit codes for this invoice
     */
    public function getUnitListAttribute()
    {
        // If this is a direct invoice (no unit transactions and no penawaran)
        if (!$this->unitTransactions->count() && !$this->penawaran_id && isset($this->attributes['unit'])) {
            return $this->attributes['unit'];
        }

        // If this is a penawaran invoice
        if ($this->penawaran_id) {
            // Load the penawaran if not already loaded
            if (!$this->relationLoaded('penawaran')) {
                $this->load('penawaran');
            }

            // Return the penawaran number as the "unit list"
            return $this->penawaran ? 'Penawaran: ' . $this->penawaran->nomor : 'Penawaran';
        }

        // For unit transaction invoices
        return $this->unitTransactions->map(function($transaction) {
            return $transaction->unit ? $transaction->unit->unit_code : 'N/A';
        })->implode(', ');
    }

    /**
     * Get the penawaran associated with this invoice
     */
    public function penawaran()
    {
        return $this->belongsTo(Penawaran::class);
    }

    /**
     * Get the site associated with this invoice
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }
}
