import 'jquery';
import 'bootstrap';

function getIconByType(type) {
    const icons = {
        stock: "mdi-alert-circle-outline",
        part_request: "mdi-cart-plus",
        other: "mdi-information-outline",
    };
    return icons[type] || icons.other;
}


function getBgColorByType(type) {
    const colors = {
        stock: "bg-danger",
        part_request: "bg-danger",
        other: "bg-primary",
    };
    return colors[type] || colors.other;
}

async function fetchNotifications() {
    try {
        const response = await fetch("/notifications", {
            headers: {
                Accept: "application/json",
                "X-Requested-With": "XMLHttpRequest",
            },
        });
        const notifications = await response.json();
        const notificationList = document.getElementById("notification-list");
        notificationList.innerHTML = "";

        notifications.forEach((notification) => {
            const notificationItem = document.createElement("a");
            notificationItem.href = "javascript:void(0);";
            notificationItem.className = "dropdown-item notify-item";
            notificationItem.innerHTML = `
                            <a href="#" class="notification-link" data-id="${
                                notification.id
                            }" data-url="${notification.routes}">
                                <div class="notify-icon ${getBgColorByType(
                                    notification.type
                                )}">
                                    <i class="mdi ${getIconByType(
                                        notification.type
                                    )}"></i>
                                </div>
                                <p class="notify-details">${notification.title}
                                    <small class="text-muted">${
                                        notification.message
                                    }</small>
                                </p>
                            </a>`;

            notificationList.appendChild(notificationItem);
        });

        const badge = document.querySelector(".badgenotif");

        if (notifications.length > 0) {
            badge.textContent = notifications.length;
            badge.style.display = "inline-block";
        } else {
            badge.style.display = "none";
        }

        badge.textContent = notifications.length;
    } catch (error) {
        console.error("Error fetching notifications:", error);
    }
}
document.addEventListener("click", function (event) {
    if (event.target.closest(".notification-link")) {
        event.preventDefault();

        let notification = event.target.closest(".notification-link");
        let notificationId = notification.getAttribute("data-id");
        let redirectUrl = notification.getAttribute("data-url");

        fetch(`/notifications/delete/${notificationId}`, {
            method: "DELETE",
            headers: {
                "X-CSRF-TOKEN": document.querySelector(
                    'meta[name="csrf-token"]'
                ).content,
                "Content-Type": "application/json",
            },
        })
            .then((response) => response.json())
            .then((data) => {
                if (data.success) {
                    notification.closest(".notify-item").remove();
                }
                window.location.href = redirectUrl;
            })
            .catch((error) => {
                console.error("Error:", error);
                window.location.href = redirectUrl;
            });
    }
});

setInterval(fetchNotifications, 30000);

document.getElementById("clear-all").addEventListener("click", async (e) => {
    e.preventDefault();
    try {
        await fetch("/notifications/mark-read", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document.querySelector(
                    'meta[name="csrf-token"]'
                ).content,
                "X-Requested-With": "XMLHttpRequest",
            },
        });
        await fetchNotifications();
    } catch (error) {
        console.error("Error clearing notifications:", error);
    }
});

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dropdowns using jQuery
    $('.dropdown-toggle').dropdown();
    
    // Your existing fetchNotifications call
    fetchNotifications();
});
