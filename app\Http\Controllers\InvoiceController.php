<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\UnitTransaction;
use App\Helpers\LogHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class InvoiceController extends Controller
{
    /**
     * Get invoice data for a unit transaction
     */
    public function getInvoice($transactionId)
    {
        // Verify the unit transaction exists
        $transaction = UnitTransaction::findOrFail($transactionId);

        // Check if this transaction is already invoiced
        $isInvoiced = $transaction->isInvoiced();
        $invoice = null;

        if ($isInvoiced) {
            // Get the invoice through the relationship
            $invoice = $transaction->getLatestInvoice();
        }

        // Generate next invoice number if no invoice exists
        $nextInvoiceNumber = '';
        if (!$invoice) {
            $nextInvoiceNumber = $this->generateNextInvoiceNumber();
        }

        return response()->json([
            'success' => true,
            'invoice' => $invoice,
            'exists' => $invoice !== null,
            'next_invoice_number' => $nextInvoiceNumber,
            'is_invoiced' => $isInvoiced
        ]);
    }

    /**
     * Check if transactions are already invoiced
     */
    public function checkTransactionsInvoiced(Request $request)
    {
        $request->validate([
            'transaction_ids' => 'required|array',
            'transaction_ids.*' => 'required|exists:unit_transactions,id'
        ]);

        $transactionIds = $request->transaction_ids;
        $invoicedTransactions = [];

        // Check which transactions are already invoiced
        foreach ($transactionIds as $id) {
            $transaction = UnitTransaction::find($id);
            if ($transaction && $transaction->isInvoiced()) {
                $invoicedTransactions[] = [
                    'id' => $transaction->id,
                    'unit_code' => $transaction->unit->unit_code ?? 'Unknown',
                    'invoice_number' => $transaction->getLatestInvoice()->no_invoice ?? 'Unknown'
                ];
            }
        }

        return response()->json([
            'success' => true,
            'invoiced_transactions' => $invoicedTransactions,
            'has_invoiced' => count($invoicedTransactions) > 0
        ]);
    }

    /**
     * Get the latest invoice
     */
    public function getLatestInvoice()
    {
        // Find the latest invoice
        $latestInvoice = Invoice::orderBy('created_at', 'desc')->first();

        return response()->json([
            'success' => true,
            'latest_invoice' => $latestInvoice
        ]);
    }

    /**
     * Generate the next invoice number based on the last invoice
     */
    private function generateNextInvoiceNumber()
    {
        // Get the current month and year
        $month = Carbon::now()->format('m');
        $year = Carbon::now()->format('Y');

        // Find the latest invoice
        $latestInvoice = Invoice::orderBy('created_at', 'desc')->first();

        if ($latestInvoice) {
            // Extract the numeric part before the first slash
            $parts = explode('/', $latestInvoice->no_invoice);
            $numericPart = (int) $parts[0];

            // Increment the numeric part
            $nextNumericPart = $numericPart + 1;

            // Pad the numeric part to the same length as the original
            $originalLength = strlen($parts[0]);
            $paddedNumericPart = str_pad($nextNumericPart, $originalLength, '0', STR_PAD_LEFT);

            // Keep the rest of the format exactly as it was
            $restOfFormat = substr($latestInvoice->no_invoice, strpos($latestInvoice->no_invoice, '/'));

            // Return the new invoice number with the same format as the last one
            return $paddedNumericPart . $restOfFormat;
        } else {
            // Start with 001 if no invoice exists (default format)
            return sprintf('%03d/INV-PWB/%s/%s', 1, $month, $year);
        }
    }

    /**
     * Get invoice details with unit transactions
     */
    public function getInvoiceDetails($id)
    {
        try {
            // Log the request
            Log::info('Getting invoice details for ID: ' . $id);

            // Find the invoice
            $invoice = Invoice::findOrFail($id);

            Log::info('Invoice found: ' . $invoice->id);

            // Check if this is a direct invoice (with direct_subtotal)
            $isDirectInvoice = !empty($invoice->direct_subtotal);

            if ($isDirectInvoice) {
                // For direct invoices, we don't need to load unit transactions
                Log::info('This is a direct invoice with subtotal: ' . $invoice->direct_subtotal);

                // Load site if site_id is present
                if ($invoice->site_id) {
                    $invoice->load('site');
                }

                // Set calculated values for direct invoice
                $subtotal = $invoice->direct_subtotal;
            } else {
                // For regular invoices with unit transactions or penawaran
                try {
                    $invoice->load([
                        'unitTransactions.unit',
                        'unitTransactions.parts.partInventory.part',
                        'unitTransactions.site',
                        'penawaran.items.partInventory.part'
                    ]);
                } catch (\Exception $loadException) {
                    Log::error('Error loading invoice relations: ' . $loadException->getMessage());
                    // Continue with what we have
                }

                // Make sure unitTransactions is loaded
                if (!$invoice->relationLoaded('unitTransactions')) {
                    Log::warning('Unit transactions relation not loaded for invoice ' . $id);
                    try {
                        $invoice->load('unitTransactions.unit', 'unitTransactions.parts.partInventory.part', 'unitTransactions.site');
                    } catch (\Exception $loadException) {
                        Log::error('Error loading unit transactions: ' . $loadException->getMessage());
                        // Initialize empty collection if loading fails
                        $invoice->setRelation('unitTransactions', collect([]));
                    }
                }

                // Make sure each unit transaction has its unit and site loaded
                foreach ($invoice->unitTransactions as $transaction) {
                    try {
                        if (!$transaction->relationLoaded('unit') && $transaction->unit_id) {
                            $transaction->load('unit');
                        }

                        if (!$transaction->relationLoaded('site') && $transaction->site_id) {
                            $transaction->load('site');
                        }
                    } catch (\Exception $loadException) {
                        Log::error('Error loading transaction relations: ' . $loadException->getMessage());
                        // Continue with what we have
                    }
                }

                // Ensure unitTransactions is not null
                if ($invoice->unitTransactions === null) {
                    Log::warning('Unit transactions is null for invoice ' . $id);
                    $invoice->setRelation('unitTransactions', collect([]));
                }

                Log::info('Invoice has ' . $invoice->unitTransactions->count() . ' transactions');

                // Manually calculate the totals to ensure they're correct
                $subtotal = 0;

                // If this is a penawaran invoice
                if ($invoice->penawaran_id && $invoice->penawaran) {
                    foreach ($invoice->penawaran->items as $item) {
                        $subtotal += $item->price * $item->quantity;
                    }
                } else {
                    // For unit transaction invoices
                    foreach ($invoice->unitTransactions as $transaction) {
                        // Make sure parts is loaded and not null
                        if ($transaction->relationLoaded('parts') && $transaction->parts !== null) {
                            foreach ($transaction->parts as $part) {
                                $subtotal += $part->price * $part->quantity;
                            }
                        }
                    }
                }
            }

            // Add the calculated values to the response
            $invoice->calculated_subtotal = $subtotal;
            $invoice->calculated_tax = $subtotal * $invoice->ppn;
            $invoice->calculated_total = $subtotal + ($subtotal * $invoice->ppn);

            // Format dates consistently for the frontend
            try {
                if ($invoice->tanggal_invoice) {
                    $invoice->tanggal_invoice = $invoice->tanggal_invoice->format('Y-m-d');
                }

                if ($invoice->due_date) {
                    $invoice->due_date = $invoice->due_date->format('Y-m-d');
                }
            } catch (\Exception $dateException) {
                Log::error('Error formatting dates: ' . $dateException->getMessage());
                // Continue with unformatted dates
            }

            Log::info('Returning invoice details successfully');

            return response()->json([
                'success' => true,
                'invoice' => $invoice
            ]);
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error getting invoice details: ' . $e->getMessage());
            Log::error('Invoice ID: ' . $id);
            Log::error($e->getTraceAsString());

            // Check if it's a ModelNotFoundException (invoice not found)
            if ($e instanceof \Illuminate\Database\Eloquent\ModelNotFoundException) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invoice tidak ditemukan. Mungkin telah dihapus atau ID tidak valid.',
                    'error' => 'Invoice not found',
                    'error_type' => 'not_found'
                ], 404);
            }

            // Check if it's a database-related error
            if ($e instanceof \Illuminate\Database\QueryException) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan database saat memuat invoice. Silakan coba lagi.',
                    'error' => $e->getMessage(),
                    'error_type' => 'database_error'
                ], 500);
            }

            // Return error response for other types of errors
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'error_type' => 'general_error',
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Store a new invoice for multiple unit transactions
     */
    public function store(Request $request)
    {
        try {
            // Validate the request
            $request->validate([
                'transaction_ids' => 'required|array',
                'transaction_ids.*' => 'required|exists:unit_transactions,id',
                'customer' => 'nullable|string|max:255',
                'location' => 'nullable|string|max:255',
                'no_invoice' => 'nullable|string|max:255',
                'sn' => 'nullable|string|max:255',
                'trouble' => 'nullable|string',
                'lokasi' => 'nullable|string|max:255',
                'tanggal_invoice' => 'nullable|date',
                'due_date' => 'nullable|date|after:tanggal_invoice',
                'ppn' => 'nullable|numeric',
                'notes' => 'nullable|string',
                'signed_document' => 'nullable|file|max:10240|mimes:pdf,jpg,jpeg,png,doc,docx'
            ], [
                'signed_document.max' => 'Ukuran file tidak boleh lebih dari 10MB',
                'signed_document.mimes' => 'Format file harus PDF, JPG, JPEG, PNG, DOC, atau DOCX'
            ]);

            // Always set ppn to 0.11 regardless of what was submitted
            $request->merge(['ppn' => 0.11]);

            // Start a database transaction
            DB::beginTransaction();

            try {
                // Check if any of the transactions are already invoiced
                $transactionIds = $request->transaction_ids;
                $alreadyInvoiced = [];

                foreach ($transactionIds as $id) {
                    $transaction = UnitTransaction::find($id);
                    if ($transaction && $transaction->isInvoiced()) {
                        $alreadyInvoiced[] = [
                            'id' => $transaction->id,
                            'unit_code' => $transaction->unit->unit_code ?? 'Unknown',
                            'invoice_number' => $transaction->getLatestInvoice()->no_invoice ?? 'Unknown'
                        ];
                    }
                }

                if (count($alreadyInvoiced) > 0) {
                    // Some transactions are already invoiced
                    DB::rollBack();
                    return response()->json([
                        'success' => false,
                        'message' => 'Beberapa transaksi sudah memiliki invoice',
                        'invoiced_transactions' => $alreadyInvoiced
                    ], 422);
                }

                // Handle file upload if present
                $documentPath = null;
                if ($request->hasFile('signed_document')) {
                    $file = $request->file('signed_document');
                    $fileName = time() . '_' . $file->getClientOriginalName();
                    $file->move(public_path('assets/invoice_documents'), $fileName);
                    $documentPath = $fileName;
                }

                // Create new invoice
                $invoice = Invoice::create([
                    'customer' => $request->customer,
                    'location' => $request->location,
                    'no_invoice' => $request->no_invoice,
                    'sn' => $request->sn,
                    'trouble' => $request->trouble ?: 'Part/AC', // Default to "Part/AC" if empty
                    'lokasi' => $request->lokasi,
                    'tanggal_invoice' => $request->tanggal_invoice,
                    'due_date' => $request->due_date,
                    'ppn' => 0.11,
                    'notes' => $request->notes,
                    'document_path' => $documentPath
                ]);

                // Associate all transactions with this invoice
                foreach ($transactionIds as $id) {
                    $transaction = UnitTransaction::findOrFail($id);

                    // Attach the transaction to the invoice
                    $invoice->unitTransactions()->attach($transaction->id);

                    // Set transaction status to 'Selesai'
                    $transaction->status = 'Selesai';
                    $transaction->save();
                }

                // Commit the transaction
                DB::commit();

                $message = 'Invoice berhasil dibuat';
                $logAction = 'Create Invoice';
                $logDescription = 'Invoice baru dibuat untuk ' . count($transactionIds) . ' transaksi unit';

                // Log the action
                LogHelper::createLog(
                    $logAction,
                    $logDescription,
                    'Invoice',
                    $request
                );

                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'invoice' => $invoice,
                    'transaction_ids' => $transactionIds
                ]);
            } catch (\Exception $innerException) {
                // Roll back the transaction
                DB::rollBack();
                throw $innerException;
            }
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error creating invoice: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Update an existing invoice
     */
    public function update(Request $request, $id)
    {
        try {
            // Check if this is a direct invoice (without unit transactions)
            $isDirectInvoice = $request->has('unit') && $request->unit === 'Manual';

            if ($isDirectInvoice) {
                // Validation for direct invoice
                $request->validate([
                    'customer' => 'required|string|max:255',
                    'site_id' => 'nullable|exists:sites,id',
                    'unit' => 'required|string|max:255',
                    'location' => 'nullable|string|max:255',
                    'no_invoice' => 'nullable|string|max:255',
                    'po_number' => 'nullable|string|max:255',
                    'sn' => 'nullable|string|max:255',
                    'trouble' => 'nullable|string',
                    'lokasi' => 'nullable|string|max:255',
                    'tanggal_invoice' => 'required|date',
                    'due_date' => 'nullable|date|after_or_equal:tanggal_invoice',
                    'subtotal' => 'required|numeric|min:0',
                    'ppn' => 'nullable|numeric|min:0|max:100',
                    'notes' => 'nullable|string',
                    'document' => 'nullable|file|max:10240|mimes:pdf,jpg,jpeg,png,doc,docx'
                ], [
                    'customer.required' => 'Nama customer harus diisi',
                    'unit.required' => 'Unit harus diisi',
                    'tanggal_invoice.required' => 'Tanggal invoice harus diisi',
                    'subtotal.required' => 'Subtotal harus diisi',
                    'subtotal.min' => 'Subtotal tidak boleh kurang dari 0',
                    'document.max' => 'Ukuran file tidak boleh lebih dari 10MB',
                    'document.mimes' => 'Format file harus PDF, JPG, JPEG, PNG, DOC, atau DOCX'
                ]);
            } else {
                // Validation for regular invoice with unit transactions
                $request->validate([
                    'transaction_ids' => 'required|array',
                    'transaction_ids.*' => 'required|exists:unit_transactions,id',
                    'customer' => 'nullable|string|max:255',
                    'location' => 'nullable|string|max:255',
                    'no_invoice' => 'nullable|string|max:255',
                    'sn' => 'nullable|string|max:255',
                    'trouble' => 'nullable|string',
                    'lokasi' => 'nullable|string|max:255',
                    'tanggal_invoice' => 'nullable|date',
                    'due_date' => 'nullable|date|after:tanggal_invoice',
                    'ppn' => 'nullable|numeric',
                    'notes' => 'nullable|string',
                    'signed_document' => 'nullable|file|max:10240|mimes:pdf,jpg,jpeg,png,doc,docx'
                ], [
                    'signed_document.max' => 'Ukuran file tidak boleh lebih dari 10MB',
                    'signed_document.mimes' => 'Format file harus PDF, JPG, JPEG, PNG, DOC, atau DOCX'
                ]);
            }

            // Find the invoice
            $invoice = Invoice::findOrFail($id);

            // Check if the invoice is already paid
            if ($invoice->payment_status === 'Lunas') {
                return response()->json([
                    'success' => false,
                    'message' => 'Invoice yang sudah lunas tidak dapat diubah'
                ], 422);
            }

            // Start a database transaction
            DB::beginTransaction();

            try {
                if ($isDirectInvoice) {
                    // Handle file upload if present for direct invoice
                    $documentPath = $invoice->document_path; // Keep existing document by default
                    if ($request->hasFile('document')) {
                        // Delete old file if it exists
                        if ($documentPath && file_exists(public_path('assets/invoice_documents/' . $documentPath))) {
                            unlink(public_path('assets/invoice_documents/' . $documentPath));
                        }

                        // Upload new file
                        $file = $request->file('document');
                        $fileName = time() . '_' . $file->getClientOriginalName();
                        $file->move(public_path('assets/invoice_documents'), $fileName);
                        $documentPath = $fileName;
                    }

                    // Convert PPN from percentage to decimal (e.g., 11% to 0.11)
                    // Make sure ppn is between 0 and 100
                    $ppn = $request->ppn ?? 11;
                    $ppn = max(0, min(100, $ppn)); // Clamp between 0 and 100
                    $ppnDecimal = $ppn / 100;

                    // Update the direct invoice
                    $invoice->update([
                        'customer' => $request->customer,
                        'site_id' => $request->site_id,
                        'location' => $request->location,
                        'no_invoice' => $request->no_invoice,
                        'po_number' => $request->po_number,
                        'sn' => $request->sn,
                        'trouble' => $request->trouble ?: 'Part/AC', // Default to "Part/AC" if empty
                        'lokasi' => $request->lokasi,
                        'model_unit' => $request->model_unit,
                        'hmkm' => $request->hmkm,
                        'tanggal_invoice' => $request->tanggal_invoice,
                        'due_date' => $request->due_date,
                        'ppn' => $ppnDecimal,
                        'notes' => $request->notes,
                        'document_path' => $documentPath,
                        'direct_subtotal' => $request->subtotal
                    ]);

                    // Commit the transaction
                    DB::commit();

                    // Log the action
                    LogHelper::createLog(
                        'Update Direct Invoice',
                        'Invoice manual ' . $invoice->no_invoice . ' diperbarui',
                        'Invoice',
                        $request
                    );

                    return response()->json([
                        'success' => true,
                        'message' => 'Invoice berhasil diperbarui',
                        'invoice' => $invoice
                    ]);
                } else {
                    // Handle file upload if present for regular invoice
                    $documentPath = $invoice->document_path; // Keep existing document by default
                    if ($request->hasFile('signed_document')) {
                        // Delete old file if it exists
                        if ($documentPath && file_exists(public_path('assets/invoice_documents/' . $documentPath))) {
                            unlink(public_path('assets/invoice_documents/' . $documentPath));
                        }

                        // Upload new file
                        $file = $request->file('signed_document');
                        $fileName = time() . '_' . $file->getClientOriginalName();
                        $file->move(public_path('assets/invoice_documents'), $fileName);
                        $documentPath = $fileName;
                    }

                    // Update the invoice
                    $invoice->update([
                        'customer' => $request->customer,
                        'location' => $request->location,
                        'no_invoice' => $request->no_invoice,
                        'sn' => $request->sn,
                        'trouble' => $request->trouble ?: 'Part/AC', // Default to "Part/AC" if empty
                        'lokasi' => $request->lokasi,
                        'tanggal_invoice' => $request->tanggal_invoice,
                        'due_date' => $request->due_date,
                        'ppn' => 0.11,
                        'notes' => $request->notes,
                        'document_path' => $documentPath
                    ]);

                    // Get the current transaction IDs associated with this invoice
                    $currentTransactionIds = $invoice->unitTransactions->pluck('id')->toArray();

                    // Get the new transaction IDs from the request
                    $newTransactionIds = $request->transaction_ids;

                    // Transactions to detach (remove from invoice)
                    $transactionsToDetach = array_diff($currentTransactionIds, $newTransactionIds);

                    // Transactions to attach (add to invoice)
                    $transactionsToAttach = array_diff($newTransactionIds, $currentTransactionIds);

                    // Check if any of the transactions to attach are already invoiced
                    $alreadyInvoicedTransactions = UnitTransaction::whereIn('id', $transactionsToAttach)
                        ->whereHas('invoices', function($query) use ($invoice) {
                            $query->where('invoices.id', '!=', $invoice->id);
                        })
                        ->get();

                    if ($alreadyInvoicedTransactions->count() > 0) {
                        $unitCodes = $alreadyInvoicedTransactions->map(function($transaction) {
                            return $transaction->unit ? $transaction->unit->unit_code : 'Unknown';
                        })->implode(', ');

                        return response()->json([
                            'success' => false,
                            'message' => 'Beberapa unit sudah terdaftar di invoice lain: ' . $unitCodes
                        ], 422);
                    }

                    // Detach transactions that are no longer associated with this invoice
                    if (!empty($transactionsToDetach)) {
                        $invoice->unitTransactions()->detach($transactionsToDetach);

                        // Update the status of detached transactions
                        UnitTransaction::whereIn('id', $transactionsToDetach)->update(['status' => 'Ready PO']);
                    }

                    // Attach new transactions to this invoice
                    if (!empty($transactionsToAttach)) {
                        try {
                            $invoice->unitTransactions()->attach($transactionsToAttach);

                            // Update the status of attached transactions
                            UnitTransaction::whereIn('id', $transactionsToAttach)->update(['status' => 'Selesai']);
                        } catch (\Exception $e) {
                            // Log the error
                            Log::error('Error attaching transactions to invoice: ' . $e->getMessage());

                            // Check if the error is due to duplicate entry
                            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                                return response()->json([
                                    'success' => false,
                                    'message' => 'Beberapa unit sudah terdaftar di invoice lain. Silakan refresh halaman dan coba lagi.'
                                ], 422);
                            }

                            // Re-throw the exception for other errors
                            throw $e;
                        }
                    }

                    // Commit the transaction
                    DB::commit();

                    // Log the action
                    LogHelper::createLog(
                        'Update Invoice',
                        'Invoice ' . $invoice->no_invoice . ' diperbarui',
                        'Invoice',
                        $request
                    );

                    return response()->json([
                        'success' => true,
                        'message' => 'Invoice berhasil diperbarui',
                        'invoice' => $invoice
                    ]);
                }
            } catch (\Exception $innerException) {
                // Roll back the transaction
                DB::rollBack();
                throw $innerException;
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a new direct invoice without unit transactions
     */
    public function storeDirectInvoice(Request $request)
    {
        try {
            // Validate the request
            $request->validate([
                'customer' => 'required|string|max:255',
                'site_id' => 'nullable|exists:sites,id',
                'unit' => 'required|string|max:255',
                'location' => 'nullable|string|max:255',
                'no_invoice' => 'nullable|string|max:255',
                'po_number' => 'nullable|string|max:255',
                'sn' => 'nullable|string|max:255',
                'trouble' => 'nullable|string',
                'lokasi' => 'nullable|string|max:255',
                'model_unit' => 'nullable|string|max:255',
                'hmkm' => 'nullable|string|max:255',
                'tanggal_invoice' => 'required|date',
                'due_date' => 'nullable|date|after_or_equal:tanggal_invoice',
                'subtotal' => 'required|numeric|min:0',
                'ppn' => 'nullable|numeric|min:0|max:100',
                'transfer_to' => 'nullable|string|max:255',
                'bank_account' => 'nullable|string|max:255',
                'bank_branch' => 'nullable|string|max:255',
                'notes' => 'nullable|string',
                'document' => 'nullable|file|max:10240|mimes:pdf,jpg,jpeg,png,doc,docx'
            ], [
                'customer.required' => 'Nama customer harus diisi',
                'unit.required' => 'Unit harus diisi',
                'tanggal_invoice.required' => 'Tanggal invoice harus diisi',
                'subtotal.required' => 'Subtotal harus diisi',
                'subtotal.min' => 'Subtotal tidak boleh kurang dari 0',
                'document.max' => 'Ukuran file tidak boleh lebih dari 10MB',
                'document.mimes' => 'Format file harus PDF, JPG, JPEG, PNG, DOC, atau DOCX'
            ]);

            // Start a database transaction
            DB::beginTransaction();

            try {
                // Generate invoice number if not provided
                $invoiceNumber = $request->no_invoice;
                if (empty($invoiceNumber)) {
                    $invoiceNumber = $this->generateNextInvoiceNumber();
                }

                // Convert PPN from percentage to decimal (e.g., 11% to 0.11)
                // Make sure ppn is between 0 and 100
                $ppn = $request->ppn ?? 11;
                $ppn = max(0, min(100, $ppn)); // Clamp between 0 and 100
                $ppnDecimal = $ppn / 100;

                // Handle file upload if present
                $documentPath = null;
                if ($request->hasFile('document')) {
                    $file = $request->file('document');
                    $fileName = time() . '_' . $file->getClientOriginalName();
                    $file->move(public_path('assets/invoice_documents'), $fileName);
                    $documentPath = $fileName;
                }

                // Set due date if not provided (default to 60 days after invoice date)
                $dueDate = $request->due_date;
                if (empty($dueDate) && !empty($request->tanggal_invoice)) {
                    $dueDate = Carbon::parse($request->tanggal_invoice)->addDays(60)->format('Y-m-d');
                }

                // Create new invoice
                $invoice = Invoice::create([
                    'customer' => $request->customer,
                    'site_id' => $request->site_id,
                    'location' => $request->location,
                    'no_invoice' => $invoiceNumber,
                    'po_number' => $request->po_number,
                    'sn' => $request->sn,
                    'trouble' => $request->trouble ?: 'Part/AC', // Default to "Part/AC" if empty
                    'lokasi' => $request->lokasi,
                    'model_unit' => $request->model_unit,
                    'hmkm' => $request->hmkm,
                    'tanggal_invoice' => $request->tanggal_invoice,
                    'due_date' => $dueDate,
                    'ppn' => $ppnDecimal,
                    'payment_status' => 'Belum Lunas',
                    'status' => 'Draft',
                    'transfer_to' => $request->transfer_to,
                    'bank_account' => $request->bank_account,
                    'bank_branch' => $request->bank_branch,
                    'notes' => $request->notes,
                    'document_path' => $documentPath,
                    // Store the direct subtotal in a meta field or notes
                    'direct_subtotal' => $request->subtotal
                ]);

                // Commit the transaction
                DB::commit();

                $message = 'Invoice langsung berhasil dibuat';
                $logAction = 'Create Direct Invoice';
                $logDescription = 'Invoice langsung baru dibuat untuk unit ' . $request->unit;

                // Log the action
                LogHelper::createLog(
                    $logAction,
                    $logDescription,
                    'Invoice',
                    $request
                );

                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'invoice' => $invoice
                ]);
            } catch (\Exception $innerException) {
                // Roll back the transaction
                DB::rollBack();
                throw $innerException;
            }
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error creating direct invoice: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat invoice: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all invoices including direct invoices
     */
    public function allInvoices(Request $request)
    {
        try {
            // Get date range from request
            $dateFrom = $request->input('date_from');
            $dateTo = $request->input('date_to');
            $search = $request->input('search', '');
            $perPage = $request->input('per_page', 10);
            $page = $request->input('page', 1);
            $sortField = $request->input('sort_field', 'no_invoice');
            $sortDirection = $request->input('sort_direction', 'asc');

            // Start query builder
            $query = Invoice::query()
                ->with(['site', 'unitTransactions.unit', 'penawaran']);

            // Apply date filter if provided
            if ($dateFrom && $dateTo) {
                $query->whereBetween('tanggal_invoice', [$dateFrom, $dateTo]);
            }

            // Apply search filter if provided
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('no_invoice', 'like', "%{$search}%")
                      ->orWhere('customer', 'like', "%{$search}%")
                      ->orWhere('po_number', 'like', "%{$search}%")
                      ->orWhere('sn', 'like', "%{$search}%")
                      ->orWhere('payment_status', 'like', "%{$search}%");
                });
            }

            // Apply sorting
            $query->orderBy($sortField, $sortDirection);

            // Get paginated results
            $invoices = $query->paginate($perPage, ['*'], 'page', $page);

            // Add additional information to each invoice
            $invoices->getCollection()->transform(function ($invoice) {
                // Add site name
                $invoice->site_name = $invoice->site ? $invoice->site->site_name : null;

                // Add unit list for display
                if ($invoice->direct_subtotal) {
                    // This is a direct invoice
                    $invoice->unit_list = 'Manual';
                } else if ($invoice->unitTransactions && $invoice->unitTransactions->count() > 0) {
                    // This is a regular invoice with unit transactions
                    $unitCodes = $invoice->unitTransactions->map(function($transaction) {
                        return $transaction->unit ? $transaction->unit->unit_code : 'Unknown';
                    })->unique()->implode(', ');
                    $invoice->unit_list = $unitCodes;
                } else if ($invoice->penawaran) {
                    // This is an invoice from penawaran
                    $invoice->unit_list = $invoice->penawaran->unit_name ?? 'Penawaran';
                } else {
                    $invoice->unit_list = $invoice->unit ?? 'Unknown';
                }

                // Calculate due status
                $dueStatus = 'Normal';
                $dueStatusClass = 'success';

                if ($invoice->due_date) {
                    $today = now();
                    $dueDate = Carbon::parse($invoice->due_date);

                    // If due date has passed and payment status is not "Lunas"
                    if ($dueDate < $today && $invoice->payment_status !== 'Lunas') {
                        $dueStatus = 'Jatuh Tempo';
                        $dueStatusClass = 'danger';
                    }
                }

                $invoice->due_status = $dueStatus;
                $invoice->due_status_class = $dueStatusClass;

                // Format dates for display
                if ($invoice->tanggal_invoice) {
                    $invoice->formatted_invoice_date = Carbon::parse($invoice->tanggal_invoice)->format('d-m-Y');
                }

                if ($invoice->due_date) {
                    $invoice->formatted_due_date = Carbon::parse($invoice->due_date)->format('d-m-Y');
                }

                return $invoice;
            });

            // Add date range to the response
            $invoices->date_from = $dateFrom;
            $invoices->date_to = $dateTo;

            // Preserve query parameters in pagination links
            $invoices->appends($request->only(['search', 'date_from', 'date_to', 'sort_field', 'sort_direction']));

            return response()->json($invoices);
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error getting all invoices: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete an invoice and reset associated unit transactions
     */
    public function destroy($id)
    {
        try {
            // Find the invoice
            $invoice = Invoice::findOrFail($id);

            // Check if the invoice is already paid
            if ($invoice->payment_status === 'Lunas') {
                return response()->json([
                    'success' => false,
                    'message' => 'Invoice yang sudah lunas tidak dapat dihapus'
                ], 422);
            }

            // Start a database transaction
            DB::beginTransaction();

            try {
                // Get the unit transactions associated with this invoice
                $unitTransactions = $invoice->unitTransactions;

                // Detach all unit transactions from this invoice
                $invoice->unitTransactions()->detach();

                // Update the status of all detached transactions to 'Ready PO'
                foreach ($unitTransactions as $transaction) {
                    $transaction->status = 'Ready PO';
                    $transaction->save();
                }

                // Delete the document file if it exists
                if ($invoice->document_path && file_exists(public_path('assets/invoice_documents/' . $invoice->document_path))) {
                    unlink(public_path('assets/invoice_documents/' . $invoice->document_path));
                }

                // Delete the invoice
                $invoice->delete();

                // Commit the transaction
                DB::commit();

                // Log the action
                LogHelper::createLog(
                    'Delete Invoice',
                    'Invoice ' . $invoice->no_invoice . ' dihapus',
                    'Invoice',
                    request()
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Invoice berhasil dihapus dan status unit transaction dikembalikan menjadi Ready PO'
                ]);
            } catch (\Exception $innerException) {
                // Roll back the transaction
                DB::rollBack();
                throw $innerException;
            }
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error deleting invoice: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus invoice: ' . $e->getMessage()
            ], 500);
        }
    }
}
