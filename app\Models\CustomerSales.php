<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerSales extends Model
{
    use HasFactory;

    protected $table = 'customersales';

    protected $fillable = [
        'kode',
        'nama_customer',
        'alamat',
        'total_pending',
    ];

    // Relationship with Penawaran
    public function penawarans()
    {
        return $this->hasMany(Penawaran::class, 'customer', 'nama_customer');
    }
}
