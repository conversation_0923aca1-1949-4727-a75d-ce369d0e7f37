@extends('sales.contentsales')
@section('title', 'Semua Invoices')
@section('resourcesales')
@vite(['resources/js/sales/all-invoices.js'])
<style>
    .w-fit-content {
        width: fit-content;
    }
    .shadow-kit {
        border: 1px solid rgb(42, 105, 168);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-radius: 0.5rem;
        background-color: #fff;
    }
    .btn{
        font-size: 11px;
    }
    .btn-warning{
        background-color:rgb(242, 215, 132);
        color: #000;
    }
    .bg-warning{
        background-color:rgb(240, 250, 150);
        color: #0f0187;
    }

    /* Sortable table styles */
    .sortable {
        cursor: pointer;
        position: relative;
    }

    .sortable:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .sort-icon {
        font-size: 11px;
        margin-left: 5px;
        opacity: 0.5;
    }

    .sortable.asc .sort-icon {
        opacity: 1;
        transform: rotate(180deg);
    }

    .sortable.desc .sort-icon {
        opacity: 1;
        transform: rotate(0deg);
    }

    /* Fix for dropdown z-index issues */
    .dropdown-menu {
        z-index: 9999 !important;
        position: absolute !important;
        display: none;
        top: 100%;
        left: 0;
        min-width: 10rem;
        margin-top: 0.125rem;
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 0.25rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
    }

    .dropdown-menu.show {
        display: block;
    }

    .status-dropdown {
        position: relative !important;
    }

    .table-responsive {
        overflow: initial !important;
    }

    /* Ensure table cells can contain dropdowns properly */
    .table td {
        position: relative;
    }

    /* Custom dropdown styling */
    .dropdown-item {
        padding: 0.25rem 1rem;
        font-size: 11px;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
    }

    .dropdown-item.active {
        background-color: #225297;
        color: white;
    }
</style>
@endsection
@section('contentsales')

<div class="bgwhite mb-2 p-2 pr-2">
    <div class="d-flex justify-content-right">
        <div class="nav-links">
            <a href="{{ route('sales.dashboard') }}" class="btn btn-sm {{ request()->routeIs('sales.dashboard') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-view-dashboard"></i> Dashboard
            </a>
            <a href="{{ route('sales.penawaran') }}" class="btn btn-sm {{ request()->routeIs('sales.penawaran') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-file-document-edit"></i> Buat Penawaran
            </a>
            <a href="{{ route('sales.invoices') }}" class="btn btn-sm {{ request()->routeIs('sales.invoices') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-file-check"></i> Invoices Unit
            </a>
            <a href="{{ route('sales.all-invoices') }}" class="btn btn-sm {{ request()->routeIs('sales.all-invoices') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-file-document"></i> Semua Invoices
            </a>
            <a href="{{ route('sales.jasa-karyawan') }}" class="btn btn-sm {{ request()->routeIs('sales.jasa-karyawan') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-account-cash"></i>Monthly Report
            </a>
            <a class="btn btn-sm btn-secondary mx-1" href="{{ route(name: 'logout') }}">
                <i class="mdi mdi-logout-variant"></i>
                <span> Logout</span>
            </a>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="bgwhite shadow-kit rounded-lg">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 mr-3 font-bold text-uppercase text-white">Semua Daftar Invoice</h5>
                            <button id="add-direct-invoice-btn" class="btn btn-sm btn-light ml-2">
                                <i class="mdi mdi-plus"></i> Tambah Invoice Langsung
                            </button>
                        </div>
                        <div class="d-flex">
                            <div class="input-group input-group-sm mr-2">
                                <input type="date" id="date-from" class="form-control form-control-sm">
                            </div>
                            <div class="input-group input-group-sm mr-2">
                                <input type="date" id="date-to" class="form-control form-control-sm">
                            </div>
                            <button id="filter-button" class="btn btn-sm btn-light mr-2">Filter</button>
                            <input type="text" id="search-input" class="form-control form-control-sm" placeholder="Search...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" style="overflow: initial;">
                            <table class="table table-bordered w-100" id="invoices-table" style="font-size: 11px;">
                                <thead class="bg-light">
                                    <tr>
                                        <th>No</th>
                                        <th class="sortable" data-sort="no_invoice">No Invoice <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="site">Site <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="unit">Unit <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="nomor_po">Nomor PO <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="tanggal_invoice">Tanggal Invoice <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="due_date">Tanggal Jatuh Tempo <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="payment_status">Status Pembayaran <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="due_status">Status Jatuh Tempo <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th>Tipe Invoice</th>
                                    </tr>
                                </thead>
                                <tbody id="invoices-table-body">
                                    <!-- Data will be loaded dynamically -->
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span id="showing-text">Menampilkan 0 dari 0 invoice</span>
                            </div>
                            <div>
                                <nav aria-label="Page navigation">
                                    <ul class="pagination pagination-sm" id="pagination">
                                        <!-- Pagination will be generated dynamically -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Document Viewer Modal -->
<div class="modal fade" id="document-viewer-modal" tabindex="-1" role="dialog" aria-labelledby="document-viewer-modal-label" aria-hidden="true">
    <div class="modal-dialog" role="document" style="max-width: 100%; margin: 0;">
        <div class="modal-content h-100">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="document-viewer-modal-label">Dokumen Invoice</h5>
                <button type="button" class="btn-close btn-close-white close-modal-btn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0" style="height: 90vh;">
                <div id="document-viewer" class="h-100">
                    <!-- Document will be embedded here -->
                </div>
            </div>
            <div class="modal-footer">
                <a id="download-document" href="#" target="_blank" class="btn btn-primary">
                    <i class="mdi mdi-download"></i> Download
                </a>
                <button type="button" class="btn btn-secondary close-modal-btn" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Direct Invoice Modal -->
<div class="modal fade" id="direct-invoice-modal" tabindex="-1" role="dialog" aria-labelledby="direct-invoice-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="direct-invoice-modal-label">Tambah Invoice Langsung</h5>
                <button type="button" class="btn-close btn-close-white close-modal-btn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="direct-invoice-form" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="no_invoice" class="form-label">Nomor Invoice</label>
                                <input type="text" class="form-control" id="no_invoice" name="no_invoice" placeholder="Nomor Invoice">
                                <small class="text-muted">Format: 001/INV-PWB/MM/YYYY</small>
                            </div>
                            <div class="mb-3">
                                <label for="customer" class="form-label">Customer</label>
                                <input type="text" class="form-control" id="customer" name="customer" placeholder="Nama Customer">
                            </div>
                            <div class="mb-3">
                                <label for="site_id" class="form-label">Site</label>
                                <select class="form-control" id="site_id" name="site_id">
                                    <option value="">Pilih Site</option>
                                    <!-- Sites will be loaded dynamically -->
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="unit" class="form-label">Unit</label>
                                <input type="text" class="form-control" id="unit" name="unit" placeholder="Kode Unit">
                            </div>
                            <div class="mb-3">
                                <label for="po_number" class="form-label">Nomor PO</label>
                                <input type="text" class="form-control" id="po_number" name="po_number" placeholder="Nomor PO">
                            </div>
                            <div class="mb-3">
                                <label for="model_unit" class="form-label">Model Unit</label>
                                <input type="text" class="form-control" id="model_unit" name="model_unit" placeholder="Model Unit">
                            </div>
                            <div class="mb-3">
                                <label for="hmkm" class="form-label">HM/KM</label>
                                <input type="text" class="form-control" id="hmkm" name="hmkm" placeholder="HM/KM">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tanggal_invoice" class="form-label">Tanggal Invoice</label>
                                <input type="date" class="form-control" id="tanggal_invoice" name="tanggal_invoice">
                            </div>
                            <div class="mb-3">
                                <label for="due_date" class="form-label">Tanggal Jatuh Tempo</label>
                                <input type="date" class="form-control" id="due_date" name="due_date">
                            </div>
                            <div class="mb-3">
                                <label for="sn" class="form-label">Serial Number</label>
                                <input type="text" class="form-control" id="sn" name="sn" placeholder="Serial Number">
                            </div>
                            <div class="mb-3">
                                <label for="trouble" class="form-label">Trouble</label>
                                <input type="text" class="form-control" id="trouble" name="trouble" placeholder="Trouble">
                            </div>
                            <div class="mb-3">
                                <label for="lokasi" class="form-label">Lokasi</label>
                                <input type="text" class="form-control" id="lokasi" name="lokasi" placeholder="Lokasi">
                            </div>
                            <div class="mb-3">
                                <label for="subtotal" class="form-label">Subtotal (Rp)</label>
                                <input type="number" class="form-control" id="subtotal" name="subtotal" placeholder="Subtotal">
                            </div>
                            <div class="mb-3">
                                <label for="ppn" class="form-label">PPN (%)</label>
                                <input type="number" class="form-control" id="ppn" name="ppn" value="11" placeholder="PPN">
                                <small class="text-muted">Masukkan dalam bentuk persentase (contoh: 11 untuk 11%)</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="transfer_to" class="form-label">Ditransfer Ke</label>
                                <input type="text" class="form-control" id="transfer_to" name="transfer_to" placeholder="Ditransfer Ke">
                            </div>
                            <div class="mb-3">
                                <label for="bank_account" class="form-label">Nomor Rekening</label>
                                <input type="text" class="form-control" id="bank_account" name="bank_account" placeholder="Nomor Rekening">
                            </div>
                            <div class="mb-3">
                                <label for="bank_branch" class="form-label">Cabang Bank</label>
                                <input type="text" class="form-control" id="bank_branch" name="bank_branch" placeholder="Cabang Bank">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notes" class="form-label">Catatan</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Catatan"></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="document" class="form-label">Dokumen Invoice</label>
                                <input type="file" class="form-control" id="document" name="document" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                <small class="text-muted">Maksimal 10MB (PDF, JPG, JPEG, PNG, DOC, DOCX)</small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal-btn" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="save-direct-invoice-btn">Simpan</button>
            </div>
        </div>
    </div>
</div>

<!-- Add modal script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add event listeners to close modal buttons
        document.querySelectorAll('.close-modal-btn').forEach(button => {
            button.addEventListener('click', function() {
                const modalId = this.closest('.modal').id;
                const modal = document.getElementById(modalId);
                modal.classList.remove('show');
                modal.style.display = 'none';
                document.body.classList.remove('modal-open');

                // Remove backdrop
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
            });
        });
    });
</script>
@endsection
