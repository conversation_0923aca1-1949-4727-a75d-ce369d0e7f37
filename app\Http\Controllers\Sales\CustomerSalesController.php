<?php

namespace App\Http\Controllers\Sales;

use App\Http\Controllers\Controller;
use App\Models\CustomerSales;
use App\Models\Penawaran;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CustomerSalesController extends Controller
{
    /**
     * Display a listing of the customers.
     */
    public function index(Request $request)
    {
        // Get the customers with pagination
        $customers = CustomerSales::orderBy('nama_customer', 'asc')->paginate(15);

        return view('sales.customer', compact('customers'));
    }

    /**
     * Store a newly created customer in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'kode' => 'required|string|max:50|unique:customersales,kode',
            'nama_customer' => 'required|string|max:255',
            'alamat' => 'nullable|string',
            'total_pending' => 'nullable|numeric',
        ]);

        try {
            $customer = CustomerSales::create([
                'kode' => strtoupper($request->kode),
                'nama_customer' => strtoupper($request->nama_customer),
                'alamat' => $request->alamat ? strtoupper($request->alamat) : null,
                'total_pending' => $request->total_pending ?? 0,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Customer berhasil ditambahkan',
                'data' => $customer
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating customer: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal menambahkan customer: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified customer.
     */
    public function show($id)
    {
        $customer = CustomerSales::findOrFail($id);
        return response()->json($customer);
    }

    /**
     * Update the specified customer in storage.
     */
    public function update(Request $request, $id)
    {
        $customer = CustomerSales::findOrFail($id);

        $request->validate([
            'kode' => 'required|string|max:50|unique:customersales,kode,' . $id,
            'nama_customer' => 'required|string|max:255',
            'alamat' => 'nullable|string',
            'total_pending' => 'nullable|numeric',
        ]);

        try {
            $customer->update([
                'kode' => strtoupper($request->kode),
                'nama_customer' => strtoupper($request->nama_customer),
                'alamat' => $request->alamat ? strtoupper($request->alamat) : null,
                'total_pending' => $request->total_pending ?? 0,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Customer berhasil diperbarui',
                'data' => $customer
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating customer: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal memperbarui customer: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified customer from storage.
     */
    public function destroy($id)
    {
        try {
            $customer = CustomerSales::findOrFail($id);

            // Check if customer has any pending offers
            $pendingOffers = Penawaran::where('customer', $customer->nama_customer)
                ->whereIn('status', ['Draft', 'Dikirim ke customer', 'PO customer', 'Proses penyediaan'])
                ->count();

            if ($pendingOffers > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer tidak dapat dihapus karena masih memiliki penawaran yang aktif'
                ], 400);
            }

            $customer->delete();

            return response()->json([
                'success' => true,
                'message' => 'Customer berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting customer: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus customer: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search for customers for autocomplete.
     */
    public function search(Request $request)
    {
        $query = $request->input('query');

        $customers = CustomerSales::where('nama_customer', 'LIKE', "%{$query}%")
            ->orWhere('kode', 'LIKE', "%{$query}%")
            ->limit(10)
            ->get(['id', 'kode', 'nama_customer', 'alamat']);

        return response()->json($customers);
    }

    /**
     * Update the total pending amount for customers.
     * This method can be called via a scheduled task or manually.
     */
    public function updatePendingTotals()
    {
        try {
            // Get all customers with active offers
            $customers = CustomerSales::all();

            foreach ($customers as $customer) {
                // Calculate total pending amount from active offers
                $totalPending = Penawaran::where('customer', $customer->nama_customer)
                    ->whereIn('status', ['Draft', 'Dikirim ke customer', 'PO customer', 'Proses penyediaan'])
                    ->with('items')
                    ->get()
                    ->sum(function ($penawaran) {
                        return $penawaran->items->sum(function ($item) {
                            return $item->quantity * $item->price;
                        });
                    });

                // Update the customer's total_pending field
                $customer->update(['total_pending' => $totalPending]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Total pending berhasil diperbarui untuk semua customer'
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating pending totals: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal memperbarui total pending: ' . $e->getMessage()
            ], 500);
        }
    }
}
