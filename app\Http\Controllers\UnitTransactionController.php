<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Unit;
use App\Models\UnitTransaction;
use App\Models\UnitTransactionPart;
use App\Models\PartInventory;
use App\Models\DocMultyUnit;
use App\Models\Part;
use App\Models\SiteOutStock;
use App\Models\LogAktivitas;
use App\Helpers\LogHelper;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Illuminate\Support\Facades\Cache;


class UnitTransactionController extends Controller
{
    /**
     * Part reference mapping
     * This array maps reference part codes to their component parts with quantities
     * Format: [
     *   'reference_code' => [
     *     ['code_part' => 'actual_part_code', 'quantity' => quantity_value],
     *     ...
     *   ],
     *   ...
     * ]
     */
    private $partReferences = [
        'HSN785-7-58-PWB' => [
            ['code_part' => 'HS-N58-PWB', 'quantity' => 2.5],
            ['code_part' => 'FT-58B-N-R134-PWB', 'quantity' => 1],
            ['code_part' => 'FTC-58-R134-PWB', 'quantity' => 1]
        ],
        'HSN785-7-12-PWB' => [
            ['code_part' => 'HS-N12-PWB', 'quantity' => 3.6],
            ['code_part' => 'FT-12L-N-R134-PWB', 'quantity' => 1],
            ['code_part' => 'FTC-12-R134-PWB', 'quantity' => 1]
        ],
        'HSN785-7-38-1-PWB' => [
            ['code_part' => 'HS-N38-PWB', 'quantity' => 1.5],
            ['code_part' => 'FT-38B-N-R134-PWB', 'quantity' => 2]
        ],
        'HSN785-7-38-2-PWB' => [
            ['code_part' => 'HS-N38-PWB', 'quantity' => 1.55],
            ['code_part' => 'FT-38B-N-R134-PWB', 'quantity' => 2]
        ],
        'HSN-58-CAT777-1-PW' => [
            ['code_part' => 'HS-N58-PWB', 'quantity' => 3],
            ['code_part' => 'FT-ACC-180-78-PWB', 'quantity' => 1],
            ['code_part' => 'FT-58B-N-R12-PWB', 'quantity' => 1]
        ]
    ];

    /**
     * Check if a part code is a reference part and return its component parts
     *
     * @param string $partCode The part code to check
     * @param float $quantity The quantity of the reference part
     * @return array|null Array of component parts with adjusted quantities or null if not a reference part
     */
    private function getReferencedParts(string $partCode, float $quantity = 1.0): ?array
    {
        // Check if the part code exists in our reference mapping
        if (!isset($this->partReferences[$partCode])) {
            return null;
        }

        // Get the referenced parts
        $referencedParts = $this->partReferences[$partCode];

        // Adjust quantities based on the reference part quantity
        foreach ($referencedParts as &$part) {
            $part['quantity'] = $part['quantity'] * $quantity;
        }

        return $referencedParts;
    }

    /**
     * Process a part for unit transaction, handling reference parts
     *
     * @param array $part The part data from request
     * @param int $unitTransactionId The unit transaction ID
     * @param Carbon $actualOutDate The actual out date for the transaction
     * @return array Information about processed parts
     */
    private function processPartForTransaction(array $part, int $unitTransactionId, Carbon $actualOutDate): array
    {
        $processedInfo = [
            'is_reference' => false,
            'transaction_parts' => [],
            'site_out_stocks' => []
        ];

        // Find the part inventory
        $partInventory = PartInventory::lockForUpdate()->find($part['part_inventory_id']);
        if (!$partInventory) {
            throw new \Exception('Part inventory not found for ID: ' . $part['part_inventory_id']);
        }

        // Get the part code
        $partCode = $partInventory->part_code;

        // Check if this is a reference part
        $referencedParts = $this->getReferencedParts($partCode, $part['quantity']);

        if ($referencedParts) {
            // This is a reference part
            $processedInfo['is_reference'] = true;

            // Create the transaction part record for the reference part
            $unitTransactionPart = UnitTransactionPart::create([
                'unit_transaction_id' => $unitTransactionId,
                'part_inventory_id' => $part['part_inventory_id'],
                'quantity' => $part['quantity'],
                'price' => $part['price'],
                'eum' => $part['eum'] ?? 'AE'
            ]);

            $processedInfo['transaction_parts'][] = $unitTransactionPart;

            // Process each referenced part
            foreach ($referencedParts as $refPart) {
                // Find the inventory for the referenced part
                $refPartInventory = PartInventory::lockForUpdate()
                    ->where('part_code', $refPart['code_part'])
                    ->where('site_id', session('site_id'))
                    ->first();

                if (!$refPartInventory) {
                    Log::warning("Referenced part {$refPart['code_part']} not found in inventory for site " . session('site_id'));
                    continue;
                }

                // Check if there's enough stock
                if ($refPartInventory->stock_quantity < $refPart['quantity']) {
                    throw new \Exception("Stok tidak mencukupi untuk part referensi: {$refPart['code_part']} ({$refPartInventory->part->part_name}). Stok tersedia: {$refPartInventory->stock_quantity}, Dibutuhkan: {$refPart['quantity']}. Part ini merupakan bagian dari part referensi {$partCode}.");
                }

                // Decrease the inventory quantity
                $refPartInventory->stock_quantity -= $refPart['quantity'];
                $refPartInventory->save();

                // Set notes based on conditions
                $daysDifference = $actualOutDate->diffInDays(Carbon::now());
                $isActualOutDateInPast = $actualOutDate->lt(Carbon::now());

                $notes = 'Part referensi: ' . $partCode;
                if ($isActualOutDateInPast && $daysDifference >= 1) {
                    $notes .= ' (Telat menambahkan transaksi)';
                }

                // Create a site out stock record for the referenced part
                $siteOutStock = SiteOutStock::create([
                    'part_inventory_id' => $refPartInventory->part_inventory_id,
                    'site_id' => session('site_id'),
                    'employee_id' => session('employee_id'),
                    'price' => $part['price'], // Use the reference part price
                    'date_out' => $actualOutDate,
                    'quantity' => $refPart['quantity'],
                    'status' => 'out stock',
                    'notes' => $notes,
                    'unit_transaction_parts_id' => $unitTransactionPart->id
                ]);

                $processedInfo['site_out_stocks'][] = $siteOutStock;
            }
        } else {
            // Regular part (not a reference)
            // Check if there's enough stock
            if ($partInventory->stock_quantity < $part['quantity']) {
                throw new \Exception('Stok tidak mencukupi untuk part: ' . $partInventory->part->part_name . ' (' . $partInventory->part_code . '). Stok tersedia: ' . $partInventory->stock_quantity . ', Dibutuhkan: ' . $part['quantity']);
            }

            // Create the transaction part record
            $unitTransactionPart = UnitTransactionPart::create([
                'unit_transaction_id' => $unitTransactionId,
                'part_inventory_id' => $part['part_inventory_id'],
                'quantity' => $part['quantity'],
                'price' => $part['price'],
                'eum' => $part['eum'] ?? 'AE'
            ]);

            $processedInfo['transaction_parts'][] = $unitTransactionPart;

            // Decrease the inventory quantity
            $partInventory->stock_quantity -= $part['quantity'];
            $partInventory->save();

            // Set notes based on conditions
            // Ensure $actualOutDate is a Carbon instance
            if (!($actualOutDate instanceof Carbon)) {
                $actualOutDate = Carbon::parse($actualOutDate);
            }
            $daysDifference = $actualOutDate->diffInDays(Carbon::now());
            $isActualOutDateInPast = $actualOutDate->lt(Carbon::now());

            $notes = '-';
            if ($isActualOutDateInPast && $daysDifference >= 1) {
                $notes = 'Telat menambahkan transaksi';
            }

            // Create a site out stock record
            $siteOutStock = SiteOutStock::create([
                'part_inventory_id' => $partInventory->part_inventory_id,
                'site_id' => session('site_id'),
                'employee_id' => session('employee_id'),
                'price' => $part['price'],
                'date_out' => $actualOutDate,
                'quantity' => $part['quantity'],
                'status' => 'out stock',
                'notes' => $notes,
                'unit_transaction_parts_id' => $unitTransactionPart->id
            ]);

            $processedInfo['site_out_stocks'][] = $siteOutStock;
        }

        return $processedInfo;
    }
    public function index(Request $request)
    {
        // Get units with pagination and search
        $query = Unit::with('site')->where('site_id', session('site_id'));

        // Apply search if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('unit_code', 'like', "%{$search}%")
                    ->orWhere('unit_type', 'like', "%{$search}%");
            });
        }

        $units = $query->paginate(5);

        // Preserve search parameter in pagination links
        if ($request->has('search')) {
            $units->appends(['search' => $request->search]);
        }

        // Get transactions
        $transactions = UnitTransaction::with(['unit', 'parts.partInventory.part'])
            ->where('site_id', session('site_id'))
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('units.unit_transactions', compact('units', 'transactions'));
    }

    public function getUnitParts($unitId)
    {
        $unit = Unit::with(['parts.partInventory.part'])->findOrFail($unitId);

        // Format the price values correctly
        $parts = $unit->parts->map(function ($part) {
            // Ensure price is a proper numeric value
            $part->price = (float) $part->price;
            return $part;
        });

        // Log the unit data to debug
        Log::info('Unit data: ' . json_encode($unit));
        Log::info('Parts data: ' . json_encode($parts));

        return response()->json([
            'unit' => $unit,
            'parts' => $parts
        ]);
    }

    public function getTransactions(Request $request)
    {
        // Get site_id from session
        $site_id = session('site_id');

        // Set default date range if not provided (yesterday to tomorrow)
        $dateFrom = $request->date_from;
        $dateTo = $request->date_to;

        if (empty($dateFrom) || empty($dateTo)) {
            $dateFrom = now()->subDay()->format('Y-m-d');
            $dateTo = now()->addDay()->format('Y-m-d');
        }

        // Build initial query with date range
        $query = UnitTransaction::with(['unit', 'parts.partInventory.part'])
            ->where('site_id', $site_id)
            ->whereBetween('created_at', [
                $dateFrom . ' 00:00:00',
                $dateTo . ' 23:59:59'
            ])
            ->orderBy('created_at', 'desc');

        // Apply other filters if provided
        if ($request->has('status') && $request->status != '') {
            // Check if the status parameter contains multiple values (comma-separated)
            if (strpos($request->status, ',') !== false) {
                $statuses = explode(',', $request->status);
                $query->whereIn('status', $statuses);
            } else {
                $query->where('status', $request->status);
            }
        }

        if ($request->has('unit_id') && $request->unit_id != '') {
            $query->where('unit_id', $request->unit_id);
        }

        // Apply search if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->whereHas('unit', function ($q) use ($search) {
                $q->where('unit_code', 'like', "%{$search}%")
                    ->orWhere('unit_type', 'like', "%{$search}%");
            });
        }

        // Clone the query to check if there are any results
        $checkQuery = clone $query;
        $hasResults = $checkQuery->exists();

        // If no results found with the date filter, get the most recent data
        if (!$hasResults) {
            // Remove the date filter and get the most recent data
            $query = UnitTransaction::with(['unit', 'parts.partInventory.part'])
                ->where('site_id', $site_id)
                ->orderBy('created_at', 'desc');

            // Re-apply other filters
            if ($request->has('status') && $request->status != '') {
                // Check if the status parameter contains multiple values (comma-separated)
                if (strpos($request->status, ',') !== false) {
                    $statuses = explode(',', $request->status);
                    $query->whereIn('status', $statuses);
                } else {
                    $query->where('status', $request->status);
                }
            }

            if ($request->has('unit_id') && $request->unit_id != '') {
                $query->where('unit_id', $request->unit_id);
            }

            // Re-apply search if provided
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->whereHas('unit', function ($q) use ($search) {
                    $q->where('unit_code', 'like', "%{$search}%")
                        ->orWhere('unit_type', 'like', "%{$search}%");
                });
            }

            // Get the most recent transaction date
            $latestTransaction = $query->first();

            if ($latestTransaction) {
                $latestDate = $latestTransaction->created_at->format('Y-m-d');

                // Update the date range to show data from the latest date
                $dateFrom = $latestDate;
                $dateTo = $latestDate;

                // Apply the new date filter
                $query->whereBetween('created_at', [
                    $dateFrom . ' 00:00:00',
                    $dateTo . ' 23:59:59'
                ]);
            }
        }

        // Paginate the results
        $transactions = $query->paginate(10);

        // Add date range to the response
        $transactions->date_from = $dateFrom;
        $transactions->date_to = $dateTo;

        // Preserve query parameters in pagination links
        $transactions->appends($request->only(['status', 'unit_id', 'search', 'date_from', 'date_to']));

        // Set no-cache headers
        return response()->json($transactions)
            ->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
            ->header('Pragma', 'no-cache')
            ->header('Expires', 'Sat, 01 Jan 2000 00:00:00 GMT');
    }

    public function getTransaction($id)
    {
        $transaction = UnitTransaction::with(['unit', 'parts.partInventory.part'])
            ->findOrFail($id);

        // Check if transaction belongs to user's site
        if ($transaction->site_id !== session('site_id')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return response()->json($transaction);
    }

    public function getUnits(Request $request)
    {
        // Get units with pagination and search
        $query = Unit::with('site')->where('site_id', session('site_id'));

        // Apply search if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('unit_code', 'like', "%{$search}%")
                    ->orWhere('unit_type', 'like', "%{$search}%");
            });
        }

        // Paginate results
        $units = $query->paginate(5);

        // Preserve search parameter in pagination links
        if ($request->has('search')) {
            $units->appends(['search' => $request->search]);
        }

        // Return JSON response with units and pagination data
        return response()->json([
            'data' => $units->items(),
            'current_page' => $units->currentPage(),
            'per_page' => $units->perPage(),
            'last_page' => $units->lastPage(),
            'total' => $units->total()
        ])
            ->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
            ->header('Pragma', 'no-cache')
            ->header('Expires', 'Sat, 01 Jan 2000 00:00:00 GMT');
    }

    /**
     * Search for parts in the current site's inventory
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchParts(Request $request)
    {
        $search = $request->input('q');
        $site_id = session('site_id');

        if (empty($search)) {
            return response()->json([]);
        }

        // Search for parts in the current site's inventory
        $parts = PartInventory::where('site_id', $site_id)
            ->where(function ($query) use ($search) {
                $query->where('part_code', 'like', "%{$search}%")
                    ->orWhereHas('part', function ($q) use ($search) {
                        $q->where('part_name', 'like', "%{$search}%")
                            ->orWhere('part_code', 'like', "%{$search}%");
                    });
            })
            ->with('part')
            ->limit(10)
            ->get()
            ->map(function ($partInventory) {
                return [
                    'id' => $partInventory->part_inventory_id,
                    'text' => $partInventory->part->part_name . ' (' . $partInventory->part_code . ') - Stock: ' . $partInventory->stock_quantity,
                    'part_inventory_id' => $partInventory->part_inventory_id,
                    'part_name' => $partInventory->part->part_name,
                    'part_code' => $partInventory->part_code,
                    'stock_quantity' => $partInventory->stock_quantity,
                    'price' => $partInventory->price,
                    'eum' => 'AE' // Default EUM value
                ];
            });

        return response()->json($parts);
    }

    public function store(Request $request)
    {
        // Basic validation rules
        $rules = [
            'unit_id' => 'required|exists:units,id',
            'status' => 'required|in:On Process,MR,Pending,Ready WO, Ready PO,Selesai,Perbaikan',
            'mr_date' => 'nullable|date',
            'wo_number' => 'nullable|string',
            'do_number' => 'nullable|string',
            'do_date' => 'nullable|date',
            'tanggalstart' => 'nullable|date',
            'tanggalend' => 'nullable|date',
            'noireq' => 'nullable|string',
            'po_number' => 'nullable|string',
            'issue_nomor' => 'nullable|string',
            'noSPB' => 'nullable|string',
            'remarks' => 'nullable|string',
            'sales_notes' => 'nullable|string',
            'pekerjaan' => 'nullable|string',
            'updated_at' => 'nullable|string',
            'HMKM' => 'nullable|string',
            'SHIFT' => 'nullable|string',
            'LOKASI' => 'nullable|string',
            'contact' => 'nullable|string',
            'phone' => 'nullable|string',
            'customer' => 'nullable|string',
            'sitework' => 'nullable|string',
            'attachment' => 'nullable|file|max:5120|mimes:pdf,jpg,jpeg,png,doc,docx,xls,xlsx',
            'parts' => 'required|array|min:1',
            'parts.*.part_inventory_id' => 'required|exists:part_inventories,part_inventory_id',
            'parts.*.quantity' => 'required|numeric|min:0.1',
            'parts.*.price' => 'required|numeric|min:0',
            'parts.*.eum' => 'nullable|string',
        ];

        // If status is Ready PO, require attachment and PO number
        if ($request->status === 'Ready PO') {
            $rules['attachment'] = 'required|file|max:5120|mimes:pdf,jpg,jpeg,png,doc,docx,xls,xlsx';
            $rules['po_number'] = 'required|string';
        }

        // If status is Ready WO, require WO number
        if ($request->status === 'Ready WO') {
            $rules['wo_number'] = 'required|string';
        }

        $request->validate($rules);

        // Start a database transaction to ensure data integrity
        DB::beginTransaction();

        try {
            // Get the unit and its current DO number and SPB number
            $unit = Unit::find($request->unit_id);

            // Generate DO number if not provided
            $doNumber = $request->do_number;

            // For DH site, always leave DO number empty unless explicitly provided
            if (session('site_id') === 'DH') {
                if (empty($doNumber)) {
                    // For DH site, leave DO number empty by default
                    $formattedDoNumber = '';
                } else {
                    // If DO number is explicitly provided, use it
                    $unit->update(['do_number' => $doNumber]);
                    $formattedDoNumber = $doNumber;
                }
            } else {
                // For other sites, use the original logic
                if (empty($doNumber)) {
                    // If unit has a DO number, get the next DO number
                    if ($unit->do_number) {
                        $nextDoNumber = $unit->getNextDoNumber();
                        // Update the unit's DO number
                        $unit->update(['do_number' => $nextDoNumber]);
                        $formattedDoNumber = $nextDoNumber;
                    } else {
                        // If no DO number exists, leave it empty
                        $formattedDoNumber = '';
                    }
                } else {
                    // If DO number is provided, use it directly
                    $unit->update(['do_number' => $doNumber]);
                    $formattedDoNumber = $doNumber;
                }
            }

            // Generate SPB number if not provided
            $spbNumber = $request->noSPB;
            if (empty($spbNumber)) {
                // If unit has an SPB number, get the next SPB number
                if ($unit->noSPB) {
                    $nextSpbNumber = $unit->getNextSpbNumber();
                    // Update the unit's SPB number
                    $unit->update(['noSPB' => $nextSpbNumber]);
                    $formattedSpbNumber = $nextSpbNumber;
                } else {
                    // If no SPB number exists, leave it empty
                    $formattedSpbNumber = '';
                }
            } else {
                // If SPB number is provided, use it directly
                $unit->update(['noSPB' => $spbNumber]);
                $formattedSpbNumber = $spbNumber;
            }

            // // For IMK site, copy noSPB to po_number
            // if (session('site_id') === 'IMK' && !empty($formattedSpbNumber)) {
            //     $request->merge(['po_number' => $formattedSpbNumber]);
            // }

            // Debug the SPB number
            Log::info('SPB Number: ' . $formattedSpbNumber);

            // Handle file upload if present
            $attachmentPath = null;
            if ($request->hasFile('attachment')) {
                $file = $request->file('attachment');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $file->move(public_path('assets/lampiranunits'), $fileName);
                $attachmentPath = $fileName;
            }

            // Get the actual out date from updated_at field if provided, otherwise use current time
            $actualOutDate = $request->filled('updated_at') ? Carbon::parse($request->updated_at) : now();

            // Create transaction
            $transaction = UnitTransaction::create([
                'unit_id' => $request->unit_id,
                'site_id' => $unit->site_id,
                'status' => $request->status,
                'mr_date' => $request->mr_date,
                'wo_number' => $request->wo_number,
                'po_number' => $request->po_number,
                'issue_nomor' => $request->issue_nomor,
                'do_number' => $formattedDoNumber,
                'do_date' => $request->do_date,
                'tanggalstart' => $request->tanggalstart,
                'tanggalend' => $request->tanggalend,
                'noireq' => $request->noireq,
                'noSPB' => $formattedSpbNumber,
                'remarks' => $request->remarks,
                'sales_notes' => $request->sales_notes,
                'pekerjaan' => $request->pekerjaan,
                'HMKM' => $request->HMKM,
                'SHIFT' => $request->SHIFT,
                'LOKASI' => $request->LOKASI,
                'contact' => $request->contact,
                'phone' => $request->phone,
                'customer' => $request->customer,
                'sitework' => $request->sitework,
                'attachment_path' => $attachmentPath,
                'actual_out_date' => $actualOutDate
            ]);

            // First, check if all parts have sufficient stock
            $stockIssues = [];
            foreach ($request->parts as $part) {
                $partInventory = PartInventory::find($part['part_inventory_id']);
                if (!$partInventory) {
                    throw new \Exception('Part inventory not found for ID: ' . $part['part_inventory_id']);
                }

                // Get the part code
                $partCode = $partInventory->part_code;

                // Check if this is a reference part
                $referencedParts = $this->getReferencedParts($partCode, $part['quantity']);

                if ($referencedParts) {
                    // This is a reference part - check stock for all referenced parts
                    foreach ($referencedParts as $refPart) {
                        // Find the inventory for the referenced part
                        $refPartInventory = PartInventory::where('part_code', $refPart['code_part'])
                            ->where('site_id', session('site_id'))
                            ->first();

                        if (!$refPartInventory) {
                            $stockIssues[] = [
                                'part_name' => "Referenced part {$refPart['code_part']} not found",
                                'part_code' => $refPart['code_part'],
                                'available' => 0,
                                'requested' => $refPart['quantity'],
                                'reference_part' => $partCode
                            ];
                            continue;
                        }

                        // Check if there's enough stock
                        if ($refPartInventory->stock_quantity < $refPart['quantity']) {
                            $stockIssues[] = [
                                'part_name' => $refPartInventory->part->part_name,
                                'part_code' => $refPartInventory->part_code,
                                'available' => $refPartInventory->stock_quantity,
                                'requested' => $refPart['quantity'],
                                'reference_part' => $partCode
                            ];
                        }
                    }
                } else {
                    // Regular part - check stock directly
                    if ($partInventory->stock_quantity < $part['quantity']) {
                        $stockIssues[] = [
                            'part_name' => $partInventory->part->part_name,
                            'part_code' => $partInventory->part->part_code,
                            'available' => $partInventory->stock_quantity,
                            'requested' => $part['quantity']
                        ];
                    }
                }
            }

            // If there are any stock issues, return them all at once with detailed error message
            if (!empty($stockIssues)) {
                DB::rollback(); // Roll back the transaction

                // Build detailed error message in Indonesian
                $errorMessage = 'Stok tidak mencukupi untuk part berikut:';
                $detailedErrors = [];

                foreach ($stockIssues as $issue) {
                    $partName = $issue['part_name'];
                    $partCode = $issue['part_code'];
                    $available = $issue['available'];
                    $requested = $issue['requested'];

                    $detailMsg = "• {$partName} ({$partCode}): Stok tersedia {$available}, dibutuhkan {$requested}";

                    // Add reference part information if applicable
                    if (isset($issue['reference_part'])) {
                        $detailMsg .= " - Bagian dari part referensi {$issue['reference_part']}";
                    }

                    $detailedErrors[] = $detailMsg;
                }

                // Join all detailed errors with line breaks
                $errorMessage .= "\n" . implode("\n", $detailedErrors);

                return response()->json([
                    'error' => $errorMessage,
                    'stock_issues' => $stockIssues
                ], 400);
            }

            // Now process each part
            foreach ($request->parts as $part) {
                // Process the part using our helper function
                $this->processPartForTransaction($part, $transaction->id, $transaction->actual_out_date);
            }

            // Commit the transaction if everything is successful
            DB::commit();

            // Refresh the transaction with relationships
            $transaction = UnitTransaction::with(['unit', 'parts.partInventory.part'])->find($transaction->id);

            // Log the transaction creation
            $partsForLog = [];
            foreach ($transaction->parts as $part) {
                $partsForLog[] = [
                    'name' => $part->partInventory->part->part_name,
                    'quantity' => $part->quantity
                ];
            }

            \App\Helpers\LogHelper::logUnitTransaction(
                'membuat',
                $unit->unit_type,
                $transaction->status,
                $partsForLog,
                $request
            );

            return response()->json(['message' => 'Transaction created successfully', 'transaction' => $transaction])
                ->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
                ->header('Pragma', 'no-cache');
        } catch (\Exception $e) {
            // Roll back the transaction if something goes wrong
            DB::rollback();
            Log::error('Error creating unit transaction: ' . $e->getMessage());

            // Check if the error is related to insufficient stock
            if (strpos($e->getMessage(), 'Stok tidak mencukupi') !== false) {
                return response()->json(['error' => $e->getMessage()], 400);
            }

            // For other errors, provide a more user-friendly message
            $errorMessage = 'Gagal membuat transaksi: ' . $e->getMessage();
            return response()->json(['error' => $errorMessage], 500);
        }
    }
    private function returnRemovedPartsStock(UnitTransaction $transaction, array $newPartInventoryIds, Request $request)
    {
        // Get existing parts from the transaction
        $existingParts = $transaction->parts()->get();
        $returnedParts = [];

        // We don't start a DB transaction here because this method is called within a transaction
        // in the update method. This avoids nested transactions which can cause issues.

        try {
            // Get the unit for logging purposes
            $unit = Unit::find($transaction->unit_id);

            foreach ($existingParts as $existingPart) {
                // Check if this part is not in the new parts list
                if (!in_array($existingPart->part_inventory_id, $newPartInventoryIds)) {
                    // This part has been removed, so return its quantity to inventory
                    $partInventory = PartInventory::lockForUpdate()->find($existingPart->part_inventory_id);

                    if ($partInventory) {
                        // Get the part code
                        $partCode = $partInventory->part_code;

                        // Check if this is a reference part
                        $referencedParts = $this->getReferencedParts($partCode, $existingPart->quantity);

                        if ($referencedParts) {
                            // This is a reference part - return stock for each referenced part
                            foreach ($referencedParts as $refPart) {
                                // Find the inventory for the referenced part
                                $refPartInventory = PartInventory::lockForUpdate()
                                    ->where('part_code', $refPart['code_part'])
                                    ->where('site_id', session('site_id'))
                                    ->first();

                                if (!$refPartInventory) {
                                    Log::warning("Referenced part {$refPart['code_part']} not found in inventory for site " . session('site_id'));
                                    continue;
                                }

                                // Increase the inventory quantity
                                $refPartInventory->stock_quantity += $refPart['quantity'];
                                $refPartInventory->save();

                                Log::info("Returned {$refPart['quantity']} of referenced part {$refPart['code_part']} to inventory");
                            }
                        } else {
                            // Regular part - increase inventory directly
                            $partInventory->stock_quantity += $existingPart->quantity;
                            $partInventory->save();

                            // Log the return for debugging
                            Log::info('Returned ' . $existingPart->quantity . ' of part ' . $partInventory->part->part_name . ' to inventory');
                        }

                        // Find and delete the corresponding site_out_stock record
                        $siteOutStock = SiteOutStock::where('unit_transaction_parts_id', $existingPart->id)->first();
                        if ($siteOutStock) {
                            $siteOutStock->delete();
                            Log::info('Deleted site out stock record for unit transaction part ID: ' . $existingPart->id);
                        } else {
                            Log::warning('No site out stock record found for unit transaction part ID: ' . $existingPart->id);
                        }

                        // Add to returned parts array
                        $returnedParts[] = [
                            'part_inventory_id' => $partInventory->part_inventory_id,
                            'part_name' => $partInventory->part->part_name,
                            'quantity' => $existingPart->quantity
                        ];
                    } else {
                        Log::error('Part inventory not found for ID: ' . $existingPart->part_inventory_id);
                    }
                }
            }

            return $returnedParts;
        } catch (\Exception $e) {
            Log::error('Error returning stock to inventory: ' . $e->getMessage());
            throw $e; // Re-throw the exception to be caught by the caller
        }
    }

    public function update(Request $request, $id)
    {
        $transaction = UnitTransaction::findOrFail($id);

        // Check if transaction belongs to user's site
        if ($transaction->site_id !== session('site_id')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Check if transaction is editable (transactions with 'Ready PO' or 'Selesai' status cannot be edited)
        if ($transaction->status === 'Ready PO' || $transaction->status === 'Selesai') {
            return response()->json(['message' => 'Only transactions with On Process, MR, Pending, or Ready WO status can be edited'], 422);
        }

        // Basic validation rules
        $rules = [
            'status' => 'required|in:On Process,MR,Pending,Ready WO,Ready PO,Completed,Selesai',
            'mr_date' => 'nullable|date',
            'remarks' => 'nullable|string',
            'sales_notes' => 'nullable|string',
            'wo_number' => 'nullable|string',
            'po_number' => 'nullable|string',
            'issue_nomor' => 'nullable|string',
            'do_number' => 'nullable|string',
            'do_date' => 'nullable|date',
            'noSPB' => 'nullable|string',
            'pekerjaan' => 'nullable|string',
            'HMKM' => 'nullable|string',
            'SHIFT' => 'nullable|string',
            'LOKASI' => 'nullable|string',
            'contact' => 'nullable|string',
            'phone' => 'nullable|string',
            'customer' => 'nullable|string',
            'sitework' => 'nullable|string',
            'updated_at' => 'nullable|date',
            'attachment' => 'nullable|file|max:5120|mimes:pdf,jpg,jpeg,png,doc,docx,xls,xlsx',
            'parts' => 'required|array|min:1',
            'parts.*.part_inventory_id' => 'required|exists:part_inventories,part_inventory_id',
            'parts.*.quantity' => 'required|numeric|min:0.1',
            'parts.*.price' => 'required|numeric|min:0',
            'parts.*.eum' => 'nullable|string',
        ];

        // If status is Ready PO, require attachment unless it already exists
        if ($request->status === 'Ready PO') {
            // Check if this is an update and if there's already an attachment
            $hasExistingAttachment = false;
            if ($transaction && $transaction->attachment_path) {
                $hasExistingAttachment = true;
            }

            // If no existing attachment, require one
            if (!$hasExistingAttachment) {
                $rules['attachment'] = 'required|file|max:5120|mimes:pdf,jpg,jpeg,png,doc,docx,xls,xlsx';
            }

            // Also require PO number for Ready PO status
            $rules['po_number'] = 'required|string';
        }

        // If status is Ready WO, require WO number
        if ($request->status === 'Ready WO') {
            $rules['wo_number'] = 'required|string';
        }

        $request->validate($rules);

        $doNumberChanged = $transaction->do_number !== $request->do_number;

        // Update DO number if explicitly provided and changed
        if ($doNumberChanged && !empty($request->do_number)) {
            $unit = Unit::find($transaction->unit_id);
            $unit->update(['do_number' => $request->do_number]);
        }

        $spbNumberChanged = $transaction->noSPB !== $request->noSPB;

        if ($spbNumberChanged && !empty($request->noSPB)) {
            $unit = Unit::find($transaction->unit_id);
            $unit->update(['noSPB' => $request->noSPB]);

            // For IMK site, copy noSPB to po_number
            if (session('site_id') === 'IMK') {
                $request->merge(['po_number' => $request->noSPB]);
            }
        }

        $attachmentPath = $transaction->attachment_path;
        if ($request->hasFile('attachment')) {
            if ($attachmentPath && file_exists(public_path('assets/lampiranunits/' . $attachmentPath))) {
                unlink(public_path('assets/lampiranunits/' . $attachmentPath));
            }

            $file = $request->file('attachment');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $file->move(public_path('assets/lampiranunits'), $fileName);
            $attachmentPath = $fileName;
        }

        $updateData = [
            'status' => $request->status,
            'mr_date' => $request->mr_date,
            'wo_number' => $request->wo_number,
            'po_number' => $request->po_number,
            'issue_nomor' => $request->issue_nomor,
            'do_number' => $request->do_number,
            'do_date' => $request->do_date,
            'tanggalstart' => $request->tanggalstart,
            'tanggalend' => $request->tanggalend,
            'noireq' => $request->noireq,
            'noSPB' => $request->noSPB,
            'remarks' => $request->remarks,
            'sales_notes' => $request->sales_notes,
            'pekerjaan' => $request->pekerjaan,
            'HMKM' => $request->HMKM,
            'SHIFT' => $request->SHIFT,
            'LOKASI' => $request->LOKASI,
            'contact' => $request->contact,
            'phone' => $request->phone,
            'customer' => $request->customer,
            'sitework' => $request->sitework,
            'attachment_path' => $attachmentPath
        ];

        // If updated_at is provided, update actual_out_date as well
        if ($request->filled('updated_at')) {
            $updateData['actual_out_date'] = Carbon::parse($request->updated_at);
        }

        // If updated_at is provided, manually set it
        if ($request->filled('updated_at')) {
            // Temporarily disable timestamps to prevent auto-update of updated_at
            $transaction->timestamps = false;
            $updateData['updated_at'] = $request->updated_at;
        }

        // Start a database transaction to ensure data integrity
        DB::beginTransaction();

        try {
            $transaction->update($updateData);

            // Re-enable timestamps for future updates
            $transaction->timestamps = true;

            // Get existing part inventory IDs and quantities before making changes
            $existingParts = $transaction->parts()->get()->mapWithKeys(function ($part) {
                return [$part->part_inventory_id => $part->quantity];
            })->toArray();

            // Extract part_inventory_ids from the request for comparison
            $newPartInventoryIds = array_map(function ($part) {
                return $part['part_inventory_id'];
            }, $request->parts);

            // Return stock for parts that have been removed
            $returnedParts = $this->returnRemovedPartsStock($transaction, $newPartInventoryIds, $request);

            // Get the unit for logging
            $unit = Unit::find($transaction->unit_id);

            // Log the returned parts
            if (!empty($returnedParts)) {
                foreach ($returnedParts as $part) {
                    \App\Helpers\LogHelper::createLog(
                        'Pengembalian Stock Unit Transaction',
                        'User ' . session('name') . ' mengembalikan stock ' . $part['part_name'] . ' sebanyak ' . $part['quantity'] . ' dari unit ' . $unit->unit_code,
                        'Unit Transaction',
                        $request
                    );
                }
            }

            // Get existing parts with their IDs for reference
            $existingPartRecords = $transaction->parts()->get()->keyBy('part_inventory_id');
            $processedPartIds = [];

            // Track newly added parts for logging
            $newlyAddedParts = [];

            // First, check if all parts have sufficient stock
            $stockIssues = [];
            foreach ($request->parts as $part) {
                $partInventoryId = $part['part_inventory_id'];
                $processedPartIds[] = $partInventoryId;

                // Determine if this is a new part or an existing part
                $isNewPart = !array_key_exists($partInventoryId, $existingParts);
                $oldQuantity = $isNewPart ? 0 : $existingParts[$partInventoryId];
                $quantityDifference = $part['quantity'] - $oldQuantity;

                // Only check stock if adding a new part or increasing quantity
                if ($isNewPart || $quantityDifference > 0) {
                    $partInventory = PartInventory::find($partInventoryId);
                    if (!$partInventory) {
                        throw new \Exception('Part inventory not found for ID: ' . $partInventoryId);
                    }

                    // Get the part code
                    $partCode = $partInventory->part_code;

                    // Check if this is a reference part
                    $referencedParts = $this->getReferencedParts($partCode, $isNewPart ? $part['quantity'] : $quantityDifference);

                    if ($referencedParts) {
                        // This is a reference part - check stock for all referenced parts
                        foreach ($referencedParts as $refPart) {
                            // Find the inventory for the referenced part
                            $refPartInventory = PartInventory::where('part_code', $refPart['code_part'])
                                ->where('site_id', session('site_id'))
                                ->first();

                            if (!$refPartInventory) {
                                $stockIssues[] = [
                                    'part_name' => "Referenced part {$refPart['code_part']} not found",
                                    'part_code' => $refPart['code_part'],
                                    'available' => 0,
                                    'requested' => $refPart['quantity'],
                                    'reference_part' => $partCode
                                ];
                                continue;
                            }

                            // Check if there's enough stock
                            if ($refPartInventory->stock_quantity < $refPart['quantity']) {
                                $stockIssues[] = [
                                    'part_name' => $refPartInventory->part->part_name,
                                    'part_code' => $refPartInventory->part_code,
                                    'available' => $refPartInventory->stock_quantity,
                                    'requested' => $refPart['quantity'],
                                    'reference_part' => $partCode
                                ];
                            }
                        }
                    } else {
                        // Regular part - check stock directly
                        $quantityToCheck = $isNewPart ? $part['quantity'] : $quantityDifference;
                        if ($partInventory->stock_quantity < $quantityToCheck) {
                            $stockIssues[] = [
                                'part_name' => $partInventory->part->part_name,
                                'part_code' => $partInventory->part->part_code,
                                'available' => $partInventory->stock_quantity,
                                'requested' => $quantityToCheck
                            ];
                        }
                    }
                }
            }

            // If there are any stock issues, return them all at once with detailed error message
            if (!empty($stockIssues)) {
                DB::rollback(); // Roll back the transaction

                // Build detailed error message in Indonesian
                $errorMessage = 'Stok tidak mencukupi untuk part berikut:';
                $detailedErrors = [];

                foreach ($stockIssues as $issue) {
                    $partName = $issue['part_name'];
                    $partCode = $issue['part_code'];
                    $available = $issue['available'];
                    $requested = $issue['requested'];

                    $detailMsg = "• {$partName} ({$partCode}): Stok tersedia {$available}, dibutuhkan {$requested}";

                    // Add reference part information if applicable
                    if (isset($issue['reference_part'])) {
                        $detailMsg .= " - Bagian dari part referensi {$issue['reference_part']}";
                    }

                    $detailedErrors[] = $detailMsg;
                }

                // Join all detailed errors with line breaks
                $errorMessage .= "\n" . implode("\n", $detailedErrors);

                return response()->json([
                    'error' => $errorMessage,
                    'stock_issues' => $stockIssues
                ], 400);
            }

            foreach ($request->parts as $part) {
                $partInventoryId = $part['part_inventory_id'];

                // Determine if this is a new part or an existing part
                $isNewPart = !array_key_exists($partInventoryId, $existingParts);
                $oldQuantity = $isNewPart ? 0 : $existingParts[$partInventoryId];
                $quantityDifference = $part['quantity'] - $oldQuantity;

                // Find the part inventory with a lock to prevent race conditions
                $partInventory = PartInventory::lockForUpdate()->find($partInventoryId);

                if (!$partInventory) {
                    throw new \Exception('Part inventory not found for ID: ' . $partInventoryId);
                }

                // Get the part code
                $partCode = $partInventory->part_code;

                // Check if the actual_out_date is more than one day earlier than today
                $actualOutDate = $transaction->actual_out_date;
                // Ensure $actualOutDate is a Carbon instance
                if (!($actualOutDate instanceof Carbon)) {
                    $actualOutDate = Carbon::parse($actualOutDate);
                }
                $today = Carbon::now();
                $daysDifference = $today->diffInDays($actualOutDate);
                $isActualOutDateInPast = $actualOutDate->lt($today);

                // Log the part quantity changes for debugging
                Log::info('Part update details:', [
                    'part_name' => $partInventory->part->part_name,
                    'part_id' => $partInventoryId,
                    'part_code' => $partCode,
                    'is_new_part' => $isNewPart,
                    'old_quantity' => $oldQuantity,
                    'new_quantity' => $part['quantity'],
                    'quantity_difference' => $quantityDifference,
                    'current_stock' => $partInventory->stock_quantity
                ]);

                // If it's a new part, create a new UnitTransactionPart record
                if ($isNewPart) {
                    // Create new transaction part record
                    $unitTransactionPart = UnitTransactionPart::create([
                        'unit_transaction_id' => $transaction->id,
                        'part_inventory_id' => $partInventoryId,
                        'quantity' => $part['quantity'],
                        'price' => $part['price'],
                        'eum' => $part['eum']
                    ]);

                    // Check if this is a reference part
                    $referencedParts = $this->getReferencedParts($partCode, $part['quantity']);

                    if ($referencedParts) {
                        // This is a reference part - process each referenced part
                        foreach ($referencedParts as $refPart) {
                            // Find the inventory for the referenced part
                            $refPartInventory = PartInventory::lockForUpdate()
                                ->where('part_code', $refPart['code_part'])
                                ->where('site_id', session('site_id'))
                                ->first();

                            if (!$refPartInventory) {
                                Log::warning("Referenced part {$refPart['code_part']} not found in inventory for site " . session('site_id'));
                                continue;
                            }

                            // Decrease the inventory quantity
                            $refPartInventory->stock_quantity -= $refPart['quantity'];
                            $refPartInventory->save();

                            // Set notes based on conditions
                            $notes = "Part referensi: {$partCode}";
                            if ($isActualOutDateInPast && $daysDifference >= 1) {
                                $notes .= ' (Telat menambahkan transaksi)';
                            }

                            // Create a site out stock record for the referenced part
                            SiteOutStock::create([
                                'part_inventory_id' => $refPartInventory->part_inventory_id,
                                'site_id' => session('site_id'),
                                'employee_id' => session('employee_id'),
                                'price' => $part['price'], // Use the reference part price
                                'date_out' => $actualOutDate,
                                'quantity' => $refPart['quantity'],
                                'status' => 'out stock',
                                'notes' => $notes,
                                'unit_transaction_parts_id' => $unitTransactionPart->id
                            ]);
                        }
                    } else {
                        // Regular part - decrease inventory directly
                        $partInventory->stock_quantity -= $part['quantity'];
                        $partInventory->save();

                        // Log the stock decrease
                        Log::info('Stock decreased for new part:', [
                            'part_name' => $partInventory->part->part_name,
                            'quantity_decreased' => $part['quantity'],
                            'new_stock_level' => $partInventory->stock_quantity
                        ]);

                        // Set notes based on conditions
                        $notes = 'Part baru ditambahkan';
                        if ($isActualOutDateInPast && $daysDifference >= 1) {
                            $notes = 'Telat menambahkan transaksi';
                        }

                        // Create a site out stock record
                        SiteOutStock::create([
                            'part_inventory_id' => $partInventoryId,
                            'site_id' => session('site_id'),
                            'employee_id' => session('employee_id'),
                            'price' => $part['price'],
                            'date_out' => $actualOutDate,
                            'quantity' => $part['quantity'],
                            'status' => 'out stock',
                            'notes' => $notes,
                            'unit_transaction_parts_id' => $unitTransactionPart->id
                        ]);
                    }

                    // Track for logging
                    $newlyAddedParts[] = [
                        'name' => $partInventory->part->part_name,
                        'quantity' => $part['quantity'],
                        'is_new' => true,
                        'is_quantity_increase' => false
                    ];
                } else {
                    // This is an existing part - get the existing record
                    $existingPart = $existingPartRecords[$partInventoryId];

                    // Update the existing part record
                    $existingPart->update([
                        'quantity' => $part['quantity'],
                        'price' => $part['price'],
                        'eum' => $part['eum']
                    ]);

                    // Handle quantity changes
                    if ($quantityDifference > 0) {
                        // Quantity increased
                        // Check if this is a reference part
                        $referencedParts = $this->getReferencedParts($partCode, $quantityDifference);

                        if ($referencedParts) {
                            // This is a reference part - process each referenced part
                            foreach ($referencedParts as $refPart) {
                                // Find the inventory for the referenced part
                                $refPartInventory = PartInventory::lockForUpdate()
                                    ->where('part_code', $refPart['code_part'])
                                    ->where('site_id', session('site_id'))
                                    ->first();

                                if (!$refPartInventory) {
                                    Log::warning("Referenced part {$refPart['code_part']} not found in inventory for site " . session('site_id'));
                                    continue;
                                }

                                // Decrease the inventory quantity
                                $refPartInventory->stock_quantity -= $refPart['quantity'];
                                $refPartInventory->save();

                                // Set notes based on conditions
                                $notes = "Part referensi: {$partCode} (Penambahan kuantitas)";
                                if ($isActualOutDateInPast && $daysDifference >= 1) {
                                    $notes .= ' (Telat menambahkan transaksi)';
                                }

                                // Create a site out stock record for the referenced part
                                SiteOutStock::create([
                                    'part_inventory_id' => $refPartInventory->part_inventory_id,
                                    'site_id' => session('site_id'),
                                    'employee_id' => session('employee_id'),
                                    'price' => $part['price'], // Use the reference part price
                                    'date_out' => $actualOutDate,
                                    'quantity' => $refPart['quantity'],
                                    'status' => 'out stock',
                                    'notes' => $notes,
                                    'unit_transaction_parts_id' => $existingPart->id
                                ]);
                            }
                        } else {
                            // Regular part - decrease inventory directly
                            $partInventory->stock_quantity -= $quantityDifference;
                            $partInventory->save();

                            // Log the stock decrease
                            Log::info('Stock decreased for quantity increase:', [
                                'part_name' => $partInventory->part->part_name,
                                'quantity_decreased' => $quantityDifference,
                                'new_stock_level' => $partInventory->stock_quantity
                            ]);

                            // Set notes based on conditions
                            $notes = 'Penambahan kuantitas part';
                            if ($isActualOutDateInPast && $daysDifference >= 1) {
                                $notes = 'Telat menambahkan transaksi';
                            }

                            // Create a site out stock record for the additional quantity
                            SiteOutStock::create([
                                'part_inventory_id' => $partInventoryId,
                                'site_id' => session('site_id'),
                                'employee_id' => session('employee_id'),
                                'price' => $part['price'],
                                'date_out' => $actualOutDate,
                                'quantity' => $quantityDifference,
                                'status' => 'out stock',
                                'notes' => $notes,
                                'unit_transaction_parts_id' => $existingPart->id
                            ]);
                        }

                        // Track for logging
                        $newlyAddedParts[] = [
                            'name' => $partInventory->part->part_name,
                            'quantity' => $quantityDifference,
                            'is_new' => false,
                            'is_quantity_increase' => true
                        ];

                        // Log quantity increase separately
                        $unit = Unit::find($transaction->unit_id);
                        \App\Helpers\LogHelper::createLog(
                            'Penambahan Kuantitas Part',
                            'User ' . session('name') . ' menambah kuantitas part ' . $partInventory->part->part_name .
                                ' sebanyak ' . $quantityDifference . ' pada transaksi unit ' . $unit->unit_type,
                            'Unit Transaction',
                            $request
                        );
                    } elseif ($quantityDifference < 0) {
                        // Quantity decreased - return stock to inventory
                        $quantityToReturn = abs($quantityDifference);

                        // Check if this is a reference part
                        $referencedParts = $this->getReferencedParts($partCode, $quantityToReturn);

                        if ($referencedParts) {
                            // This is a reference part - return stock for each referenced part
                            foreach ($referencedParts as $refPart) {
                                // Find the inventory for the referenced part
                                $refPartInventory = PartInventory::lockForUpdate()
                                    ->where('part_code', $refPart['code_part'])
                                    ->where('site_id', session('site_id'))
                                    ->first();

                                if (!$refPartInventory) {
                                    Log::warning("Referenced part {$refPart['code_part']} not found in inventory for site " . session('site_id'));
                                    continue;
                                }

                                // Increase the inventory quantity
                                $refPartInventory->stock_quantity += $refPart['quantity'];
                                $refPartInventory->save();

                                Log::info("Returned {$refPart['quantity']} of referenced part {$refPart['code_part']} to inventory");
                            }
                        } else {
                            // Regular part - return stock directly
                            $partInventory->stock_quantity += $quantityToReturn;
                            $partInventory->save();

                            // Log the stock return
                            Log::info('Stock returned for quantity decrease:', [
                                'part_name' => $partInventory->part->part_name,
                                'quantity_returned' => $quantityToReturn,
                                'new_stock_level' => $partInventory->stock_quantity
                            ]);
                        }

                        // Get the unit for logging
                        $unit = Unit::find($transaction->unit_id);

                        // Log the stock return
                        \App\Helpers\LogHelper::createLog(
                            'Pengembalian Stock Unit Transaction',
                            'User ' . session('name') . ' mengembalikan stock ' . $partInventory->part->part_name .
                                ' sebanyak ' . $quantityToReturn . ' dari unit ' . $unit->unit_code . ' (pengurangan kuantitas)',
                            'Unit Transaction',
                            $request
                        );

                        // Find and update the corresponding site_out_stock record
                        $siteOutStock = SiteOutStock::where('unit_transaction_parts_id', $existingPart->id)->first();
                        if ($siteOutStock) {
                            // Update the quantity in the existing record
                            $siteOutStock->update([
                                'quantity' => $part['quantity'],
                                'price' => $part['price']
                            ]);
                        }
                    } else {
                        // No quantity change - just log the update
                        $notes = '-';
                        if ($isActualOutDateInPast && $daysDifference >= 1) {
                            $notes = 'Telat menambahkan transaksi';
                        }

                        // Find the corresponding site_out_stock record
                        $siteOutStock = SiteOutStock::where('unit_transaction_parts_id', $existingPart->id)->first();

                        // If no site_out_stock record exists, create one to maintain consistency
                        if (!$siteOutStock) {
                            SiteOutStock::create([
                                'part_inventory_id' => $partInventoryId,
                                'site_id' => session('site_id'),
                                'employee_id' => session('employee_id'),
                                'price' => $part['price'],
                                'date_out' => $actualOutDate,
                                'quantity' => $part['quantity'],
                                'status' => 'out stock',
                                'notes' => $notes,
                                'unit_transaction_parts_id' => $existingPart->id
                            ]);
                        } else {
                            // Update the existing site_out_stock record
                            $siteOutStock->update([
                                'price' => $part['price'],
                                'notes' => $notes
                            ]);
                        }
                    }
                }
            }

            // Delete parts that are no longer in the request
            foreach ($existingPartRecords as $partId => $existingPart) {
                if (!in_array($partId, $processedPartIds)) {
                    // Find and delete the corresponding site_out_stock record
                    $siteOutStock = SiteOutStock::where('unit_transaction_parts_id', $existingPart->id)->first();
                    if ($siteOutStock) {
                        $siteOutStock->delete();
                    }

                    // Delete the part record
                    $existingPart->delete();
                }
            }

            // Commit the transaction if everything is successful
            DB::commit();

            // Refresh the transaction with relationships
            $transaction = UnitTransaction::with(['unit', 'parts.partInventory.part'])->find($transaction->id);

            // Log the transaction update
            $partsForLog = [];
            foreach ($transaction->parts as $part) {
                $partsForLog[] = [
                    'name' => $part->partInventory->part->part_name,
                    'quantity' => $part->quantity
                ];
            }

            // Determine if status has changed
            $statusChanged = $transaction->getOriginal('status') !== $transaction->status;

            // Determine action description based on changes
            $action = 'mengubah';
            if ($statusChanged) {
                $action = 'mengubah status';
            } else if (!empty($newlyAddedParts)) {
                $action = 'menambahkan part baru';
            }

            // Log newly added parts separately
            if (!empty($newlyAddedParts)) {
                $newPartsDescription = 'User ' . session('name') . ' menambahkan part baru ke transaksi unit ' . $transaction->unit->unit_type . ': ';
                $partDetails = [];

                foreach ($newlyAddedParts as $part) {
                    if ($part['is_new']) {
                        $partDetails[] = $part['name'] . ' (' . $part['quantity'] . ')';
                    }
                }

                if (!empty($partDetails)) {
                    LogAktivitas::create([
                        'site_id' => session('site_id'),
                        'name' => session('name'),
                        'action' => 'Penambahan Part Unit Transaction',
                        'description' => $newPartsDescription . implode(', ', $partDetails),
                        'table' => 'Unit Transaction',
                        'ip_address' => $request->ip(),
                    ]);
                }
            }

            // Create parts string for log
            $partsStr = implode(', ', array_map(function ($part) {
                return $part['name'] . ' (' . $part['quantity'] . ')';
            }, $partsForLog));

            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => $action,
                'description' => "User " . session('name') . " {$action} transaksi unit " . $transaction->unit->unit_type .
                    " dengan status " . $transaction->status . ". Parts: " . $partsStr,
                'table' => "Unit Transaction",
                'ip_address' => $request->ip(),
            ]);

            return response()->json(['message' => 'Transaction updated successfully', 'transaction' => $transaction])
                ->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
                ->header('Pragma', 'no-cache');
        } catch (\Exception $e) {
            // Roll back the transaction if something goes wrong
            DB::rollback();
            Log::error('Error updating unit transaction: ' . $e->getMessage());

            // Check if the error is related to insufficient stock
            if (strpos($e->getMessage(), 'Stok tidak mencukupi') !== false) {
                return response()->json(['error' => $e->getMessage()], 400);
            }

            // For other errors, provide a more user-friendly message
            $errorMessage = 'Gagal mengupdate transaksi: ' . $e->getMessage();
            return response()->json(['error' => $errorMessage], 500);
        }
    }
    private function getTransactionsForPdf(Request $request)
    {
        // Get site_id from session
        $site_id = session('site_id');
        $site_name = session('name');

        // Set default date range if not provided (yesterday to tomorrow)
        $dateFrom = $request->date_from;
        $dateTo = $request->date_to;

        if (empty($dateFrom) || empty($dateTo)) {
            $dateFrom = now()->subDay()->format('Y-m-d');
            $dateTo = now()->addDay()->format('Y-m-d');
        }

        // Build query with date range
        $query = UnitTransaction::with(['unit', 'parts.partInventory.part'])
            ->where('site_id', $site_id)
            ->whereBetween('created_at', [
                $dateFrom . ' 00:00:00',
                $dateTo . ' 23:59:59'
            ])
            ->orderBy('created_at', 'desc');

        // Apply filters if provided
        if ($request->has('status') && $request->status != '') {
            $query->where('status', $request->status);
        }

        if ($request->has('unit_id') && $request->unit_id != '') {
            $query->where('unit_id', $request->unit_id);
        }

        // Apply search if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->whereHas('unit', function ($q) use ($search) {
                $q->where('unit_code', 'like', "%{$search}%")
                    ->orWhere('unit_type', 'like', "%{$search}%");
            });
        }

        // Check if there are any results
        $hasResults = $query->exists();

        // If no results found with the date filter, get the most recent data
        if (!$hasResults) {
            // Remove the date filter and get the most recent data
            $query = UnitTransaction::with(['unit', 'parts.partInventory.part'])
                ->where('site_id', $site_id)
                ->orderBy('created_at', 'desc');

            // Re-apply other filters
            if ($request->has('status') && $request->status != '') {
                $query->where('status', $request->status);
            }

            if ($request->has('unit_id') && $request->unit_id != '') {
                $query->where('unit_id', $request->unit_id);
            }

            // Re-apply search if provided
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->whereHas('unit', function ($q) use ($search) {
                    $q->where('unit_code', 'like', "%{$search}%")
                        ->orWhere('unit_type', 'like', "%{$search}%");
                });
            }

            // Get the most recent transaction date
            $latestTransaction = $query->first();

            if ($latestTransaction) {
                $latestDate = $latestTransaction->created_at->format('Y-m-d');

                // Update the date range to show data from the latest date
                $dateFrom = $latestDate;
                $dateTo = $latestDate;

                // Apply the new date filter
                $query->whereBetween('created_at', [
                    $dateFrom . ' 00:00:00',
                    $dateTo . ' 23:59:59'
                ]);
            }
        }

        // Get all transactions (no pagination for PDF)
        $transactions = $query->get();

        // Calculate total price for all transactions
        $totalPrice = 0;
        foreach ($transactions as $transaction) {
            foreach ($transaction->parts as $part) {
                $totalPrice += $part->price * $part->quantity;
            }
        }

        // Format the total price
        $formattedTotalPrice = 'Rp ' . number_format($totalPrice, 0, ',', '.');

        return [
            'transactions' => $transactions,
            'site_name' => $site_name,
            'totalPrice' => $formattedTotalPrice,
            'date' => Carbon::now()->format('d-m-Y H:i:s'),
            'title' => 'All Unit Transactions Report (' . $dateFrom . ' to ' . $dateTo . ')',
            'filename' => 'unit_transactions_' . str_replace(' ', '_', strtolower($site_name)) . '_' . Carbon::now()->format('YmdHis') . '.pdf'
        ];
    }

    public function exportPdf(Request $request)
    {
        $data = $this->getTransactionsForPdf($request);

        // Check if template is 'slip' for SLIP STORE report
        if ($request->template === 'slip') {
            // For DH site, use the DH-specific Slip Store template
            if (session('site_id') == 'DH') {
                $template = 'units.reportDH_SlipStore';
            } else {
                $template = 'units.reportSlipStore';
            }
        } else {
            // Use existing template logic for other reports
            if (session('site_id') == 'PPA') {
                $template = $request->template === 'report' ? 'units.reportPPA_PO' : 'units.reportPPA_WO';
            } elseif (session('site_id') == 'IMK') {
                $template = $request->template === 'report' ? 'units.reportIMK_PO' : 'units.reportIMK_WO';
            } elseif (session('site_id') == 'UDU') {
                $template = $request->template === 'report' ? 'units.reportUDU_PO' : 'units.reportUDU_WO';
            } elseif (session('site_id') == 'DH') {
                $template = $request->template === 'report' ? 'units.reportDH_BAPP' : 'units.reportDH_SlipStore';
            }
        }

        $pdf = Pdf::loadView($template, $data);
        $pdf->setPaper('a4', 'landscape');
        return $pdf->download($data['filename']);
    }

    public function previewPdf(Request $request)
    {
        $data = $this->getTransactionsForPdf($request);

        // Check if template is 'slip' for SLIP STORE report
        if ($request->template === 'slip') {
            // For DH site, use the DH-specific Slip Store template
            if (session('site_id') == 'DH') {
                $template = 'units.reportDH_SlipStore';
            } else {
                $template = 'units.reportSlipStore';
            }
        } else {
            // Use existing template logic for other reports
            if (session('site_id') == 'PPA') {
                $template = $request->template === 'report' ? 'units.reportPPA_PO' : 'units.reportPPA_WO';
            } elseif (session('site_id') == 'IMK') {
                $template = $request->template === 'report' ? 'units.reportIMK_PO' : 'units.reportIMK_WO';
            } elseif (session('site_id') == 'UDU') {
                $template = $request->template === 'report' ? 'units.reportUDU_PO' : 'units.reportUDU_WO';
            } elseif (session('site_id') == 'DH') {
                $template = $request->template === 'report' ? 'units.reportDH_BAPP' : 'units.reportDH_SlipStore';
            }
        }

        $pdf = Pdf::loadView($template, $data);
        $pdf->setPaper('A4', 'landscape');
        return $pdf->stream($data['filename']);
    }

    private function getSelectedTransactionsForPdf(Request $request)
    {
        // Get site_id from session
        $site_id = session('site_id');
        $site_name = session('name');

        // Validate request
        if (!$request->has('ids') || empty($request->ids)) {
            return response()->json(['error' => 'No transactions selected'], 400);
        }

        // Get selected transaction IDs
        $ids = explode(',', $request->ids);

        // Build query
        $query = UnitTransaction::with(['unit', 'parts.partInventory.part'])
            ->where('site_id', $site_id)
            ->whereIn('id', $ids)
            ->orderBy('created_at', 'desc');

        // Get selected transactions
        $transactions = $query->get();

        // Check if any transactions were found
        if ($transactions->isEmpty()) {
            return response()->json(['error' => 'No transactions found with the selected IDs'], 404);
        }

        // Calculate total price for selected transactions
        $totalPrice = 0;
        foreach ($transactions as $transaction) {
            foreach ($transaction->parts as $part) {
                $totalPrice += $part->price * $part->quantity;
            }
        }

        // Format the total price
        $formattedTotalPrice = 'Rp ' . number_format($totalPrice, 0, ',', '.');

        return [
            'transactions' => $transactions,
            'site_name' => $site_name,
            'totalPrice' => $formattedTotalPrice,
            'title' => 'Selected Unit Transactions Report',
            'filename' => 'selected_unit_transactions_' . str_replace(' ', '_', strtolower($site_name)) . '_' . Carbon::now()->format('YmdHis') . '.pdf'
        ];
    }

    public function exportSelected(Request $request)
    {
        // Get data for PDF
        $data = $this->getSelectedTransactionsForPdf($request);
        if ($data instanceof \Illuminate\Http\JsonResponse) {
            return $data;
        }
        if ($request->template === 'slip') {
            // For DH site, use the DH-specific Slip Store template
            if (session('site_id') == 'DH') {
                $template = 'units.reportDH_SlipStore';
            } else {
                $template = 'units.reportSlipStore';
            }
        } else {
            if (session('site_id') == 'PPA') {
                $template = $request->template === 'report' ? 'units.reportPPA_PO' : 'units.reportPPA_WO';
            } elseif (session('site_id') == 'IMK') {
                $template = $request->template === 'report' ? 'units.reportIMK_PO' : 'units.reportIMK_WO';
            } elseif (session('site_id') == 'UDU') {
                $template = $request->template === 'report' ? 'units.reportUDU_PO' : 'units.reportUDU_WO';
            } elseif (session('site_id') == 'DH') {
                $template = $request->template === 'report' ? 'units.reportDH_BAPP' : 'units.reportDH_SlipStore';
            }
        }

        // For PPA site, use DO number in filename if provided
        if (session('site_id') == 'PPA' && isset($data['transactions'][0])) {
            $doNumber = $data['transactions'][0]->do_number ?? '';

            // Extract the last number from DO number using regex
            if (preg_match('/(\d+)(?!.*\d)/', $doNumber, $matches)) {
                $doLastNumber = $matches[1];
                $docType = $request->template === 'report' ? 'DO' : 'SPB';
                $data['filename'] = $docType . '_' . $doLastNumber . '_' . Carbon::now()->format('YmdHis') . '.pdf';
            }
        }

        // Generate PDF
        $pdf = Pdf::loadView($template, $data);

        // Set paper size to landscape for better table display
        $pdf->setPaper('a4', 'landscape');

        // Download the PDF
        return $pdf->download($data['filename']);
    }
    public function previewSelected(Request $request)
    {
        // Get data for PDF
        $data = $this->getSelectedTransactionsForPdf($request);

        // Check if there was an error
        if ($data instanceof \Illuminate\Http\JsonResponse) {
            return $data;
        }

        // Determine which template to use
        if ($request->template === 'slip') {
            // For DH site, use the DH-specific Slip Store template
            if (session('site_id') == 'DH') {
                $template = 'units.reportDH_SlipStore';
            } else {
                $template = 'units.reportSlipStore';
            }
        } else {
            if (session('site_id') == 'PPA') {
                $template = $request->template === 'report' ? 'units.reportPPA_PO' : 'units.reportPPA_WO';
            } elseif (session('site_id') == 'IMK') {
                $template = $request->template === 'report' ? 'units.reportIMK_PO' : 'units.reportIMK_WO';
            } elseif (session('site_id') == 'UDU') {
                $template = $request->template === 'report' ? 'units.reportUDU_PO' : 'units.reportUDU_WO';
            } elseif (session('site_id') == 'DH') {
                $template = $request->template === 'report' ? 'units.reportDH_BAPP' : 'units.reportDH_SlipStore';
            }
        }

        // For PPA site, use DO number in filename if provided
        if (session('site_id') == 'PPA' && isset($data['transactions'][0])) {
            $doNumber = $data['transactions'][0]->do_number ?? '';

            // Extract the last number from DO number using regex
            if (preg_match('/(\d+)(?!.*\d)/', $doNumber, $matches)) {
                $doLastNumber = $matches[1];
                $docType = $request->template === 'report' ? 'DO' : 'SPB';
                $data['filename'] = $docType . '_' . $doLastNumber . '_' . Carbon::now()->format('YmdHis') . '.pdf';
            }
        }

        // Generate PDF
        $pdf = Pdf::loadView($template, $data);

        // Set paper size to landscape for better table display
        $pdf->setPaper('a4', 'landscape');

        // Stream the PDF (show in browser)
        return $pdf->stream($data['filename']);
    }

    /**
     * Delete a unit transaction and return stock to inventory
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        // Start a database transaction to ensure data integrity
        DB::beginTransaction();

        try {
            $transaction = UnitTransaction::with(['unit', 'parts.partInventory.part'])->findOrFail($id);

            // Check if transaction belongs to user's site
            if ($transaction->site_id !== session('site_id')) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }

            // Get unit and parts data for logging before deletion
            $unit = $transaction->unit;
            $partsForLog = [];

            // Return stock for all parts and delete site_out_stock records
            foreach ($transaction->parts as $part) {
                $partInventory = $part->partInventory;

                if ($partInventory) {
                    // Add to parts log
                    $partsForLog[] = [
                        'name' => $partInventory->part->part_name,
                        'quantity' => $part->quantity
                    ];

                    // Get the part code
                    $partCode = $partInventory->part_code;

                    // Check if this is a reference part
                    $referencedParts = $this->getReferencedParts($partCode, $part->quantity);

                    if ($referencedParts) {
                        // This is a reference part - return stock for each referenced part
                        foreach ($referencedParts as $refPart) {
                            // Find the inventory for the referenced part
                            $refPartInventory = PartInventory::lockForUpdate()
                                ->where('part_code', $refPart['code_part'])
                                ->where('site_id', session('site_id'))
                                ->first();

                            if (!$refPartInventory) {
                                Log::warning("Referenced part {$refPart['code_part']} not found in inventory for site " . session('site_id'));
                                continue;
                            }

                            // Increase the inventory quantity
                            $refPartInventory->stock_quantity += $refPart['quantity'];
                            $refPartInventory->save();

                            Log::info("Returned {$refPart['quantity']} of referenced part {$refPart['code_part']} to inventory during transaction deletion");
                        }
                    } else {
                        // Regular part - increase inventory directly
                        // Increase the inventory quantity with locking to prevent race conditions
                        $partInventory = PartInventory::lockForUpdate()->find($part->part_inventory_id);
                        if ($partInventory) {
                            $partInventory->stock_quantity += $part->quantity;
                            $partInventory->save();
                        } else {
                            Log::error('Part inventory not found for ID: ' . $part->part_inventory_id . ' during transaction deletion');
                        }
                    }

                    // Find and delete the corresponding site_out_stock record
                    $siteOutStock = SiteOutStock::where('unit_transaction_parts_id', $part->id)->first();
                    if ($siteOutStock) {
                        $siteOutStock->delete();
                    }

                    // Log the stock return
                    \App\Helpers\LogHelper::createLog(
                        'Pengembalian Stock Unit Transaction',
                        'User ' . session('name') . ' mengembalikan stock ' . $partInventory->part->part_name . ' sebanyak ' . $part->quantity . ' dari unit ' . $unit->unit_code . ' karena transaksi dihapus',
                        'Unit Transaction',
                        $request
                    );
                }
            }

            // Log the transaction deletion
            \App\Helpers\LogHelper::logUnitTransaction(
                'menghapus',
                $unit->unit_type,
                $transaction->status,
                $partsForLog,
                $request
            );

            // Delete all transaction parts first
            $transaction->parts()->delete();

            // Then delete the transaction
            $transaction->delete();

            // Commit the transaction
            DB::commit();

            return response()->json(['message' => 'Transaction deleted successfully']);
        } catch (\Exception $e) {
            // Roll back the transaction if something goes wrong
            DB::rollback();
            Log::error('Error deleting unit transaction: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete transaction: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Export transactions to Excel using template
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function exportExcel(Request $request)
    {
        // Only allow for PPA site
        if (session('site_id') !== 'PPA') {
            return response()->json(['error' => 'Excel export is only available for PPA site'], 403);
        }

        // Get data for Excel
        $data = $this->getTransactionsForPdf($request);

        // Check if there was an error
        if ($data instanceof \Illuminate\Http\JsonResponse) {
            return $data;
        }

        // Load the template
        $templatePath = public_path('assets/template.xlsx');
        $spreadsheet = IOFactory::load($templatePath);

        // Fill the DO sheet (first sheet)
        $doSheet = $spreadsheet->getSheet(0);
        $this->fillDOSheet($doSheet, $data);

        // Fill the PSB sheet (second sheet)
        $psbSheet = $spreadsheet->getSheet(1);
        $this->fillPSBSheet($psbSheet, $data);

        // Create a new Excel writer
        $writer = new Xlsx($spreadsheet);

        // Set the filename
        $filename = 'unit_transactions_ppa_' . Carbon::now()->format('YmdHis') . '.xlsx';

        // Set headers for download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        // Save to output
        $writer->save('php://output');
        exit;
    }
    public function exportSelectedExcel(Request $request)
    {
        if (session('site_id') !== 'PPA') {
            return response()->json(['error' => 'Excel export is only available for PPA site'], 403);
        }

        $data = $this->getSelectedTransactionsForPdf($request);

        if ($data instanceof \Illuminate\Http\JsonResponse) {
            return $data;
        }

        $templatePath = public_path('assets/TEMPLATESPBDANDO.xlsx');
        $spreadsheet = IOFactory::load($templatePath);

        // Dapatkan sheet template asli
        $originalDoSheet = $spreadsheet->getSheet(0);
        $originalSpbSheet = $spreadsheet->getSheet(1);

        // Kelompokkan transaksi berdasarkan unit_transaction_id
        $transactions = $data['transactions'];
        $groupedByTransactionId = [];
        foreach ($transactions as $transaction) {
            $transactionId = $transaction->id ?? 'UNKNOWN'; // Bisa diganti unit_transaction_id jika ada
            if (!isset($groupedByTransactionId[$transactionId])) {
                $groupedByTransactionId[$transactionId] = [];
            }
            $groupedByTransactionId[$transactionId][] = $transaction;
        }

        // Buat sheet untuk setiap transaksi
        foreach ($groupedByTransactionId as $transactionId => $unitTransactions) {
            // Siapkan data transaksi
            $unitData = $data;
            $unitData['transactions'] = $unitTransactions;

            // Ambil unit code dari salah satu transaksi (untuk penamaan sheet)
            $unitCode = $unitTransactions[0]->unit->unit_code ?? 'UNKNOWN';

            // Get DO number and extract the last number
            $doNumber = $unitTransactions[0]->do_number ?? '';
            $spbNumber = $unitTransactions[0]->noSPB ?? '';

            // Extract the last number from DO number using regex
            $doLastNumber = '';
            if (preg_match('/(\d+)(?!.*\d)/', $doNumber, $matches)) {
                $doLastNumber = $matches[1];
            }

            // Clone dan isi sheet DO
            $doSheet = clone $originalDoSheet;
            $doSheet->setTitle("SPB " . $doLastNumber);
            $spreadsheet->addSheet($doSheet);
            $this->fillPSBSheet($doSheet, $unitData);

            // Clone dan isi sheet SPB
            $spbSheet = clone $originalSpbSheet;
            $spbSheet->setTitle("DO " . $doLastNumber);
            $spreadsheet->addSheet($spbSheet);
            $this->fillDOSheet($spbSheet, $unitData);
        }

        // Hapus sheet template asli
        $spreadsheet->removeSheetByIndex(0); // Hapus sheet DO template
        $spreadsheet->removeSheetByIndex(0); // Hapus sheet SPB template

        $writer = new Xlsx($spreadsheet);
        $filename = 'selected_unit_transactions_ppa_' . Carbon::now()->format('YmdHis') . '.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    private function fillDOSheet($sheet, $data)
    {
        $transactions = $data['transactions'];
        $row = 14; // Starting row for data

        // Group transactions by WO, Date, and Unit
        $groupedData = [];
        foreach ($transactions as $transaction) {
            $doDate = $transaction->do_date ? Carbon::parse($transaction->do_date)->locale('id')->isoFormat('D MMMM Y') : '';
            $mrDate = $transaction->mr_date ? Carbon::parse($transaction->mr_date)->locale('id')->isoFormat('D MMMM Y') : '';
            $woKey = $transaction->wo_number ?? '';
            $dateKey = $mrDate;
            $unitKey = $transaction->unit->unit_code ?? '';
            $unitType = $transaction->unit->unit_type ?? '';
            $poNumber = $transaction->po_number ?? '';
            $doNumber = $transaction->do_number ?? '';
            $noSPB = $transaction->noSPB ?? '';
            $pekerjaan = $transaction->pekerjaan ?? '';
            $hmkm = $transaction->HMKM ?? '';
            $shift = $transaction->SHIFT ?? '';
            $lokasi = $transaction->LOKASI ?? '';
            $contact = $transaction->contact ?? '';
            $phone = $transaction->phone ?? '';
            $customer = $transaction->customer ?? 'PT PUTRA PERKASA ABADI';
            $sitework = $transaction->sitework ?? '';
            $remarks = $transaction->remarks ?? '';

            // Create a unique key for this combination
            $groupKey = $woKey . '|' . $dateKey . '|' . $unitKey;

            if (!isset($groupedData[$groupKey])) {
                $groupedData[$groupKey] = [
                    'wo' => $woKey,
                    'date' => $dateKey,
                    'mr_date' => $mrDate,
                    'do_date' => $doDate,
                    'unit' => $unitKey,
                    'unit_type' => $unitType,
                    'po_number' => $poNumber,
                    'do_number' => $doNumber,
                    'spb_number' => $noSPB,
                    'pekerjaan' => $pekerjaan,
                    'hmkm' => $hmkm,
                    'shift' => $shift,
                    'lokasi' => $lokasi,
                    'contact' => $contact,
                    'phone' => $phone,
                    'customer' => $customer,
                    'sitework' => $sitework,
                    'remarks' => $remarks,
                    'parts' => [],
                ];
            }

            foreach ($transaction->parts as $part) {
                $subtotal = $part->price * $part->quantity;

                $groupedData[$groupKey]['parts'][] = [
                    'part_code' => $part->partInventory->part->part_code,
                    'part_name' => $part->partInventory->part->part_name,
                    'quantity' => $part->quantity,
                    'eum' => $part->eum,
                    'price' => $part->price,
                    'subtotal' => $subtotal
                ];
            }
        }

        // Fill header information

        if (count($groupedData) > 0) {
            $firstGroup = reset($groupedData);

            // Set header information in the template
            $sheet->setCellValue('L8', ': ' . $firstGroup['contact']);
            $sheet->setCellValue('L9', ': ' . $firstGroup['phone']);
            $sheet->setCellValue('L10', ': ' . $firstGroup['do_date']);

            // NOMOR DO
            $sheet->mergeCells('L5:M6');
            $sheet->setCellValue('L5',$firstGroup['do_number']);

            $sheet->getStyle('L5')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle('L5')->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

            $sheet->getStyle('L5')->getFont()->setColor(new \PhpOffice\PhpSpreadsheet\Style\Color(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE));
            $sheet->getStyle('L5')->getFont()->setSize(16);
            $sheet->getStyle('L5')->getFont()->setSize(16)->setBold(true);



            // Atur alignment ke kiri
            $sheet->getStyle("L8:L10")->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        }

        $totalAmount = 0;
        $i = 1;
        // Fill data into the sheet
        foreach ($groupedData as $group) {
            $startRow = $row;
            $rowSpan = count($group['parts']);

            foreach ($group['parts'] as $part) {
                // Set data parts
                $sheet->setCellValue('B' . $row, $i++);
                $sheet->setCellValue('F' . $row, $part['part_code']);
                $sheet->setCellValue('G' . $row, $part['part_name']);
                $sheet->setCellValue('H' . $row, $part['quantity']);
                $sheet->setCellValue('I' . $row, $part['eum']);
                $sheet->setCellValue('J' . $row, 'Rp');
                $sheet->setCellValue('K' . $row, $part['price']);
                $sheet->setCellValue('L' . $row, 'Rp');
                $sheet->setCellValue('M' . $row, $part['subtotal']);
                // Format angka rupiah
                $sheet->getStyle("K{$row}")
                    ->getNumberFormat()
                    ->setFormatCode('#,##0.00');
                $sheet->getStyle("M{$row}")
                    ->getNumberFormat()
                    ->setFormatCode('#,##0.00');

                // Alignment
                $sheet->getStyle("J{$row}")
                    ->getAlignment()
                    ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
                $sheet->getStyle("K{$row}")
                    ->getAlignment()
                    ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
                $sheet->getStyle("L{$row}")
                    ->getAlignment()
                    ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
                $sheet->getStyle("M{$row}")
                    ->getAlignment()
                    ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);

                $totalAmount += $part['subtotal'];
                $row++;
            }

            $endRow = $row - 1;

            // Merge kolom C, D, E (wo, date, unit)
            $sheet->mergeCells("C{$startRow}:C{$endRow}");
            $sheet->mergeCells("D{$startRow}:D{$endRow}");
            $sheet->mergeCells("E{$startRow}:E{$endRow}");

            // Isi data hanya sekali di awal merge
            $wonumber = " " . $group['wo'];
            $sheet->setCellValue("C{$startRow}", $wonumber);
            $sheet->setCellValue("D{$startRow}", $group['date']);
            $sheet->setCellValue("E{$startRow}", $group['unit']);

            // Apply style alignment
            $alignmentStyle = [
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical'   => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                    'wrapText'   => true,
                ],
            ];

            $sheet->getStyle("C{$startRow}:C{$endRow}")->applyFromArray($alignmentStyle);
            $sheet->getStyle("D{$startRow}:D{$endRow}")->applyFromArray($alignmentStyle);
            $sheet->getStyle("E{$startRow}:E{$endRow}")->applyFromArray($alignmentStyle);
        }


        // TOTAL, PPN, GRANDTOTAL
        $sheet->setCellValue('L24', 'Rp');
        $sheet->setCellValue('L25', 'Rp');
        $sheet->setCellValue('L26', 'Rp');

        $sheet->setCellValue('M24', $totalAmount);
        $sheet->setCellValue('M25', $totalAmount * 0.11); // PPN 11%
        $sheet->setCellValue('M26', $totalAmount * 1.11); // Grand Total

        // Format dan align total
        $sheet->getStyle("M24:M26")->getNumberFormat()->setFormatCode('#,##0.00');
        $sheet->getStyle("M24:M26")->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle("L24:L26")->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
    }

    private function fillPSBSheet($sheet, $data)
    {
        $transactions = $data['transactions'];
        $row = 7; // Starting row for data

        // Group transactions by WO, Date, and Unit
        $groupedData = [];
        foreach ($transactions as $transaction) {
            $doDate = $transaction->do_date ? Carbon::parse($transaction->do_date)->locale('id')->isoFormat('D MMMM Y') : '';
            $mrDate = $transaction->mr_date ? Carbon::parse($transaction->mr_date)->locale('id')->isoFormat('D MMMM Y') : '';
            $woKey = $transaction->wo_number ?? '';
            $dateKey = $doDate;
            $unitKey = $transaction->unit->unit_code ?? '';
            $unitType = $transaction->unit->unit_type ?? '';
            $poNumber = $transaction->po_number ?? '';
            $doNumber = $transaction->do_number ?? '';
            $noSPB = $transaction->noSPB ?? '';
            $pekerjaan = $transaction->pekerjaan ?? '';
            $hmkm = $transaction->HMKM ?? '';
            $shift = $transaction->SHIFT ?? '';
            $lokasi = $transaction->LOKASI ?? '';
            $contact = $transaction->contact ?? '';
            $phone = $transaction->phone ?? '';
            $customer = $transaction->customer ?? 'PT PUTRA PERKASA ABADI';
            $sitework = $transaction->sitework ?? '';
            $remarks = $transaction->remarks ?? '';

            // Create a unique key for this combination
            $groupKey = $woKey . '|' . $dateKey . '|' . $unitKey;

            if (!isset($groupedData[$groupKey])) {
                $groupedData[$groupKey] = [
                    'wo' => $woKey,
                    'date' => $dateKey,
                    'mr_date' => $mrDate,
                    'unit' => $unitKey,
                    'unit_type' => $unitType,
                    'po_number' => $poNumber,
                    'do_number' => $doNumber,
                    'spb_number' => $noSPB,
                    'pekerjaan' => $pekerjaan,
                    'hmkm' => $hmkm,
                    'shift' => $shift,
                    'lokasi' => $lokasi,
                    'contact' => $contact,
                    'phone' => $phone,
                    'customer' => $customer,
                    'sitework' => $sitework,
                    'remarks' => $remarks,
                    'parts' => [],
                ];
            }

            foreach ($transaction->parts as $part) {
                $groupedData[$groupKey]['parts'][] = [
                    'part_code' => $part->partInventory->part->part_code,
                    'part_name' => $part->partInventory->part->part_name,
                    'quantity' => $part->quantity,
                    'eum' => $part->eum
                ];
            }
        }

        $row = 24;
        $i = 1;
        foreach ($groupedData as $group) {

            $startRow = $row; // awal baris untuk merge
            $rowSpan = count($group['parts']); // berapa baris yang akan dimerge
            // Terapkan gaya tengah dan wrap text
            $alignmentStyle = [
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical'   => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                    'wrapText'   => true,
                ],
            ];

            foreach ($group['parts'] as $index => $part) {
                // Set data in cells
                $sheet->setCellValue('C' . $row, $i++);
                $sheet->setCellValue('D' . $row, $part['part_code']);
                $sheet->setCellValue('E' . $row, $part['part_name']);
                $sheet->setCellValue('H' . $row, $part['quantity']);
                $sheet->setCellValue('I' . $row, $part['eum']);
                $sheet->getStyle("H{$row}")->applyFromArray($alignmentStyle);
                $sheet->getStyle("D{$row}")->applyFromArray($alignmentStyle);
                $sheet->getStyle("I{$row}")->applyFromArray($alignmentStyle);
                $row++;
            }

            // Merge dan isi unit dan remarks setelah loop parts
            $endRow = $row - 1;
            $sheet->mergeCells("J{$startRow}:J{$endRow}");
            $sheet->mergeCells("K{$startRow}:K{$endRow}");
            $sheet->setCellValue("J{$startRow}", $group['unit']);
            $sheet->setCellValue("K{$startRow}", $group['remarks']);

            $sheet->getStyle("J{$startRow}:J{$endRow}")->applyFromArray($alignmentStyle);
            $sheet->getStyle("K{$startRow}:K{$endRow}")->applyFromArray($alignmentStyle);
        }

        if (count($groupedData) > 0) {
            $firstGroup = reset($groupedData);
            $wonumber = " " . $firstGroup['wo'];
            $sheet->setCellValue('D21', ': ' . $firstGroup['mr_date']);
            $sheet->setCellValue('D19', ': ' . $firstGroup['spb_number']);
            $sheet->setCellValue('K19', ':' . $wonumber);
            $sheet->setCellValue('k20', ': ' . $firstGroup['do_number']);
        }
    }



    /**
     * Preview BAPP document
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function previewBAPP(Request $request)
    {
        try {
            // Validate the request
            $request->validate([
                'tanggalstart' => 'required|date',
                'tanggalend' => 'required|date',
                'noBA' => 'required|string',
                'unit_transaction_ids' => 'required|array',
                'unit_transaction_ids.*' => 'exists:unit_transactions,id',
            ]);

            // Get the transactions
            $transactions = UnitTransaction::with(['unit', 'parts.partInventory.part'])
                ->whereIn('id', $request->unit_transaction_ids)
                ->get();

            // Get site name
            $site_name = session('site_id');

            // Calculate total price
            $totalPrice = 0;
            foreach ($transactions as $transaction) {
                foreach ($transaction->parts as $part) {
                    $totalPrice += $part->price * $part->quantity;
                }
            }

            $formattedTotalPrice = 'Rp ' . number_format($totalPrice, 0, ',', '.');

            // Update tanggalstart, tanggalend, noBA, and do_number in each transaction
            foreach ($transactions as $transaction) {
                // Update the transaction fields
                $transaction->tanggalstart = $request->tanggalstart;
                $transaction->tanggalend = $request->tanggalend;
                $transaction->noBA = $request->noBA; // Store noBA in the noBA field
                $transaction->do_number = $request->noBA; // Update DO number to match BAPP number
                $transaction->save(); // Save the transaction changes

                // Also update the unit's DO number
                if ($transaction->unit) {
                    $transaction->unit->do_number = $request->noBA;
                    $transaction->unit->save();

                    // Log the DO number update for debugging
                    Log::info('Updated DO number for Unit ID: ' . $transaction->unit->id .
                             ', Unit Code: ' . $transaction->unit->unit_code .
                             ', New DO Number: ' . $request->noBA);
                }
                // Keep the original noireq value
            }

            // Prepare data for the PDF
            $data = [
                'transactions' => $transactions,
                'site_name' => $site_name,
                'totalPrice' => $formattedTotalPrice,
                'date' => Carbon::now()->format('d-m-Y H:i:s'),
                'title' => 'BAPP Document Preview',
                'filename' => 'bapp_preview_' . str_replace(' ', '_', strtolower($site_name)) . '_' . Carbon::now()->format('YmdHis') . '.pdf',
            ];

            // Generate PDF with landscape orientation
            $pdf = PDF::loadView('units.reportDH_BAPP', $data)
                ->setPaper('a4', 'landscape');

            // Stream the PDF
            return $pdf->stream('bapp_preview.pdf');
        } catch (\Exception $e) {
            Log::error('Error previewing BAPP document: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal melihat preview dokumen BAPP: ' . $e->getMessage()
            ], 500);
        }
    }


}