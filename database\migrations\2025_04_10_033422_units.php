<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('units', function (Blueprint $table) {
            $table->id();
            $table->string('site_id', 50);
            $table->foreign('site_id')
                ->references('site_id')
                ->on('sites')
                ->onUpdate('cascade');
            $table->string('unit_code', 50)->unique();
            $table->string('unit_type', 255);
            $table->integer('nopr');
            $table->integer('noqtn');
            $table->string('do_number')->nullable();
            $table->string('noSPB')->nullable();
            $table->string('perkerjaan')->nullable();
            $table->string('HMKM')->nullable();
            $table->string('SHIFT')->nullable();
            $table->string('LOKASI')->nullable();
            $table->timestamps();
        });

        Schema::create('unit_parts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('unit_id')->constrained('units')->onUpdate('cascade');
            $table->foreignId('part_inventory_id')
            ->constrained('part_inventories', 'part_inventory_id')
            ->onUpdate('cascade');
            $table->integer('quantity');
            $table->decimal('price', 15, 2)->default(0);
            $table->string('eum','5')->nullable()->default('AE');
            $table->timestamps();
        });

        Schema::create('unit_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('unit_id')->constrained('units')->onUpdate('cascade');
            $table->string('site_id', 50);
            $table->foreign('site_id')
                ->references('site_id')
                ->on('sites')
                ->onUpdate('cascade');
            $table->enum('status', ['On Process','MR','Pending','Ready WO','Ready PO','perbaikan','selesai'])->default('On Process');
            $table->text('remarks')->nullable();
            $table->string('nopr')->nullable();
            $table->string('noqtn')->nullable();
            $table->string('wo_number')->nullable();
            $table->string('po_number')->nullable();
            $table->string('do_number')->nullable();
            $table->string('perkerjaan')->nullable();
            $table->string('HMKM')->nullable();
            $table->string('SHIFT')->nullable();
            $table->string('LOKASI')->nullable();
            $table->string('contact')->nullable();
            $table->string('phone')->nullable();
            $table->string('noSPB')->nullable();
            $table->timestamps();
        });

        Schema::create('unit_transaction_parts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('unit_transaction_id')->constrained('unit_transactions')->onUpdate('cascade');
            $table->foreignId('part_inventory_id')
            ->constrained('part_inventories', 'part_inventory_id')
            ->onUpdate('cascade');
            $table->integer('quantity');
            $table->decimal('price', 15, 2);
            $table->string('eum','5')->nullable()->default('AE');
            $table->timestamps();
        });
    }
    public function down(): void
    {
        Schema::dropIfExists('unit_transaction_parts');
        Schema::dropIfExists('unit_transactions');
        Schema::dropIfExists('unit_parts');
        Schema::dropIfExists('units');
    }
};