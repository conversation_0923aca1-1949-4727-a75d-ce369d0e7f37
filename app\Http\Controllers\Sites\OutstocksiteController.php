<?php

namespace App\Http\Controllers\Sites;

use App\Http\Controllers\Controller;
use App\Models\LogAktivitas;
use App\Models\Notification;
use App\Models\Part;
use App\Models\PartInventory;
use App\Models\SiteOutStock;
use App\Models\Unit;
use App\Models\UnitTransaction;
use App\Models\UnitTransactionPart;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class OutstocksiteController extends Controller
{
    public function index()
    {
        $site_id = session('site_id');

        // Get today's date for default filtering
        $today = Carbon::today()->format('Y-m-d');

        // Query for paginated data to display
        $siteOutStocks = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
            ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
            ->leftJoin('unit_transaction_parts', 'site_out_stocks.unit_transaction_parts_id', '=', 'unit_transaction_parts.id')
            ->leftJoin('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
            ->leftJoin('units as transaction_units', 'unit_transactions.unit_id', '=', 'transaction_units.id')
            ->leftJoin('units', 'site_out_stocks.unit_id', '=', 'units.id')
            ->where('part_inventories.site_id', $site_id)
            ->whereDate('site_out_stocks.date_out', $today) // Default to today's date
            ->select(
                'site_out_stocks.*',
                'parts.part_name',
                'parts.part_code',
                DB::raw('COALESCE(units.unit_code, transaction_units.unit_code) as unit_code'),
                DB::raw('COALESCE(unit_transactions.HMKM, units.HMKM) as HMKM'),
                'unit_transactions.LOKASI',
                'units.id as unit_id'
            )
            ->orderBy('site_out_stocks.created_at', 'desc')
            ->paginate(15);

        // Calculate total quantity for all records for today
        $totalQuantity = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
            ->where('part_inventories.site_id', $site_id)
            ->whereDate('site_out_stocks.date_out', $today)
            ->sum('site_out_stocks.quantity');

        return view('sites.outstocksite', compact('site_id', 'siteOutStocks', 'totalQuantity'));
    }

    public function loadData(Request $request)
    {
        $site_id = session('site_id');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $searchTerm = $request->input('search');
        $siteOutStockId = $request->input('site_out_stock_id');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15); // Default to 15 items per page

        $query = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
            ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
            ->leftJoin('unit_transaction_parts', 'site_out_stocks.unit_transaction_parts_id', '=', 'unit_transaction_parts.id')
            ->leftJoin('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
            ->leftJoin('units as transaction_units', 'unit_transactions.unit_id', '=', 'transaction_units.id')
            ->leftJoin('units', 'site_out_stocks.unit_id', '=', 'units.id')
            ->where('part_inventories.site_id', $site_id)
            ->select(
                'site_out_stocks.*',
                'parts.part_name',
                'parts.part_code',
                DB::raw('COALESCE(units.unit_code, transaction_units.unit_code) as unit_code'),
                DB::raw('COALESCE(unit_transactions.HMKM, units.HMKM) as HMKM'),
                'unit_transactions.LOKASI',
                'units.id as unit_id'
            )
            ->orderBy('site_out_stocks.created_at', 'desc');

        // If a specific site_out_stock_id is provided, filter by it
        if ($siteOutStockId) {
            $query->where('site_out_stocks.site_out_stock_id', $siteOutStockId);
        }

        if ($startDate && $endDate) {
            $query->whereDate('site_out_stocks.date_out', '>=', $startDate)
                  ->whereDate('site_out_stocks.date_out', '<=', $endDate);
        }

        if ($searchTerm) {
            $query->where(function ($q) use ($searchTerm) {
                $q->where('parts.part_name', 'like', '%' . $searchTerm . '%')
                    ->orWhere('parts.part_code', 'like', '%' . $searchTerm . '%');
            });
        }

        // Clone the query for counting and summing
        $countQuery = clone $query;
        $sumQuery = clone $query;

        // Calculate total quantity for all matching records
        $totalQuantity = $sumQuery->sum('site_out_stocks.quantity');

        // Get total count for pagination
        $total = $countQuery->count();

        // Get paginated data
        $siteOutStocks = $query->skip(($page - 1) * $perPage)
                            ->take($perPage)
                            ->get();

        return response()->json([
            'data' => $siteOutStocks,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total,
            'total_quantity' => $totalQuantity
        ]);
    }

    public function create(Request $request)
    {
        $site_id = session('site_id');

        $validator = Validator::make($request->all(), [
            'part_code' => 'required|string|max:50',
            'quantity' => 'required|numeric|min:0.1',
            'price' => 'required|numeric|min:0',
            'date_out' => 'required|date',
            'status' => 'nullable|in:out stock,Proses Out,Done,Proses Return,Proses PO,Proses MR,Garansi',
            'notes' => 'nullable|string',
            'unit_id' => 'nullable|exists:units,id',
            'po_number' => 'nullable|string',
            'wo_number' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        DB::beginTransaction();
        try {
            // Use lockForUpdate to prevent race conditions
            $partInventory = PartInventory::where('part_code', $request->input('part_code'))
                ->where('site_id', $site_id)
                ->lockForUpdate()
                ->first();

            if (!$partInventory) {
                return response()->json(['error' => 'Silahkan Pilih Part Dengan Benar !!'], 400);
            }

            if ($partInventory->stock_quantity < $request->input('quantity')) {
                return response()->json(['error' => 'Stock tidak mencukupi. Stock sisa ' . $partInventory->stock_quantity], 400);
            }

            $partInventory->stock_quantity -= $request->input('quantity');
            if ($partInventory->stock_quantity < 0) {
                throw new \Exception('Stock quantity cannot be negative');
            }

            $partInventory->price = $request->input('price'); // Update the price in part inventory
            $partInventory->save();
            $siteOutStock = new SiteOutStock();
            $siteOutStock->part_inventory_id = $partInventory->part_inventory_id;
            $siteOutStock->site_id = $site_id;
            $siteOutStock->employee_id = session('employee_id');
            $siteOutStock->unit_id = $request->input('unit_id'); // Add unit_id
            $siteOutStock->quantity = $request->input('quantity');
            $siteOutStock->price = $request->input('price'); // Add price to site out stock
            $siteOutStock->date_out = $request->input('date_out');
            $siteOutStock->notes = $request->input('notes');
            $siteOutStock->status = $request->input('status', 'out stock'); // Use provided status or default to 'out stock'

            // If status is 'Done', require PO and WO numbers
            if ($request->input('status') === 'Done') {
                if (empty($request->input('po_number')) || empty($request->input('wo_number'))) {
                    return response()->json(['error' => 'PO Number dan WO Number harus diisi jika status Done.'], 422);
                }
                $siteOutStock->po_number = $request->input('po_number');
                $siteOutStock->wo_number = $request->input('wo_number');
            } else {
                $siteOutStock->po_number = $request->input('po_number');
                $siteOutStock->wo_number = $request->input('wo_number');
            }

            $siteOutStock->save();

            // If status is 'Done' and unit_id is set, create a Unit Transaction
            if ($request->input('status') === 'Done' && $request->input('unit_id')) {
                $this->createUnitTransactionFromOutStock($siteOutStock);
            }

            // Log Actifitas
            $part_code = $request->input('part_code');
            $partname = Part::where('part_code', $part_code)->first()->part_name;
            $quantity = $request->input('quantity');

            // Create log activity directly
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menambahkan Out Part',
                'description' => "User " . session('name') . " menambahkan " . $partname . " sebanyak " . $quantity . " dengan harga " . number_format($request->input('price'), 0, ',', '.'),
                'table' => "Out stock",
                'ip_address' => $request->ip(),
            ]);

            // notif
            $partInventory = PartInventory::with('part', 'site')
                ->findOrFail($partInventory->part_inventory_id);
            $max = $partInventory->max_stock;
            $min = $partInventory->min_stock;
            $stock = $partInventory->stock_quantity;
            $partname = $partInventory->part->part_name;
            $sitename = $partInventory->site->site_name;
            $site_id = $partInventory->site->site_id;
            $notificationTitle = '';
            $notificationMessage = '';

            if ($stock <= $min) {
                $notificationTitle = 'Part Not Ready';
                $notificationMessage = 'Part ' . $partname . ' Not Ready Pada Site : ' . $sitename;
            } elseif ($stock <= (($min + $max) / 2) || $stock < $max) {
                $notificationTitle = 'Medium Part';
                $notificationMessage = 'Part ' . $partname . ' Hampir Menyentuh Minimum Site : ' . $sitename;
            }
            if ($notificationTitle !== '' && $notificationMessage !== '') {
                Notification::create([
                    'title' => $notificationTitle,
                    'message' => $notificationMessage,
                    'type' => 'stock',
                    'routes' => 'inventori.card',
                    'site_id' => 'WHO',
                    'from_site' => session('site_id'),
                    'is_read' => false,
                ]);
            }
            DB::commit();
            return response()->json(['success' => 'Out-stock record created successfully.']);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Failed to create out-stock record: ' . $e->getMessage()], 500);
        }
    }

    public function delete(Request $request)
    {
        $outstockId = $request->input('site_out_stock_id');
        $siteOutStock = SiteOutStock::find($outstockId);
        if (!$siteOutStock) {
            return response()->json(['error' => 'Out-stock record not found.'], 404);
        }

        // Check if the record is linked to a unit transaction
        if ($siteOutStock->unit_transaction_parts_id) {
            return response()->json(['error' => 'Tidak dapat menghapus record yang terhubung dengan transaksi unit.'], 403);
        }

        // Check if the notes contain 'Part Digabung ke'
        if ($siteOutStock->notes && strpos($siteOutStock->notes, 'Part Digabung ke') !== false) {
            return response()->json(['error' => 'Tidak dapat menghapus part yang telah digabung.'], 403);
        }

        $part_inventory_id = $siteOutStock->part_inventory_id;

        DB::beginTransaction();
        try {
            // Use lockForUpdate to prevent race conditions
            $partInventory = PartInventory::where('part_inventory_id', $part_inventory_id)
                ->lockForUpdate()
                ->first();

            if (!$partInventory) {
                return response()->json(['error' => 'Part Inventory record not found.'], 404);
            }

            $partInventory->stock_quantity += $siteOutStock->quantity;
            $partInventory->save();
            // Log Actifitas
            $part_code = PartInventory::where('part_inventory_id', $part_inventory_id)->first()->part_code;
            $partname = Part::where('part_code', $part_code)->first()->part_name;
            $quantity = SiteOutStock::where('part_inventory_id', $part_inventory_id)->first()->quantity;
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menghapaus data Out Part',
                'decription' => "Admin " . session('name') . " Menghapus " . $partname . " sebanyak " . $quantity,
                'ip_address' => $request->ip(),
                'table' => "Out stock",
            ]);
            $siteOutStock->delete();
            DB::commit();
            // Log Actifitas

            return response()->json(['success' => 'Out-stock record deleted successfully.']);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Failed to delete out-stock record: ' . $e->getMessage()], 500);
        }
    }

    public function getParts(Request $request)
    {
        $site_id = session('site_id');
        $searchTerm = $request->input('search');
        $parts = Part::join('part_inventories', 'parts.part_code', '=', 'part_inventories.part_code')
            ->where('part_inventories.site_id', $site_id)
            ->where('part_inventories.stock_quantity','>', 0)
            ->where('parts.part_name', 'like', '%' . $searchTerm . '%')
            ->select('parts.part_code', 'parts.part_name','parts.part_type', 'part_inventories.stock_quantity', 'part_inventories.price')
            ->distinct()
            ->get();

        return response()->json($parts);
    }

    public function getUnits(Request $request)
    {
        $site_id = session('site_id');
        $searchTerm = $request->input('search');

        $units = Unit::where('site_id', $site_id)
            ->where(function($query) use ($searchTerm) {
                $query->where('unit_code', 'like', '%' . $searchTerm . '%')
                      ->orWhere('unit_type', 'like', '%' . $searchTerm . '%');
            })
            ->select('id', 'unit_code', 'unit_type', 'HMKM')
            ->limit(10)
            ->get();

        return response()->json($units);
    }

    /**
     * Create a Unit Transaction from a Site Out Stock record
     *
     * @param SiteOutStock $siteOutStock The site out stock record
     * @return UnitTransaction The created unit transaction
     */
    private function createUnitTransactionFromOutStock(SiteOutStock $siteOutStock)
    {
        // Get the unit
        $unit = Unit::findOrFail($siteOutStock->unit_id);

        // Check if there's already a transaction for this unit with the same PO and WO
        $existingTransaction = UnitTransaction::where('unit_id', $unit->id)
            ->where('po_number', $siteOutStock->po_number)
            ->where('wo_number', $siteOutStock->wo_number)
            ->first();

        if ($existingTransaction) {
            // If a transaction exists, add this part to it
            $transaction = $existingTransaction;

            // Check if this part is already in the transaction
            $existingPart = UnitTransactionPart::where('unit_transaction_id', $transaction->id)
                ->where('part_inventory_id', $siteOutStock->part_inventory_id)
                ->first();

            if (!$existingPart) {
                // Create a new transaction part
                $transactionPart = UnitTransactionPart::create([
                    'unit_transaction_id' => $transaction->id,
                    'part_inventory_id' => $siteOutStock->part_inventory_id,
                    'quantity' => $siteOutStock->quantity,
                    'price' => $siteOutStock->price,
                    'eum' => 'AE' // Default value
                ]);

                // Link the site out stock to the transaction part
                $siteOutStock->unit_transaction_parts_id = $transactionPart->id;
                $siteOutStock->save();
            } else {
                // If the part already exists, update the quantity and link the outstock to it
                $existingPart->quantity += $siteOutStock->quantity;
                $existingPart->save();

                // Link the site out stock to the existing transaction part
                $siteOutStock->unit_transaction_parts_id = $existingPart->id;
                $siteOutStock->save();
            }
        } else {
            // Create a new transaction
            $transaction = UnitTransaction::create([
                'unit_id' => $unit->id,
                'site_id' => session('site_id'),
                'status' => 'Pending', // Set initial status to 'Pending'
                'wo_number' => $siteOutStock->wo_number,
                'po_number' => $siteOutStock->po_number,
                'do_number' => $unit->do_number,
                'noSPB' => $unit->noSPB,
                'pekerjaan' => $unit->pekerjaan,
                'HMKM' => $unit->HMKM, // Make sure to get HMKM from the unit
                'SHIFT' => $unit->SHIFT,
                'LOKASI' => $unit->LOKASI
            ]);

            // Create a transaction part
            $transactionPart = UnitTransactionPart::create([
                'unit_transaction_id' => $transaction->id,
                'part_inventory_id' => $siteOutStock->part_inventory_id,
                'quantity' => $siteOutStock->quantity,
                'price' => $siteOutStock->price,
                'eum' => 'AE' // Default value
            ]);

            // Link the site out stock to the transaction part
            $siteOutStock->unit_transaction_parts_id = $transactionPart->id;
            $siteOutStock->save();
        }

        // Log the activity
        $partInventory = PartInventory::find($siteOutStock->part_inventory_id);
        $part = Part::where('part_code', $partInventory->part_code)->first();

        LogAktivitas::create([
            'site_id' => session('site_id'),
            'name' => session('name'),
            'action' => 'Membuat Unit Transaction dari Out Stock',
            'description' => "User " . session('name') . " membuat transaksi unit untuk " . $unit->unit_code . " dengan part " . $part->part_name,
            'table' => "Unit Transactions",
            'ip_address' => request()->ip(),
        ]);

        return $transaction;
    }

    public function updateStatus(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'required|in:out stock,Proses Out,Done,Proses Return,Proses PO,Proses MR,Garansi',
                'price' => 'required|numeric|min:0',
                'po_number' => 'nullable|string',
                'wo_number' => 'nullable|string',
                'unit_id' => 'nullable|exists:units,id',
                'notes' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json(['success' => false, 'message' => 'Status tidak valid.'], 422);
            }

            $siteOutStock = SiteOutStock::findOrFail($id);
            $oldStatus = $siteOutStock->status;
            $newStatus = $request->input('status');
            $newPrice = $request->input('price');
            $oldPrice = $siteOutStock->price;
            $oldNotes = $siteOutStock->notes;
            $newNotes = $request->input('notes');

            // If status is 'Done', require PO and WO numbers
            if ($newStatus === 'Done') {
                if (empty($request->input('po_number')) || empty($request->input('wo_number'))) {
                    return response()->json(['success' => false, 'message' => 'PO Number dan WO Number harus diisi jika status Done.'], 422);
                }
            }

            // Update the status and price
            $siteOutStock->status = $newStatus;
            $siteOutStock->price = $newPrice;
            $siteOutStock->po_number = $request->input('po_number');
            $siteOutStock->wo_number = $request->input('wo_number');
            $siteOutStock->notes = $request->input('notes');

            // Update unit_id if provided
            if ($request->has('unit_id')) {
                $siteOutStock->unit_id = $request->input('unit_id');
            }

            $siteOutStock->save();

            // Update the price in part inventory
            $partInventory = PartInventory::findOrFail($siteOutStock->part_inventory_id);
            $partInventory->price = $newPrice;
            $partInventory->save();

            // If status is 'Done' and unit_id is set, create a Unit Transaction
            if ($newStatus === 'Done' && $siteOutStock->unit_id) {
                $this->createUnitTransactionFromOutStock($siteOutStock);
            }

            // Log the activity
            $partInventory = PartInventory::find($siteOutStock->part_inventory_id);
            $part = Part::where('part_code', $partInventory->part_code)->first();

            // Create log activity directly
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Mengubah Status Out Part',
                'description' => "User " . session('name') . " mengubah status " . $part->part_name . " dari " . $oldStatus . " menjadi " . $newStatus .
                    " dan harga dari " . number_format($oldPrice, 0, ',', '.') . " menjadi " . number_format($newPrice, 0, ',', '.') .
                    ($oldNotes != $newNotes ? ", keterangan diperbarui" : ""),
                'table' => "Out stock",
                'ip_address' => $request->ip(),
            ]);

            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Status berhasil diperbarui.',
                'data' => $siteOutStock
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()], 500);
        }
    }
}
