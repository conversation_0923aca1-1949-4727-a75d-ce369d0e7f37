<?php

namespace App\Http\Controllers;

use App\Models\PartInventory;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class InventoryController extends Controller
{
    public function index()
    {
        // Ambil site pertama dari database
        $firstSite = Site::first();
        $startDate = Carbon::now()->startOfMonth()->toDateString();
        $endDate = Carbon::now()->toDateString();
        $sites = Site::all();

        return view('inventory.index', compact('firstSite', 'startDate', 'endDate', 'sites'));
    }

    public function show(string $id_site)
    {
        $firstSite = Site::find($id_site);
        if (!$firstSite) {
            abort(404, 'Site not found');
        }

        $startDate = Carbon::now()->startOfMonth()->toDateString();
        $endDate = Carbon::now()->toDateString();
        $sites = Site::all();

        return view('inventory.index', compact('firstSite', 'startDate', 'endDate', 'sites'));
    }
    public function getData(Request $request)
    {
        $siteId = $request->input('site_id');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $search = $request->input('search');

        $inventories = PartInventory::with('part', 'site')
            ->where('site_id', $siteId)
            ->when($search, function ($query) use ($search) {
                $query->whereHas('part', function ($q) use ($search) {
                    $q->where('part_name', 'like', '%' . $search . '%')
                        ->orWhere('part_code', 'like', '%' . $search . '%');
                });
            })
            ->get();

        $transformedData = $inventories->map(function ($inventory) use ($startDate, $endDate) {
            $totalIn = DB::table('site_in_stocks')
                ->where('part_inventory_id', $inventory->part_inventory_id)
                ->whereBetween('date_in', [$startDate, $endDate])
                ->sum('quantity');

            $totalOut = DB::query()
                ->select(DB::raw('COALESCE(SUM(quantity), 0) as total_quantity')) // Gunakan COALESCE
                ->from(
                    DB::table(DB::raw('( SELECT part_inventory_id, date_out, quantity, "site" AS source_type FROM site_out_stocks
                    WHERE part_inventory_id = :part_inventory_id1
                    AND date_out BETWEEN :start_date1 AND :end_date1
                UNION ALL
                SELECT part_inventory_id, date_out, quantity, "warehouse" AS source_type FROM warehouse_out_stocks
                    WHERE part_inventory_id = :part_inventory_id2
                    AND date_out BETWEEN :start_date2 AND :end_date2 ) as combined_out_stocks'))
                )
                ->setBindings([
                    'part_inventory_id1' => $inventory->part_inventory_id,
                    'start_date1' => $startDate,
                    'end_date1' => $endDate,
                    'part_inventory_id2' => $inventory->part_inventory_id,
                    'start_date2' => $startDate,
                    'end_date2' => $endDate,
                ])
                ->first()
                ->total_quantity;

            $averageStock = ($inventory->min_stock + $inventory->max_stock) / 2;
            $status = 'Ready';

            if ($inventory->max_stock == 0) {
                $status = 'Not Priority';
            } elseif ($inventory->stock_quantity <= $inventory->min_stock) {
                $status = 'Not Ready';
            } elseif ($inventory->stock_quantity < $averageStock) {
                $status = 'Medium';
            }

            return [
                'part_code' => $inventory->part->part_code,
                'part_name' => $inventory->part->part_name,
                'stock_quantity' => $inventory->stock_quantity,
                'min_stock' => $inventory->min_stock,
                'max_stock' => $inventory->max_stock,
                'total_in' => $totalIn,
                'total_out' => $totalOut,
                'status' => $status,
                'part_inventory_id' => $inventory->part_inventory_id,
            ];
        });

        $sortedData = $transformedData->sortBy(function ($item) {
            $order = [
                'Not Ready' => 1,
                'Medium' => 2,
                'Ready' => 3,
                'Not Priority' => 4,
            ];

            return $order[$item['status']] ?? 5;
        })->values()->all();


        return response()->json($sortedData);
    }
    public function getDetail(Request $request)
    {
        $partInventoryId = $request->input('part_inventory_id');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $inStocks = DB::table('site_in_stocks')
            ->join('users', 'site_in_stocks.employee_id', '=', 'users.employee_id')
            ->leftJoin('suppliers', 'site_in_stocks.supplier_id', '=', 'suppliers.supplier_id')
            ->where('part_inventory_id', $partInventoryId)
            ->whereBetween('date_in', [$startDate, $endDate])
            ->select(
                'site_in_stocks.date_in',
                'site_in_stocks.quantity',
                'users.name as employee_name',
                'suppliers.supplier_name',
                'site_in_stocks.notes'
            )
            ->get();


        // Ambil data dari site_out_stocks
        $siteOutStocks = DB::table('site_out_stocks')
            ->join('users', 'site_out_stocks.employee_id', '=', 'users.employee_id')
            ->where('part_inventory_id', $partInventoryId)
            ->whereBetween('date_out', [$startDate, $endDate])
            ->select(
                'site_out_stocks.date_out AS date_out', 
                'site_out_stocks.quantity',
                'site_out_stocks.status',
                'users.name as employee_name',
                'site_out_stocks.notes',
                DB::raw('"site" as source_type') 
            )
            ->get();

        // Ambil data dari warehouse_out_stocks
        $warehouseOutStocks = DB::table('warehouse_out_stocks')
            ->join('users', 'warehouse_out_stocks.employee_id', '=', 'users.employee_id')
            ->where('part_inventory_id', $partInventoryId)
            ->whereBetween('date_out', [$startDate, $endDate])
            ->select(
                'warehouse_out_stocks.date_out AS date_out', 
                'warehouse_out_stocks.quantity',
                DB::raw('"" as status'), 
                'users.name as employee_name',
                'warehouse_out_stocks.notes',
                DB::raw('"warehouse" as source_type') 
            )
            ->get();

        // Gabungkan data out stocks
        $outStocks = $siteOutStocks->concat($warehouseOutStocks);
        return response()->json(['in' => $inStocks, 'out' => $outStocks]);
    }

    // Ambil site mana saja yang ada not ready
    public function getNotReadySiteNames()
    {
        $notReadySites = PartInventory::join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
            ->join('sites', 'part_inventories.site_id', '=', 'sites.site_id')
            ->where('part_inventories.site_id', '!=', session('site_id'))
            ->where(function ($query) {
                $query->where('part_inventories.min_stock', '>', 0)
                    ->where('part_inventories.max_stock', '>', 0)
                    ->whereColumn('part_inventories.stock_quantity', '<=', 'part_inventories.min_stock');
            })
            ->distinct('part_inventories.site_id')
            ->pluck('sites.site_name')
            ->toArray();
        return response()->json(['sites' => $notReadySites]);
    }
}
