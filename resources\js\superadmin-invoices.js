/**
 * Superadmin Invoices JavaScript
 *
 * This file contains the JavaScript code for the superadmin invoices page.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    let currentPage = 1;
    let totalPages = 1;
    let perPage = 10;
    let totalInvoices = 0;
    let currentSearch = '';
    let currentStatus = '';
    let currentSiteId = '';
    let currentStartDate = '';
    let currentEndDate = '';

    // Get DOM elements
    const skeletonLoader = document.getElementById('skeleton-loader');
    const contentWrapper = document.querySelector('.content-wrapper');
    const searchInput = document.getElementById('search-input');
    const statusFilter = document.getElementById('status-filter');
    const siteFilter = document.getElementById('site-filter');
    const invoiceTableBody = document.getElementById('invoice-table-body');
    const paginationStart = document.getElementById('pagination-start');
    const paginationEnd = document.getElementById('pagination-end');
    const paginationTotal = document.getElementById('pagination-total');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    const monthPicker = document.getElementById('monthPicker');
    const prevMonthBtn = document.getElementById('prevMonthBtn');
    const nextMonthBtn = document.getElementById('nextMonthBtn');

    // Initialize month picker
    const currentDate = new Date();
    const currentMonth = currentDate.getFullYear() + '-' + String(currentDate.getMonth() + 1).padStart(2, '0');
    monthPicker.value = currentMonth;

    // Set up event listeners
    searchInput.addEventListener('input', debounce(handleSearch, 500));
    statusFilter.addEventListener('change', handleFilterChange);
    siteFilter.addEventListener('change', handleFilterChange);
    prevPageBtn.addEventListener('click', handlePrevPage);
    nextPageBtn.addEventListener('click', handleNextPage);
    monthPicker.addEventListener('change', handleMonthChange);
    prevMonthBtn.addEventListener('click', handlePrevMonth);
    nextMonthBtn.addEventListener('click', handleNextMonth);

    // Load initial data
    loadInvoicesData();

    /**
     * Load invoices data from the server
     */
    function loadInvoicesData() {
        // Show skeleton loader
        skeletonLoader.style.display = 'block';
        contentWrapper.style.display = 'none';

        // Get month value for date filtering
        const monthValue = monthPicker.value;
        if (monthValue) {
            const [year, month] = monthValue.split('-');
            currentStartDate = `${year}-${month}-01`;
            // Calculate last day of month
            const lastDay = new Date(year, month, 0).getDate();
            currentEndDate = `${year}-${month}-${lastDay}`;
        }

        // Build query parameters
        const params = new URLSearchParams({
            page: currentPage,
            per_page: perPage,
            search: currentSearch,
            status: currentStatus,
            site_id: currentSiteId
        });

        if (currentStartDate && currentEndDate) {
            params.append('start_date', currentStartDate);
            params.append('end_date', currentEndDate);
        }

        // Fetch data from the server
        fetch(`/superadmin/invoices-data?${params.toString()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Update site filter options if needed
                if (siteFilter.children.length <= 1) {
                    populateSiteFilter(data.sites);
                }

                // Update invoices table
                renderInvoicesTable(data.invoices);

                // Update pagination
                updatePagination(data.pagination);

                // Hide skeleton loader and show content
                skeletonLoader.style.display = 'none';
                contentWrapper.style.display = 'block';
            })
            .catch(error => {
                console.error('Error fetching invoices data:', error);
                // Hide skeleton loader and show content with error message
                skeletonLoader.style.display = 'none';
                contentWrapper.style.display = 'block';
                invoiceTableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center">Error loading data. Please try again.</td>
                    </tr>
                `;
            });
    }

    /**
     * Populate site filter dropdown with options
     */
    function populateSiteFilter(sites) {
        // Clear existing options except the first one (All Sites)
        while (siteFilter.children.length > 1) {
            siteFilter.removeChild(siteFilter.lastChild);
        }

        // Add non-site option
        const nonSiteOption = document.createElement('option');
        nonSiteOption.value = 'non-site';
        nonSiteOption.textContent = 'Non-Site';
        siteFilter.appendChild(nonSiteOption);

        // Add site options (exclude warehouse)
        sites.forEach(site => {
            if (site.site_id !== 'WHO') {
                const option = document.createElement('option');
                option.value = site.site_id;
                option.textContent = site.site_name;
                siteFilter.appendChild(option);
            }
        });
    }

    /**
     * Render invoices table with data
     */
    function renderInvoicesTable(invoices) {
        // Clear existing rows
        invoiceTableBody.innerHTML = '';

        if (invoices.length === 0) {
            // Show no data message
            invoiceTableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">Tidak ada data invoice yang ditemukan.</td>
                </tr>
            `;
            return;
        }

        // Add rows for each invoice
        invoices.forEach(invoice => {
            const row = document.createElement('tr');

            // Format currency values - only use total amount (no PPN)
            const formattedTotalAmount = formatCurrency(invoice.total_amount);

            // Format date
            const formattedDate = invoice.tanggal_invoice ? formatDate(invoice.tanggal_invoice) : '-';

            // Create status badge with blue theme colors
            let statusBadgeClass = 'bg-secondary';
            if (invoice.payment_status === 'Lunas' || invoice.invoice_status === 'Lunas') {
                statusBadgeClass = 'bg-success'; // Using success color from theme
            } else if (invoice.payment_status === 'Jatuh Tempo' || invoice.invoice_status === 'Jatuh Tempo') {
                statusBadgeClass = 'bg-danger'; // Using danger color from theme
            } else if (invoice.payment_status === 'Belum Lunas' || invoice.invoice_status === 'Belum Lunas') {
                statusBadgeClass = 'bg-warning text-dark'; // Using warning color from theme
            }

            // Check for document path (either signed_document_path or document_path)
            const documentPath = invoice.signed_document_path || invoice.document_path;

            // Set row content
            row.innerHTML = `
                <td>
                    <a href="#" class="invoice-link" data-id="${invoice.id}" ${documentPath ? `data-document="${documentPath}"` : ''}>${invoice.no_invoice || '-'}</a>
                </td>
                <td>${invoice.site ? invoice.site.site_name : '-'}</td>
                <td>${invoice.customer || '-'}</td>
                <td>${formattedDate}</td>
                <td>${formattedTotalAmount}</td>
                <td><span class="badge ${statusBadgeClass}">${invoice.invoice_status}</span></td>
            `;

            invoiceTableBody.appendChild(row);
        });

        // Add event listeners to invoice links - ALWAYS show document viewer, not details
        document.querySelectorAll('.invoice-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const invoiceId = this.getAttribute('data-id');
                const documentPath = this.getAttribute('data-document');

                // If document path is available, show document directly
                if (documentPath) {
                    showDocumentViewer(documentPath);
                } else {
                    // If no document is available, fetch invoice details to get document path
                    fetch(`/superadmin/invoices/${invoiceId}`)
                        .then(response => response.json())
                        .then(data => {
                            // Check for either signed_document_path or document_path
                            const documentPath = data.success ? (data.invoice.signed_document_path || data.invoice.document_path) : null;

                            if (documentPath) {
                                showDocumentViewer(documentPath);
                            } else {
                                // If still no document, show a message
                                alert('Tidak ada lampiran invoice yang tersedia.');
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching invoice details:', error);
                            alert('Gagal mengambil data lampiran invoice.');
                        });
                }
            });
        });
    }

    /**
     * Show invoice details in a modal - DEPRECATED, use showDocumentViewer instead
     * This function is kept for backward compatibility but should not be used anymore
     */
    function showInvoiceDetails(invoiceId) {
        // Instead of showing details, fetch the document path and show the document
        fetch(`/superadmin/invoices/${invoiceId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Check for either signed_document_path or document_path
                    const documentPath = data.invoice.signed_document_path || data.invoice.document_path;

                    if (documentPath) {
                        showDocumentViewer(documentPath);
                    } else {
                        alert('Tidak ada lampiran invoice yang tersedia.');
                    }
                } else {
                    alert('Gagal mengambil data lampiran invoice.');
                }
            })
            .catch(error => {
                console.error('Error fetching invoice details:', error);
                alert('Gagal mengambil data lampiran invoice.');
            });
    }

    /**
     * Render invoice details in the modal
     */
    function renderInvoiceDetails(invoice) {
        const detailContent = document.getElementById('invoice-detail-content');

        // Format currency and date values
        const formattedSubtotal = formatCurrency(invoice.subtotal);
        const formattedTotalAmount = formatCurrency(invoice.total_amount);
        const formattedDate = invoice.tanggal_invoice ? formatDate(invoice.tanggal_invoice) : '-';
        const formattedDueDate = invoice.due_date ? formatDate(invoice.due_date) : '-';

        // Create status badge with blue theme colors
        let statusBadgeClass = 'bg-secondary';
        if (invoice.payment_status === 'Lunas') {
            statusBadgeClass = 'bg-success'; // Using success color from theme
        } else if (invoice.payment_status === 'Jatuh Tempo') {
            statusBadgeClass = 'bg-danger'; // Using danger color from theme
        } else if (invoice.payment_status === 'Belum Lunas') {
            statusBadgeClass = 'bg-warning text-dark'; // Using warning color from theme
        }

        // Build HTML content
        let html = `
            <div class="row mb-3">
                <div class="col-md-6">
                    <h5>Informasi Invoice</h5>
                    <table class="table table-sm">
                        <tr>
                            <th>No. Invoice</th>
                            <td>${invoice.no_invoice || '-'}</td>
                        </tr>
                        <tr>
                            <th>Customer</th>
                            <td>${invoice.customer || '-'}</td>
                        </tr>
                        <tr>
                            <th>Lokasi</th>
                            <td>${invoice.location || '-'}</td>
                        </tr>
                        <tr>
                            <th>Tanggal Invoice</th>
                            <td>${formattedDate}</td>
                        </tr>
                        <tr>
                            <th>Jatuh Tempo</th>
                            <td>${formattedDueDate}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5>Informasi Pembayaran</h5>
                    <table class="table table-sm">
                        <tr>
                            <th>Subtotal</th>
                            <td>${formattedSubtotal}</td>
                        </tr>
                        <tr>
                            <th>Total</th>
                            <td class="fw-bold">${formattedTotalAmount}</td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td><span class="badge ${statusBadgeClass}">${invoice.payment_status}</span></td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        // Check for document path (either signed_document_path or document_path)
        const documentPath = invoice.signed_document_path || invoice.document_path;

        // Add document section if available
        if (documentPath) {
            html += `
                <div class="row mb-3">
                    <div class="col-12">
                        <h5>Dokumen Invoice</h5>
                        <button class="btn btn-sm btn-info view-document-btn" data-path="${documentPath}">
                            <i class="mdi mdi-file-pdf"></i> Lihat Dokumen
                        </button>
                    </div>
                </div>
            `;
        }

        // Add unit transactions section
        html += `
            <div class="row">
                <div class="col-12">
                    <h5>Unit Transactions</h5>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>Unit</th>
                                    <th>Site</th>
                                    <th>Status</th>
                                    <th>Tanggal</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        // Add rows for each unit transaction
        if (invoice.unit_transactions && invoice.unit_transactions.length > 0) {
            invoice.unit_transactions.forEach(transaction => {
                const unitCode = transaction.unit ? transaction.unit.unit_code : '-';
                const siteName = transaction.site ? transaction.site.site_name : '-';
                const formattedCreatedAt = formatDate(transaction.created_at);

                html += `
                    <tr>
                        <td>${unitCode}</td>
                        <td>${siteName}</td>
                        <td>${transaction.status}</td>
                        <td>${formattedCreatedAt}</td>
                    </tr>
                `;
            });
        } else {
            html += `
                <tr>
                    <td colspan="4" class="text-center">Tidak ada data transaksi unit.</td>
                </tr>
            `;
        }

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        // Set the HTML content
        detailContent.innerHTML = html;

        // Add event listener to view document button
        const viewDocumentBtn = detailContent.querySelector('.view-document-btn');
        if (viewDocumentBtn) {
            viewDocumentBtn.addEventListener('click', function() {
                const documentPath = this.getAttribute('data-path');
                showDocumentViewer(documentPath);
            });
        }
    }

    /**
     * Show document viewer in a modal
     */
    function showDocumentViewer(documentPath) {
        const viewerContent = document.getElementById('document-viewer-content');
        const documentUrl = `/assets/invoice_documents/${documentPath}`;

        // Set the document viewer content with 100% width
        viewerContent.innerHTML = `
            <iframe src="${documentUrl}" style="width: 100%; height: 100%; border: none; display: block;"></iframe>
        `;

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('document-viewer-modal'));
        modal.show();
    }

    /**
     * Update pagination information and controls
     */
    function updatePagination(pagination) {
        currentPage = pagination.current_page;
        totalPages = pagination.last_page;
        totalInvoices = pagination.total;

        // Update pagination text
        const start = (currentPage - 1) * perPage + 1;
        const end = Math.min(currentPage * perPage, totalInvoices);
        paginationStart.textContent = totalInvoices > 0 ? start : 0;
        paginationEnd.textContent = end;
        paginationTotal.textContent = totalInvoices;

        // Update pagination buttons
        prevPageBtn.disabled = currentPage <= 1;
        nextPageBtn.disabled = currentPage >= totalPages;
    }

    /**
     * Handle search input
     */
    function handleSearch() {
        currentSearch = searchInput.value.trim();
        currentPage = 1; // Reset to first page
        loadInvoicesData();
    }

    /**
     * Handle filter change
     */
    function handleFilterChange() {
        currentStatus = statusFilter.value;
        currentSiteId = siteFilter.value;
        currentPage = 1; // Reset to first page
        loadInvoicesData();
    }

    /**
     * Handle previous page button click
     */
    function handlePrevPage() {
        if (currentPage > 1) {
            currentPage--;
            loadInvoicesData();
        }
    }

    /**
     * Handle next page button click
     */
    function handleNextPage() {
        if (currentPage < totalPages) {
            currentPage++;
            loadInvoicesData();
        }
    }

    /**
     * Handle month picker change
     */
    function handleMonthChange() {
        currentPage = 1; // Reset to first page
        loadInvoicesData();
    }

    /**
     * Handle previous month button click
     */
    function handlePrevMonth() {
        const [year, month] = monthPicker.value.split('-');
        let prevMonth = parseInt(month) - 1;
        let prevYear = parseInt(year);

        if (prevMonth < 1) {
            prevMonth = 12;
            prevYear--;
        }

        monthPicker.value = `${prevYear}-${String(prevMonth).padStart(2, '0')}`;
        handleMonthChange();
    }

    /**
     * Handle next month button click
     */
    function handleNextMonth() {
        const [year, month] = monthPicker.value.split('-');
        let nextMonth = parseInt(month) + 1;
        let nextYear = parseInt(year);

        if (nextMonth > 12) {
            nextMonth = 1;
            nextYear++;
        }

        monthPicker.value = `${nextYear}-${String(nextMonth).padStart(2, '0')}`;
        handleMonthChange();
    }

    /**
     * Format currency value
     */
    function formatCurrency(value) {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(value);
    }

    /**
     * Format date value
     */
    function formatDate(dateString) {
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        return new Date(dateString).toLocaleDateString('id-ID', options);
    }

    /**
     * Debounce function to limit how often a function can be called
     */
    function debounce(func, delay) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    }
});
