/**
 * Superadmin Dashboard Best Parts Settings
 *
 * Handles the best parts settings functionality in the superadmin dashboard
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize category tabs functionality
    initCategoryTabs();

    // Initialize best parts settings
    initBestPartsSettings();

    // Listen for date range change events
    document.addEventListener('dateRangeChanged', function() {
        // Reload best parts data when date range changes
        showLoadingOverlay();
        loadBestPartsData();
    });

    // Listen for filter change events
    const divisionFilter = document.getElementById('division-filter');
    const siteFilter = document.getElementById('site-filter');
    const searchBtn = document.getElementById('searchBtn');

    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            // Reload best parts data when filters are applied
            showLoadingOverlay();
            loadBestPartsData();
        });
    }
});

/**
 * Initialize category tabs functionality
 */
function initCategoryTabs() {
    const categoryTabs = document.querySelectorAll('.category-tab');
    const categoryPanes = document.querySelectorAll('.category-pane');

    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Get the category from data attribute
            const category = this.getAttribute('data-category');

            // Remove active class from all tabs and add to clicked tab
            categoryTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // Hide all panes and show the selected one
            categoryPanes.forEach(pane => {
                pane.style.display = 'none';
            });
            document.getElementById(`category-${category}`).style.display = 'block';
        });
    });
}

/**
 * Initialize best parts settings functionality
 */
function initBestPartsSettings() {
    const saveButton = document.getElementById('saveBestPartsSettings');
    if (!saveButton) return;

    saveButton.addEventListener('click', function() {
        // Get form values
        const limit = document.getElementById('bestPartsLimit').value;
        const sortBy = document.querySelector('input[name="bestPartsSortBy"]:checked').value;

        // Validate limit
        if (limit < 1 || limit > 20) {
            alert('Jumlah part harus antara 1 dan 20');
            return;
        }

        // Show loading state
        saveButton.disabled = true;
        saveButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Memuat...';

        // Show loading overlay on the best parts section
        showLoadingOverlay();

        // Send AJAX request to save settings
        fetch('/superadmin/save-best-parts-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                limit: limit,
                sort_by: sortBy
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Lazy load the updated best parts data
            loadBestPartsData(data);
        })
        .catch(error => {
            console.error('Error saving settings:', error);
            alert('Gagal menyimpan pengaturan. Silakan coba lagi.');
            hideLoadingOverlay();
        })
        .finally(() => {
            // Reset button state
            saveButton.disabled = false;
            saveButton.innerHTML = '<i class="mdi mdi-refresh me-1"></i> Terapkan';
        });
    });
}

/**
 * Show loading overlay on the best parts section
 */
function showLoadingOverlay() {
    // Get the category content container
    const categoryContent = document.querySelector('.category-content');
    if (!categoryContent) return;

    // Create loading overlay if it doesn't exist
    let loadingOverlay = document.getElementById('bestPartsLoadingOverlay');
    if (!loadingOverlay) {
        loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'bestPartsLoadingOverlay';
        loadingOverlay.className = 'loading-overlay';
        loadingOverlay.innerHTML = `
            <div class="spinner-container">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Memuat data...</p>
            </div>
        `;

        // Add styles to the loading overlay
        loadingOverlay.style.position = 'absolute';
        loadingOverlay.style.top = '0';
        loadingOverlay.style.left = '0';
        loadingOverlay.style.width = '100%';
        loadingOverlay.style.height = '100%';
        loadingOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
        loadingOverlay.style.display = 'flex';
        loadingOverlay.style.justifyContent = 'center';
        loadingOverlay.style.alignItems = 'center';
        loadingOverlay.style.zIndex = '10';

        // Add the loading overlay to the category content container
        categoryContent.style.position = 'relative';
        categoryContent.appendChild(loadingOverlay);
    } else {
        loadingOverlay.style.display = 'flex';
    }
}

/**
 * Hide loading overlay
 */
function hideLoadingOverlay() {
    const loadingOverlay = document.getElementById('bestPartsLoadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

/**
 * Load best parts data via AJAX
 *
 * @param {Object} data - Response data from the save settings request
 */
function loadBestPartsData(data) {
    // Get the date range from the header form
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const divisionFilter = document.getElementById('division-filter');
    const siteFilter = document.getElementById('site-filter');

    // Get values
    const startDate = startDateInput ? startDateInput.value : '';
    const endDate = endDateInput ? endDateInput.value : '';
    const division = divisionFilter ? divisionFilter.value : '';
    const site = siteFilter ? siteFilter.value : '';

    // Fallback to month picker if date range is not provided
    const monthPicker = document.getElementById('month-picker');
    const selectedMonth = monthPicker ? monthPicker.value : '';

    // Build query parameters
    let queryParams = new URLSearchParams();

    // Add date range parameters if available
    if (startDate && endDate) {
        queryParams.append('start_date', startDate);
        queryParams.append('end_date', endDate);
    } else if (selectedMonth) {
        queryParams.append('month', selectedMonth);
    }

    // Add filters if available
    if (division) {
        queryParams.append('division', division);
    }

    if (site) {
        queryParams.append('site', site);
    }

    // Fetch the updated best parts data with all parameters
    fetch(`/superadmin/best-parts-data?${queryParams.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Update the best parts content
            updateBestPartsContent(data);

            // Hide loading overlay
            hideLoadingOverlay();
        })
        .catch(error => {
            console.error('Error loading best parts data:', error);
            alert('Gagal memuat data part terbaik. Silakan coba lagi.');
            hideLoadingOverlay();
        });
}

/**
 * Update the best parts content with new data
 *
 * @param {Object} data - Best parts data from the server
 */
function updateBestPartsContent(data) {
    // Update each category pane
    const categories = ['AC', 'TYRE', 'FABRIKASI'];

    categories.forEach(category => {
        const categoryPane = document.getElementById(`category-${category}`);
        if (!categoryPane) return;

        const categoryData = data[category];

        // Update total revenue
        const totalRevenueElement = categoryPane.querySelector('.text-success');
        if (totalRevenueElement && categoryData.total_revenue_with_ppn !== undefined) {
            totalRevenueElement.textContent = `Rp ${formatNumber(categoryData.total_revenue_with_ppn)}`;
        }

        // Update table content
        const tableBody = categoryPane.querySelector('tbody');
        if (!tableBody) return;

        if (categoryData.count > 0) {
            // Clear existing rows
            tableBody.innerHTML = '';

            // Add new rows
            categoryData.items.forEach(part => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${part.part_name}</td>
                    <td>${part.total_quantity}</td>
                    <td>Rp ${formatNumber(part.total_value)}</td>
                    <td>${part.contribution_percent}%</td>
                `;
                tableBody.appendChild(row);
            });

            // Show table, hide empty state
            const tableContainer = categoryPane.querySelector('.table-responsive');
            const emptyState = categoryPane.querySelector('.empty-state');

            if (tableContainer) tableContainer.style.display = 'block';
            if (emptyState) emptyState.style.display = 'none';
        } else {
            // Show empty state, hide table
            const tableContainer = categoryPane.querySelector('.table-responsive');
            const emptyState = categoryPane.querySelector('.empty-state');

            if (tableContainer) tableContainer.style.display = 'none';
            if (emptyState) emptyState.style.display = 'block';
        }
    });
}

/**
 * Format a number with thousands separator
 *
 * @param {number} number - The number to format
 * @returns {string} - Formatted number
 */
function formatNumber(number) {
    return new Intl.NumberFormat('id-ID').format(number);
}
