document.addEventListener('DOMContentLoaded', function() {
    // Set CSRF token for all AJAX requests
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Helper function to format currency
    function formatCurrency(amount) {
        return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
    }

    // Form elements
    const formContainer = document.getElementById('form-container');
    const customerForm = document.getElementById('customer-form');
    const customerId = document.getElementById('customer-id');
    const kode = document.getElementById('kode');
    const namaCustomer = document.getElementById('nama_customer');
    const alamat = document.getElementById('alamat');
    const totalPending = document.getElementById('total_pending');

    // Buttons
    const btnShowForm = document.getElementById('btn-show-form');
    const btnCloseForm = document.getElementById('btn-close-form');
    const btnResetForm = document.getElementById('btn-reset-form');

    // Event listeners for buttons
    btnShowForm.addEventListener('click', showForm);
    btnCloseForm.addEventListener('click', hideForm);
    btnResetForm.addEventListener('click', resetForm);

    // Format total_pending as currency when typing
    totalPending.addEventListener('input', function() {
        // Remove non-numeric characters
        let value = this.value.replace(/[^\d]/g, '');

        // Format with thousand separators
        if (value) {
            this.value = new Intl.NumberFormat('id-ID').format(parseInt(value));
        }
    });

    // Form submission
    customerForm.addEventListener('submit', handleFormSubmit);

    // Edit buttons
    document.querySelectorAll('.btn-edit').forEach(btn => {
        btn.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            editCustomer(id);
        });
    });

    // Delete buttons
    document.querySelectorAll('.btn-delete').forEach(btn => {
        btn.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            deleteCustomer(id);
        });
    });

    // Function to show the form
    function showForm() {
        formContainer.style.display = 'block';
        btnShowForm.style.display = 'none';
    }

    // Function to hide the form
    function hideForm() {
        formContainer.style.display = 'none';
        btnShowForm.style.display = 'inline-block';
        resetForm();
    }

    // Function to reset the form
    function resetForm() {
        customerForm.reset();
        customerId.value = '';
    }

    // Function to handle form submission
    function handleFormSubmit(e) {
        e.preventDefault();

        const id = customerId.value;

        // Format total_pending to remove thousand separators if present
        let pendingValue = totalPending.value.replace(/\./g, '');
        pendingValue = pendingValue.replace(/,/g, '.');

        const data = {
            kode: kode.value,
            nama_customer: namaCustomer.value,
            alamat: alamat.value,
            total_pending: pendingValue ? parseFloat(pendingValue) : 0
        };

        if (id) {
            // Update existing customer
            updateCustomer(id, data);
        } else {
            // Create new customer
            createCustomer(data);
        }
    }

    // Function to create a new customer
    function createCustomer(data) {
        fetch('/sales/customer', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil',
                    text: result.message,
                    showConfirmButton: false,
                    timer: 1500
                }).then(() => {
                    window.location.reload();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Gagal',
                    text: result.message
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Gagal',
                text: 'Terjadi kesalahan saat menyimpan data'
            });
        });
    }

    // Function to edit a customer
    function editCustomer(id) {
        fetch(`/sales/customer/${id}`)
        .then(response => response.json())
        .then(data => {
            customerId.value = data.id;
            kode.value = data.kode;
            namaCustomer.value = data.nama_customer;
            alamat.value = data.alamat || '';

            // Format total_pending as currency
            totalPending.value = formatCurrency(data.total_pending).replace('Rp ', '');

            showForm();
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Gagal',
                text: 'Terjadi kesalahan saat mengambil data customer'
            });
        });
    }

    // Function to update a customer
    function updateCustomer(id, data) {
        fetch(`/sales/customer/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil',
                    text: result.message,
                    showConfirmButton: false,
                    timer: 1500
                }).then(() => {
                    window.location.reload();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Gagal',
                    text: result.message
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Gagal',
                text: 'Terjadi kesalahan saat memperbarui data'
            });
        });
    }

    // Function to delete a customer
    function deleteCustomer(id) {
        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin menghapus customer ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/sales/customer/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil',
                            text: result.message,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            window.location.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal',
                            text: result.message
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Gagal',
                        text: 'Terjadi kesalahan saat menghapus data'
                    });
                });
            }
        });
    }
});
