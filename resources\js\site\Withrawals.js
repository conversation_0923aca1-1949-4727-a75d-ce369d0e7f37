import Swal from "sweetalert2";

document.addEventListener("DOMContentLoaded", function () {
    let allWithdrawals = [];
    const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

    // Set default date values to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById("start_date").value = today;
    document.getElementById("end_date").value = today;

    // Global variables for pagination
    let currentPage = 1;
    let completedCurrentPage = 1;
    const itemsPerPage = 5; // 5 items per page for first table
    const completedItemsPerPage = 10; // 10 items per page for completed table
    const withdrawalFunctions = {
        editWithdrawal: function (id) {
            const withdrawal = allWithdrawals.find(
                (w) => w.withdrawal_id === id
            );
            if (withdrawal) {
                document.getElementById("withdrawal_id_edit").value =
                    withdrawal.withdrawal_id;
                document.getElementById("part_code").value =
                    withdrawal.part_code;
                document.getElementById("from_site_id").value =
                    withdrawal.from_site_id;
                document.getElementById("requested_quantity").value =
                    withdrawal.requested_quantity;
                document.getElementById("withdrawal_reason").value =
                    withdrawal.withdrawal_reason || "";
                document.getElementById("notes").value = withdrawal.notes || "";
            }
        },

        deleteWithdrawal: function (id) {
            Swal.fire({
                title: "Hapus Permintaan Return?",
                text: "Anda yakin ingin menghapus permintaan return ini?",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "Ya, Hapus!",
                cancelButtonText: "Batal"
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/withdrawalsite/${id}`, {
                        method: "DELETE",
                        headers: {
                            "Content-Type": "application/json",
                            "X-CSRF-TOKEN": csrfToken,
                        },
                    })
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.success) {
                            Swal.fire({
                                icon: "success",
                                title: "Berhasil!",
                                text: data.message,
                            }).then(() => {
                                withdrawalFunctions.loadWithdrawals();
                            });
                        } else {
                            Swal.fire({
                                icon: "error",
                                title: "Gagal!",
                                text: data.message,
                            });
                        }
                    })
                    .catch((error) => {
                        Swal.fire({
                            icon: "error",
                            title: "Gagal!",
                            text: "Terjadi kesalahan saat menghapus permintaan.",
                        });
                        console.error("Error:", error);
                    });
                }
            });
        },

        createWithdrawal: function () {
            const form = document.getElementById("create-withdrawal-form");
            const formData = new FormData(form);
            const withdrawalId = document.getElementById("withdrawal_id_edit").value;
            const submitButton = document.getElementById("submit-button");

            // Validate form
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Show loading state
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Mengirim...';

            fetch("/withdrawalsite", {
                method: "POST",
                body: new URLSearchParams(formData),
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                    "X-CSRF-TOKEN": csrfToken,
                },
            })
            .then((response) => response.json())
            .then((data) => {
                // Reset button state
                submitButton.disabled = false;
                submitButton.textContent = "Kirim Permintaan";

                if (data.success) {
                    Swal.fire({
                        icon: "success",
                        title: "Berhasil!",
                        text: data.message,
                    }).then(() => {
                        // Reset form and reload data
                        form.reset();
                        document.getElementById("part-name-display").textContent = "";
                        withdrawalFunctions.loadWithdrawals();
                    });
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Gagal!",
                        text: data.message,
                    });
                }
            })
            .catch((error) => {
                // Reset button state
                submitButton.disabled = false;
                submitButton.textContent = "Kirim Permintaan";

                Swal.fire({
                    icon: "error",
                    title: "Gagal!",
                    text: "Terjadi kesalahan saat mengirim permintaan.",
                });
                console.error("Error:", error);
            });
        },

        editStatus: function (id) {
            const withdrawal = allWithdrawals.find(
                (w) => w.withdrawal_id === id
            );
            if (withdrawal) {
                // Show the form and container
                const form = document.getElementById("update-withdrawal-form");
                form.style.display = "block";
                document.getElementById("colhidden").style.display = "block";

                // Set form values
                form.querySelector("#withdrawal_id_status").value = withdrawal.withdrawal_id;
                form.querySelector("#approved_quantity").value = withdrawal.requested_quantity;
                form.querySelector("#status").value = withdrawal.status;

                // Scroll to the form
                document.getElementById("colhidden").scrollIntoView({ behavior: 'smooth' });
            }
        },

        updateWithdrawal: function () {
            const form = document.getElementById("update-withdrawal-form");
            const formData = new FormData(form);
            const withdrawalId = form.querySelector(
                "#withdrawal_id_status"
            ).value;

            // Make sure notes field is included and properly named
            const notes = form.querySelector("#notes").value;
            formData.append("notes", notes);

            // Log the form data to verify
            console.log("Form data:", Object.fromEntries(formData));

            fetch(`/withdrawalsite/status/${withdrawalId}`, {
                method: "PUT",
                body: new URLSearchParams(formData),
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                    "X-CSRF-TOKEN": csrfToken,
                },
            })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        Swal.fire({
                            icon: "success",
                            title: "Berhasil!",
                            text: "Status penarikan berhasil diperbarui!",
                        }).then(() => {
                            withdrawalFunctions.loadWithdrawals();
                            // Reset and hide the form
                            form.reset();
                            form.style.display = "none";
                            document.getElementById("colhidden").style.display = "none";

                            // Scroll back to the main content
                            document.getElementById("container").scrollIntoView({ behavior: 'smooth' });
                        });
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Gagal!",
                            text: data.message || "Gagal memperbarui status penarikan.",
                        });
                    }
                })
                .catch((error) => {
                    console.error("Error updating status:", error);
                    Swal.fire({
                        icon: "error",
                        title: "Gagal!",
                        text: "Gagal memperbarui status penarikan.",
                    });
                });
        },

        loadWithdrawals: function () {
            const status = document.getElementById("statusFilter").value;
            const startDate = document.getElementById("start_date").value;
            const endDate = document.getElementById("end_date").value;

            let url = `/withdrawalsite/get?status=${status}`;

            // Add date filters if provided
            if (startDate && endDate) {
                url += `&start_date=${startDate}&end_date=${endDate}`;
            }

            fetch(url, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": csrfToken,
                },
            })
                .then((response) => response.json())
                .then((data) => {
                    allWithdrawals = data.data;
                    populateTable(data.data);
                })
                .catch((error) => {
                    Swal.fire({
                        icon: "error",
                        title: "Gagal!",
                        text: "Gagal memuat daftar penarikan.",
                    });
                });
        },

        getPartSuggestions: function (query) {
            const suggestionsDiv = document.getElementById("part-suggestions");
            const suggestionsList = suggestionsDiv.querySelector("ul");

            if (query.length < 2) {
                suggestionsList.innerHTML = "";
                suggestionsDiv.style.display = "none";
                return;
            }

            fetch(`/parts/suggestionsite?query=${query}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": csrfToken,
                },
            })
                .then((response) => response.json())
                .then((data) => {
                    suggestionsList.innerHTML = "";
                    if (data.length > 0) {
                        data.forEach((part) => {
                            const li = document.createElement("li");
                            li.textContent = `${part.part_code} - ${part.part_name}`;
                            li.addEventListener("click", () => {
                                document.getElementById("part_code").value =
                                    part.part_code;
                                suggestionsList.innerHTML = "";
                                suggestionsDiv.style.display = "none";
                            });
                            suggestionsList.appendChild(li);
                        });
                        suggestionsDiv.style.display = "block";
                    } else {
                        suggestionsDiv.style.display = "none";
                    }
                })
                .catch((error) => console.error("Error:", error));
        },
    };

    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;

        li.appendChild(a);
        return li;
    }

    // Function to render pagination for withdrawals table
    function renderWithdrawalsPagination(data, totalPages) {
        const container = document.getElementById('withdrawals-pagination');
        container.innerHTML = '';

        if (totalPages > 1) {
            const pagination = document.createElement('ul');
            pagination.className = 'pagination pagination-rounded  justify-content-center';

            // Previous button
            if (currentPage > 1) {
                pagination.appendChild(createPaginationItem(currentPage - 1, '«'));
            }

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                pagination.appendChild(createPaginationItem(i, i, i === currentPage));
            }

            // Next button
            if (currentPage < totalPages) {
                pagination.appendChild(createPaginationItem(currentPage + 1, '»'));
            }

            container.appendChild(pagination);

            // Add event listeners to pagination links
            container.querySelectorAll('.page-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    currentPage = parseInt(this.dataset.page);
                    withdrawalFunctions.loadWithdrawals();
                });
            });
        }
    }

    // Function to render pagination for completed table
    function renderCompletedPagination(data, totalPages) {
        const container = document.getElementById('completed-pagination');
        container.innerHTML = '';

        if (totalPages > 1) {
            const pagination = document.createElement('ul');
            pagination.className = 'pagination pagination-rounded  justify-content-center';

            // Previous button
            if (completedCurrentPage > 1) {
                pagination.appendChild(createPaginationItem(completedCurrentPage - 1, '«'));
            }

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                pagination.appendChild(createPaginationItem(i, i, i === completedCurrentPage));
            }

            // Next button
            if (completedCurrentPage < totalPages) {
                pagination.appendChild(createPaginationItem(completedCurrentPage + 1, '»'));
            }

            container.appendChild(pagination);

            // Add event listeners to pagination links
            container.querySelectorAll('.page-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    completedCurrentPage = parseInt(this.dataset.page);
                    withdrawalFunctions.loadWithdrawals();
                });
            });
        }
    }

    function populateTable(data) {
        const tableBody = document.querySelector("#withdrawals-table tbody");
        const completedTableBody = document.querySelector("#completed-table tbody");
        tableBody.innerHTML = "";
        completedTableBody.innerHTML = "";

        // Separate active and completed withdrawals
        const activeWithdrawals = data.filter(item => item.status !== "Completed");
        const completedWithdrawals = data.filter(item => item.status === "Completed");

        // Calculate total pages for both tables
        const activePages = Math.ceil(activeWithdrawals.length / itemsPerPage);
        const completedPages = Math.ceil(completedWithdrawals.length / completedItemsPerPage);

        // Get current page items for active withdrawals
        const startActive = (currentPage - 1) * itemsPerPage;
        const endActive = startActive + itemsPerPage;
        const currentActiveItems = activeWithdrawals.slice(startActive, endActive);

        // Get current page items for completed withdrawals
        const startCompleted = (completedCurrentPage - 1) * completedItemsPerPage;
        const endCompleted = startCompleted + completedItemsPerPage;
        const currentCompletedItems = completedWithdrawals.slice(startCompleted, endCompleted);

        // Render active withdrawals
        if (currentActiveItems.length === 0) {
            const row = document.createElement("tr");
            row.innerHTML = `<td colspan="8" class="text-center">Tidak ada data</td>`;
            tableBody.appendChild(row);
        } else {
            let i = startActive + 1;
            currentActiveItems.forEach((item) => {
                const row = document.createElement("tr");
                let rowClass = "table-success";
                if (item.status === "Pending" || item.status === "In Transit") {
                    rowClass = "table-primary";
                } else if (item.status === "Rejected") {
                    rowClass = "table-danger";
                }
                row.classList.add(rowClass);

                // Only show delete button for In Transit status
                const deleteButton = item.status === 'In Transit' ?
                    `<button class="btn btn-sm btn-danger delete-withdrawal-button ms-1" data-id="${item.withdrawal_id}">Hapus</button>` : '';

                row.innerHTML = `
                    <td>${i++}</td>
                    <td>${item.part?.part_name || "N/A"}</td>
                    <td>${item.from_site_id}</td>
                    <td>${item.withdrawal_reason}</td>
                    <td>${item.requested_quantity}</td>
                    <td>${item.notes}</td>
                    <td>${item.status}</td>
                    <td>
                        <button class="btn btn-sm btn-primary edit-status-button" data-id="${item.withdrawal_id}">Edit Status</button>
                        ${deleteButton}
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Render completed withdrawals
        if (currentCompletedItems.length === 0) {
            const row = document.createElement("tr");
            row.innerHTML = `<td colspan="7" class="text-center">Tidak ada data</td>`;
            completedTableBody.appendChild(row);
        } else {
            let j = startCompleted + 1;
            currentCompletedItems.forEach((item) => {
                const row = document.createElement("tr");
                row.classList.add("table-success");

                row.innerHTML = `
                    <td>${j++}</td>
                    <td>${item.part?.part_name || "N/A"}</td>
                    <td>${item.from_site_id}</td>
                    <td>${item.withdrawal_reason}</td>
                    <td>${item.requested_quantity}</td>
                    <td>${item.notes}</td>
                    <td>${item.status}</td>
                `;
                completedTableBody.appendChild(row);
            });
        }

        // Render pagination
        renderWithdrawalsPagination(activeWithdrawals, activePages);
        renderCompletedPagination(completedWithdrawals, completedPages);
    }

    document
        .getElementById("btnclosed")
        .addEventListener("click", function (event) {
            // Hide the form column
            document.getElementById("colhidden").style.display = "none";

            // Reset the form
            if (document.getElementById("update-withdrawal-form")) {
                document.getElementById("update-withdrawal-form").reset();
                document.getElementById("update-withdrawal-form").style.display = "none";
            }
        });

    document
        .getElementById("withdrawals-table")
        .addEventListener("click", function (event) {
            if (event.target.classList.contains("edit-withdrawal-button")) {
                const id = parseInt(event.target.dataset.id);
                withdrawalFunctions.editWithdrawal(id);
            } else if (event.target.classList.contains("edit-status-button")) {
                const id = parseInt(event.target.dataset.id);
                withdrawalFunctions.editStatus(id);
            } else if (
                event.target.classList.contains("delete-withdrawal-button")
            ) {
                const id = parseInt(event.target.dataset.id);
                withdrawalFunctions.deleteWithdrawal(id);
            }
        });

    document
        .querySelector("#update-withdrawal-form .btn-success")
        .addEventListener("click", (e) => {
            e.preventDefault();
            withdrawalFunctions.updateWithdrawal();
        });

    document
        .getElementById("statusFilter")
        .addEventListener("change", function () {
            currentPage = 1; // Reset to first page when changing filter
            completedCurrentPage = 1;
            withdrawalFunctions.loadWithdrawals();
        });

    document
        .getElementById("siteFilter")
        .addEventListener("change", function () {
            currentPage = 1; // Reset to first page when changing filter
            completedCurrentPage = 1;
            withdrawalFunctions.loadWithdrawals();
        });

    document
        .getElementById("start_date")
        .addEventListener("change", function () {
            currentPage = 1; // Reset to first page when changing date filter
            completedCurrentPage = 1;
            withdrawalFunctions.loadWithdrawals();
        });

    document
        .getElementById("end_date")
        .addEventListener("change", function () {
            currentPage = 1; // Reset to first page when changing date filter
            completedCurrentPage = 1;
            withdrawalFunctions.loadWithdrawals();
        });

    // Part code input event listener for suggestions
    document
        .getElementById("part_code")
        .addEventListener("input", function () {
            withdrawalFunctions.getPartSuggestions(this.value);
        });

    // When a part is selected, display its name
    document.getElementById("part_code").addEventListener("change", function() {
        const partCode = this.value;
        if (partCode) {
            fetch(`/parts/detailsite?part_code=${partCode}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": csrfToken,
                },
            })
            .then((response) => response.json())
            .then((data) => {
                if (data && data.part_name) {
                    document.getElementById("part-name-display").textContent = data.part_name;
                }
            })
            .catch((error) => console.error("Error:", error));
        } else {
            document.getElementById("part-name-display").textContent = "";
        }
    });

    // Submit button event listener
    document
        .getElementById("submit-button")
        .addEventListener("click", function(e) {
            e.preventDefault();
            withdrawalFunctions.createWithdrawal();
        });

    withdrawalFunctions.loadWithdrawals();
});