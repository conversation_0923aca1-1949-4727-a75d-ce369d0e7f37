<?php

namespace App\Http\Controllers\Sites;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Site;
use App\Models\SiteInStock;
use App\Models\SiteOutStock;
use App\Models\PartInventory;
use Carbon\Carbon;

class DashboardsiteContoller extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'group_by' => 'nullable|in:day,week,month'
        ]);
        $groupBy = $request->input('group_by', 'week');
        $today = Carbon::now();
        $defaultEndDate = $today->toDateString();
        if ($today->day === 1) {
            $defaultStartDate = $today->subMonth()->startOfMonth()->toDateString();
        } else {
            $defaultStartDate = $today->startOfMonth()->toDateString();
        }

        $startDate = $request->input('start_date', $defaultStartDate);
        $endDate = $request->input('end_date', $defaultEndDate);
        $siteId = session('site_id');
        $site = Site::find($siteId);

        if (!$site) {
            return redirect()->back()->with('error', 'Site not found.');
        }

        $periods = [];
        $currentDate = Carbon::parse($startDate);
        $endDateObj = Carbon::parse($endDate);
        while ($currentDate <= $endDateObj) {
            switch ($groupBy) {
                case 'day':
                    $periodStart = $currentDate->copy()->startOfDay();
                    $periodEnd = $currentDate->copy()->endOfDay();
                    $label = $currentDate->format('d M Y');
                    $currentDate->addDay();
                    break;
                case 'week':
                    $periodStart = $currentDate->copy()->startOfWeek();
                    $periodEnd = $currentDate->copy()->endOfWeek();
                    $periodEnd = $periodEnd->min($endDateObj);
                    $label = $periodStart->format('d M') . ' - ' . $periodEnd->format('d M');
                    $currentDate = $periodEnd->copy()->addDay();
                    break;
                case 'month':
                    $periodStart = $currentDate->copy()->startOfMonth();
                    $periodEnd = $currentDate->copy()->endOfMonth();
                    $periodEnd = $periodEnd->min($endDateObj);
                    $label = $periodStart->format('M Y');
                    $currentDate = $periodEnd->copy()->addDay();
                    break;
            }

            $periods[] = [
                'start' => $periodStart,
                'end' => $periodEnd,
                'label' => $label
            ];
        }

        $inStockData = [];
        $outStockData = [];

        // Inisialisasi variabel total
        $totalInStock = 0;
        $totalOutStock = 0;

        foreach ($periods as $period) {
            // In Stock Calculation
            $inStock = SiteInStock::join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                ->where('part_inventories.site_id', $site->site_id)
                ->whereBetween('date_in', [$period['start'], $period['end']])
                ->sum('site_in_stocks.quantity');
            $totalInStock += $inStock;

            // Out Stock Calculation
            $outStock = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                ->where('part_inventories.site_id', $site->site_id)
                ->whereBetween('date_out', [$period['start'], $period['end']])
                ->sum('site_out_stocks.quantity');

             $totalOutStock += $outStock;

            $inStockData[] = round($inStock, 2);
            $outStockData[] = round($outStock, 2);
        }

        $chartData = [
            'site_name' => $site->site_name,
            'labels' => array_column($periods, 'label'),
            'in_stock_data' => $inStockData,
            'out_stock_data' => $outStockData
        ];

        $inventoryData = $this->getInventoryStatusData(session('site_id'));

        // Round total values to 2 decimal places
        $totalInStock = round($totalInStock, 2);
        $totalOutStock = round($totalOutStock, 2);

        // Calculate total unit transactions based on MR date
        $unitTransactions = \App\Models\UnitTransaction::with(['parts'])
            ->where('site_id', session('site_id'))
            ->whereBetween('mr_date', [
                $startDate . ' 00:00:00',
                $endDate . ' 23:59:59'
            ])
            ->get();

        // Calculate total based on formula: (total all parts price) * 0.11 + total price
        $totalUnitTransactions = 0;
        foreach ($unitTransactions as $transaction) {
            // Calculate total price for all parts in this transaction
            $transactionTotal = 0;
            foreach ($transaction->parts as $part) {
                // Sum up all part prices * quantities
                $transactionTotal += $part->price * $part->quantity;
            }

            // Apply formula: (total price * 0.11) + total price
            $totalUnitTransactions += ($transactionTotal * 0.11) + $transactionTotal;
        }

        // Format the total with Rupiah
        $formattedTotalUnitTransactions = 'Rp ' . number_format($totalUnitTransactions, 0, ',', '.');

        // Menambahkan total in dan out stock ke compact
        return view('sites.dashboard', compact(
            'chartData',
            'startDate',
            'endDate',
            'groupBy',
            'inventoryData',
            'totalInStock', // Pass total in stock
            'totalOutStock', // Pass total out stock
            'unitTransactions', // Pass unit transactions
            'formattedTotalUnitTransactions' // Pass formatted total unit transactions
        ));
    }

    private function getInventoryStatusData($siteId)
    {
        $site = Site::find($siteId);
        $inventoryData = [];
        if ($site) {
            $inventories = PartInventory::where('site_id', $siteId)->get();
            $totalInventories = $inventories->count();
            $notReadyCount = 0;
            $mediumCount = 0;
            $notReadyParts = [];
            $mediumParts = [];

            foreach ($inventories as $inventory) {
                $averageStock = ($inventory->min_stock + $inventory->max_stock) / 2;
                if ($inventory->stock_quantity <= $inventory->min_stock) {
                    $status = 'danger';
                    $notReadyCount++;
                    $notReadyParts[] = [
                        'part_name' => $inventory->part->part_name,
                        'min_stock' => $inventory->min_stock,
                        'max_stock' => $inventory->max_stock,
                        'stock_quantity' => $inventory->stock_quantity,
                        'status' => $status,
                    ];
                } elseif ($inventory->stock_quantity < $averageStock) {
                    $status = 'warning';
                    $mediumParts[] = [
                        'part_name' => $inventory->part->part_name,
                        'min_stock' => $inventory->min_stock,
                        'max_stock' => $inventory->max_stock,
                        'stock_quantity' => $inventory->stock_quantity,
                        'status' => $status,
                    ];
                } else {
                    $status = 'ok';
                }
            }

            $readyCount = $totalInventories - $notReadyCount - $mediumCount;
            $totalNotOk = $notReadyCount + $mediumCount;
            $readyPercentage = ($totalInventories > 0) ? ($readyCount / $totalInventories) * 100 : 0;
            $notReadyPercentage = ($totalInventories > 0) ? ($totalNotOk / $totalInventories) * 100 : 0;

            $inventoryData[] = [
                'site_name' => $site->site_name,
                'ready_percentage' => round($readyPercentage, 2),
                'not_ready_percentage' => round($notReadyPercentage, 2),
                'not_ready_count' => $notReadyCount,
                'medium_count' => $mediumCount,
                'not_ready_parts' => $notReadyParts,
                'medium_parts' => $mediumParts,
            ];
        }

        return $inventoryData;
    }

       public function getStockTotals(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'group_by' => 'nullable|in:day,week,month'
        ]);

        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $groupBy = $request->input('group_by', 'week');
        $siteId = session('site_id');
        $site = Site::find($siteId);

        if (!$site) {
            return response()->json(['error' => 'Site not found.'], 404);
        }

        // In Stock Calculation
        $totalInStock = SiteInStock::join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
            ->where('part_inventories.site_id', $site->site_id)
            ->whereBetween('date_in', [$startDate, $endDate])
            ->sum('site_in_stocks.quantity');

        // Out Stock Calculation
        $totalOutStock = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
            ->where('part_inventories.site_id', $site->site_id)
            ->whereBetween('date_out', [$startDate, $endDate])
            ->sum('site_out_stocks.quantity');

        // Calculate total unit transactions based on MR date
        $unitTransactions = \App\Models\UnitTransaction::with(['parts'])
            ->where('site_id', $siteId)
            ->whereBetween('mr_date', [
                $startDate . ' 00:00:00',
                $endDate . ' 23:59:59'
            ])
            ->get();

        // Calculate total based on formula: (total all parts price) * 0.11 + total price
        $totalUnitTransactions = 0;
        foreach ($unitTransactions as $transaction) {
            // Calculate total price for all parts in this transaction
            $transactionTotal = 0;
            foreach ($transaction->parts as $part) {
                // Sum up all part prices * quantities
                $transactionTotal += $part->price * $part->quantity;
            }

            // Apply formula: (total price * 0.11) + total price
            $totalUnitTransactions += ($transactionTotal * 0.11) + $transactionTotal;
        }

        // Format the total with Rupiah
        $formattedTotalUnitTransactions = 'Rp ' . number_format($totalUnitTransactions, 0, ',', '.');

        // Generate chart data based on group_by
        $periods = [];
        $inStockData = [];
        $outStockData = [];

        $startDateTime = \Carbon\Carbon::parse($startDate);
        $endDateTime = \Carbon\Carbon::parse($endDate);

        // Generate periods based on groupBy
        if ($groupBy === 'day') {
            for ($date = $startDateTime->copy(); $date->lte($endDateTime); $date->addDay()) {
                $periods[] = [
                    'start' => $date->copy()->startOfDay()->toDateTimeString(),
                    'end' => $date->copy()->endOfDay()->toDateTimeString(),
                    'label' => $date->format('d M')
                ];
            }
        } elseif ($groupBy === 'week') {
            $startWeek = $startDateTime->copy()->startOfWeek();
            for ($date = $startWeek; $date->lte($endDateTime); $date->addWeek()) {
                $weekEnd = $date->copy()->endOfWeek();
                if ($weekEnd->gt($endDateTime)) {
                    $weekEnd = $endDateTime->copy();
                }
                $periods[] = [
                    'start' => $date->copy()->toDateTimeString(),
                    'end' => $weekEnd->copy()->toDateTimeString(),
                    'label' => 'Week ' . $date->weekOfYear
                ];
            }
        } else { // month
            $startMonth = $startDateTime->copy()->startOfMonth();
            for ($date = $startMonth; $date->lte($endDateTime); $date->addMonth()) {
                $monthEnd = $date->copy()->endOfMonth();
                if ($monthEnd->gt($endDateTime)) {
                    $monthEnd = $endDateTime->copy();
                }
                $periods[] = [
                    'start' => $date->copy()->toDateTimeString(),
                    'end' => $monthEnd->copy()->toDateTimeString(),
                    'label' => $date->format('M Y')
                ];
            }
        }

        // Calculate data for each period
        foreach ($periods as $period) {
            // In Stock Calculation
            $inStock = SiteInStock::join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                ->where('part_inventories.site_id', $site->site_id)
                ->whereBetween('date_in', [$period['start'], $period['end']])
                ->sum('site_in_stocks.quantity');

            // Out Stock Calculation
            $outStock = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                ->where('part_inventories.site_id', $site->site_id)
                ->whereBetween('date_out', [$period['start'], $period['end']])
                ->sum('site_out_stocks.quantity');

            $inStockData[] = round($inStock, 2);
            $outStockData[] = round($outStock, 2);
        }

        $chartData = [
            'site_name' => $site->site_name,
            'labels' => array_column($periods, 'label'),
            'in_stock_data' => $inStockData,
            'out_stock_data' => $outStockData
        ];

        // Get inventory status data
        $inventoryData = $this->getInventoryStatusData(session('site_id'));

        return response()->json([
            'totalInStock' => round($totalInStock, 2),
            'totalOutStock' => round($totalOutStock, 2),
            'totalUnitTransactions' => $totalUnitTransactions,
            'formattedTotalUnitTransactions' => $formattedTotalUnitTransactions,
            'dateRange' => [
                'start' => \Carbon\Carbon::parse($startDate)->format('d-m-Y'),
                'end' => \Carbon\Carbon::parse($endDate)->format('d-m-Y')
            ],
            'chartData' => $chartData,
            'inventoryData' => $inventoryData
        ]);
    }
}